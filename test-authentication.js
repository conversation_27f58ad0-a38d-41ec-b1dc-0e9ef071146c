// Test script to verify authentication fixes
// Run this in browser console on your site

async function testAuthentication() {
    console.log('🧪 Testing Authentication System...');
    
    // Test 1: Check if Supabase is connected
    try {
        const { data, error } = await supabase.auth.getSession();
        if (error) {
            console.error('❌ Supabase connection failed:', error);
            return;
        }
        console.log('✅ Supabase connection successful');
    } catch (e) {
        console.error('❌ Supabase not available:', e);
        return;
    }
    
    // Test 2: Check RLS policies
    try {
        const { data, error } = await supabase.from('agencies').select('count').limit(1);
        if (error && error.message.includes('row-level security')) {
            console.error('❌ RLS policies still have issues:', error);
        } else {
            console.log('✅ RLS policies working correctly');
        }
    } catch (e) {
        console.error('❌ Error checking RLS policies:', e);
    }
    
    // Test 3: Check if auth pages load
    const authElements = {
        loginForm: document.querySelector('form[data-testid="login-form"]') || document.querySelector('input[type="email"]'),
        signupForm: document.querySelector('form[data-testid="signup-form"]') || document.querySelector('input[placeholder*="email"]'),
        agencyOption: document.querySelector('input[value="agency"]') || document.querySelector('label[for*="agency"]')
    };
    
    if (authElements.loginForm) {
        console.log('✅ Login form found');
    } else {
        console.log('⚠️ Login form not found on current page');
    }
    
    if (authElements.agencyOption) {
        console.log('✅ Agency registration option found');
    } else {
        console.log('⚠️ Agency registration option not found on current page');
    }
    
    // Test 4: Check car availability components
    const carComponents = {
        availabilityCalendar: document.querySelector('[class*="react-calendar"]'),
        agencyOtherCars: document.querySelector('[data-testid="agency-other-cars"]') || document.querySelector('h3:contains("More Cars")')
    };
    
    if (window.location.pathname.includes('/car-details/')) {
        if (carComponents.availabilityCalendar) {
            console.log('✅ Car availability calendar found');
        } else {
            console.log('⚠️ Car availability calendar not found');
        }
    }
    
    console.log('🏁 Authentication test completed');
}

// Test car availability specifically
async function testCarAvailability(carId) {
    if (!carId) {
        console.log('Please provide a car ID: testCarAvailability("your-car-id")');
        return;
    }
    
    console.log(`🚗 Testing car availability for car: ${carId}`);
    
    try {
        const { data, error } = await supabase
            .from('bookings')
            .select('id, start_date, end_date, status')
            .eq('car_id', carId)
            .in('status', ['confirmed', 'active']);
            
        if (error) {
            console.error('❌ Error fetching bookings:', error);
            return;
        }
        
        console.log(`✅ Found ${data.length} active bookings for this car`);
        console.log('Bookings:', data);
        
        // Test date generation
        const bookedDates = [];
        data.forEach(booking => {
            const startDate = new Date(booking.start_date);
            const endDate = new Date(booking.end_date);
            
            const currentDate = new Date(startDate);
            while (currentDate <= endDate) {
                bookedDates.push(new Date(currentDate));
                currentDate.setDate(currentDate.getDate() + 1);
            }
        });
        
        console.log(`✅ Generated ${bookedDates.length} booked dates`);
        
    } catch (e) {
        console.error('❌ Error testing car availability:', e);
    }
}

// Test agency cars
async function testAgencyCars(agencyId) {
    if (!agencyId) {
        console.log('Please provide an agency ID: testAgencyCars("your-agency-id")');
        return;
    }
    
    console.log(`🏢 Testing agency cars for agency: ${agencyId}`);
    
    try {
        const { data, error } = await supabase
            .from('cars')
            .select('id, make, model, daily_rate, status')
            .eq('agency_id', agencyId)
            .eq('status', 'available');
            
        if (error) {
            console.error('❌ Error fetching agency cars:', error);
            return;
        }
        
        console.log(`✅ Found ${data.length} available cars for this agency`);
        console.log('Cars:', data);
        
    } catch (e) {
        console.error('❌ Error testing agency cars:', e);
    }
}

// Run basic test
testAuthentication();

console.log(`
🔧 Authentication Test Script Loaded!

Available test functions:
- testAuthentication() - Run basic authentication tests
- testCarAvailability("car-id") - Test car booking calendar
- testAgencyCars("agency-id") - Test agency cars display

Example usage:
testCarAvailability("your-car-id-here");
testAgencyCars("your-agency-id-here");
`);
