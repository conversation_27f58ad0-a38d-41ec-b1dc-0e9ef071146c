// Test script to verify agency login and dashboard access
// Run with: node test-agency-login.js

// Load environment variables from .env.local
require('dotenv').config({ path: '.env.local' })

const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://sagtwjbwgfgvzulnsmhc.supabase.co'
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseKey) {
    console.error('❌ NEXT_PUBLIC_SUPABASE_ANON_KEY not found')
    process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseKey)

async function testAgencyLogin() {
    console.log('🏢 Testing agency login...')

    try {
        // Use an existing agency user or create a new one
        const agencyEmail = '<EMAIL>' // This should be an existing agency user
        const agencyPassword = 'TestPassword123!' // You'll need to know the actual password

        console.log(`📝 Attempting to sign in as agency: ${agencyEmail}`)

        const { data, error } = await supabase.auth.signInWithPassword({
            email: agencyEmail,
            password: agencyPassword
        })

        if (error) {
            console.error('❌ Agency login failed:', error.message)
            console.log('\n💡 If you don\'t know the password, you can:')
            console.log('1. Reset the password in Supabase dashboard')
            console.log('2. Or create a new agency user with a known password')
            return false
        }

        console.log('✅ Agency login successful!')
        console.log('User ID:', data.user?.id)

        // Check user profile
        const { data: profile, error: profileError } = await supabase
            .from('users')
            .select('*')
            .eq('id', data.user.id)
            .single()

        if (profileError) {
            console.error('❌ Profile fetch failed:', profileError.message)
            return false
        }

        console.log('✅ User profile loaded!')
        console.log('Role:', profile.role)
        console.log('Email:', profile.email)
        console.log('Name:', profile.first_name, profile.last_name)

        // Check agency profile
        const { data: agencyProfile, error: agencyError } = await supabase
            .from('agencies')
            .select('*')
            .eq('user_id', data.user.id)
            .single()

        if (agencyError) {
            console.error('❌ Agency profile fetch failed:', agencyError.message)
            console.log('This might be normal if the agency profile wasn\'t created yet')
        } else {
            console.log('✅ Agency profile found!')
            console.log('Agency Name:', agencyProfile.agency_name)
            console.log('Agency Email:', agencyProfile.agency_email)
        }

        // Sign out
        await supabase.auth.signOut()
        console.log('✅ Signed out successfully')

        return true
    } catch (err) {
        console.error('❌ Agency login test error:', err.message)
        return false
    }
}

async function createTestAgency() {
    console.log('\n🏢 Creating a test agency user...')

    try {
        const testEmail = `testagency${Date.now()}@gmail.com`
        const testPassword = 'TestPassword123!'

        console.log(`📝 Creating agency: ${testEmail}`)

        // Create the auth user
        const { data: authData, error: authError } = await supabase.auth.signUp({
            email: testEmail,
            password: testPassword,
            options: {
                data: {
                    first_name: 'Test',
                    last_name: 'Agency',
                    role: 'agency'
                }
            }
        })

        if (authError || !authData.user) {
            console.error('❌ Agency auth creation failed:', authError?.message)
            return false
        }

        console.log('✅ Agency auth user created!')

        // Wait for profile creation
        await new Promise(resolve => setTimeout(resolve, 2000))

        // Create agency record
        const { data: agencyData, error: agencyError } = await supabase
            .from('agencies')
            .insert({
                user_id: authData.user.id,
                agency_name: 'Test Agency',
                agency_description: 'A test agency for testing',
                agency_address: '123 Test Street',
                agency_phone: '+1234567890',
                agency_email: testEmail,
                agency_website: 'https://testagency.com',
                is_approved: false
            })
            .select()
            .single()

        if (agencyError) {
            console.error('❌ Agency creation failed:', agencyError.message)
            return false
        }

        console.log('✅ Agency record created!')
        console.log('\n🎉 Test agency created successfully!')
        console.log('Email:', testEmail)
        console.log('Password:', testPassword)
        console.log('Role: agency')
        console.log('Agency Name:', agencyData.agency_name)

        return { email: testEmail, password: testPassword }
    } catch (err) {
        console.error('❌ Agency creation error:', err.message)
        return false
    }
}

async function main() {
    console.log('🚀 Testing agency authentication...\n')

    // Try to login with existing agency
    const loginSuccess = await testAgencyLogin()

    if (!loginSuccess) {
        console.log('\n🔄 Creating a new test agency user...')
        const newAgency = await createTestAgency()

        if (newAgency) {
            console.log('\n✅ You can now test agency login with:')
            console.log('Email:', newAgency.email)
            console.log('Password:', newAgency.password)
            console.log('\nTry logging in with these credentials in your web app!')
        }
    }

    console.log('\n✅ Agency authentication test completed!')
}

main().catch(console.error) 