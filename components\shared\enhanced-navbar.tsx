"use client"

import { useState } from "react"
import Link from "next/link"
import { Car, Globe, DollarSign, ChevronDown, Check } from "lucide-react"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
} from "@/components/ui/dropdown-menu"
import { cn } from "@/lib/utils"

type Currency = {
  code: string
  symbol: string
  name: string
  rate: number // Exchange rate relative to MAD
}

type Language = {
  code: string
  name: string
  nativeName: string
  flag: string
}

const currencies: Currency[] = [
  { code: "MAD", symbol: "د.م.", name: "Moroccan Dirham", rate: 1 },
  { code: "USD", symbol: "$", name: "US Dollar", rate: 0.099 },
  { code: "EUR", symbol: "€", name: "Euro", rate: 0.091 },
  { code: "GBP", symbol: "£", name: "British Pound", rate: 0.078 },
]

const languages: Language[] = [
  { code: "en", name: "English", nativeName: "English", flag: "🇬🇧" },
  { code: "ar", name: "Arabic", nativeName: "العربية", flag: "🇲🇦" },
  { code: "fr", name: "French", nativeName: "Français", flag: "🇫🇷" },
  { code: "es", name: "Spanish", nativeName: "Español", flag: "🇪🇸" },
]

export function EnhancedNavbar() {
  const [currency, setCurrency] = useState<Currency>(currencies[0])
  const [language, setLanguage] = useState<Language>(languages[0])

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-16 items-center">
        <Link className="flex items-center justify-center mr-6" href="/">
          <Car className="h-6 w-6 text-primary" />
          <span className="ml-2 text-xl font-bold">KriwDrive</span>
        </Link>

        <nav className="hidden md:flex items-center gap-6 text-sm">
          <Link className="font-medium transition-colors hover:text-primary" href="/listings">
            Browse Cars
          </Link>
          <Link className="font-medium transition-colors hover:text-primary" href="/become-host">
            List Your Car
          </Link>
          <Link className="font-medium transition-colors hover:text-primary" href="/agency/dashboard">
            Agency Dashboard
          </Link>
        </nav>

        <div className="flex-1"></div>

        <div className="flex items-center gap-2">
          {/* Currency Selector */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="h-8 gap-1 border-dashed">
                <DollarSign className="h-3.5 w-3.5" />
                <span>{currency.code}</span>
                <ChevronDown className="h-3.5 w-3.5 opacity-50" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[200px]">
              <DropdownMenuRadioGroup
                value={currency.code}
                onValueChange={(value) => {
                  const selected = currencies.find((c) => c.code === value)
                  if (selected) setCurrency(selected)
                }}
              >
                {currencies.map((c) => (
                  <DropdownMenuRadioItem key={c.code} value={c.code} className="cursor-pointer">
                    <div className="flex items-center justify-between w-full">
                      <span>
                        {c.symbol} {c.code}
                      </span>
                      <span className="text-xs text-muted-foreground">{c.name}</span>
                    </div>
                  </DropdownMenuRadioItem>
                ))}
              </DropdownMenuRadioGroup>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Language Selector */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="h-8 gap-1 border-dashed">
                <Globe className="h-3.5 w-3.5" />
                <span>{language.flag}</span>
                <ChevronDown className="h-3.5 w-3.5 opacity-50" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[180px]">
              {languages.map((lang) => (
                <DropdownMenuItem
                  key={lang.code}
                  className={cn(
                    "cursor-pointer flex items-center justify-between",
                    language.code === lang.code && "font-medium",
                  )}
                  onClick={() => setLanguage(lang)}
                >
                  <div className="flex items-center gap-2">
                    <span className="text-base">{lang.flag}</span>
                    <span>{lang.name}</span>
                  </div>
                  {language.code === lang.code && <Check className="h-4 w-4" />}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>

          <div className="hidden md:flex items-center gap-2 ml-2">
            <Button variant="outline" size="sm">
              Sign In
            </Button>
            <Button size="sm">Sign Up</Button>
          </div>
        </div>
      </div>
    </header>
  )
}
