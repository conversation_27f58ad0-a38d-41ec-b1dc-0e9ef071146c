-- Temporarily disable <PERSON><PERSON> to fix recursion issues
-- This will allow basic operations to work while we fix the policies

-- Disable RLS on users table temporarily
ALTER TABLE public.users DISABLE ROW LEVEL SECURITY;

-- Disable RLS on agencies table temporarily
ALTER TABLE public.agencies DISABLE ROW LEVEL SECURITY;

-- Disable RLS on cars table temporarily
ALTER TABLE public.cars DISABLE ROW LEVEL SECURITY;

-- Disable RLS on bookings table temporarily
ALTER TABLE public.bookings DISABLE ROW LEVEL SECURITY;

-- Note: This is a temporary fix. In production, you should re-enable RLS
-- with properly designed policies that don't cause recursion. 