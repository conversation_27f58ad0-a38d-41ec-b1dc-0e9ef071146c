// Create a test agency user with known credentials
// Run with: node create-test-agency.js

// Load environment variables from .env.local
require('dotenv').config({ path: '.env.local' })

const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://sagtwjbwgfgvzulnsmhc.supabase.co'
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseKey) {
    console.error('❌ NEXT_PUBLIC_SUPABASE_ANON_KEY not found')
    process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseKey)

async function createTestAgency() {
    console.log('🏢 Creating a test agency user...')

    try {
        const testEmail = '<EMAIL>'
        const testPassword = 'TestPassword123!'

        console.log(`📝 Creating agency: ${testEmail}`)

        // First check if user already exists
        const { data: existingUser } = await supabase
            .from('users')
            .select('*')
            .eq('email', testEmail)
            .single()

        if (existingUser) {
            console.log('✅ User already exists, updating role to agency...')

            // Update the existing user to agency role
            const { data: updatedUser, error: updateError } = await supabase
                .from('users')
                .update({ role: 'agency' })
                .eq('id', existingUser.id)
                .select()
                .single()

            if (updateError) {
                console.error('❌ Failed to update user role:', updateError.message)
                return false
            }

            console.log('✅ User role updated to agency!')

            // Check if agency profile exists
            const { data: existingAgency } = await supabase
                .from('agencies')
                .select('*')
                .eq('user_id', existingUser.id)
                .single()

            if (!existingAgency) {
                console.log('Creating agency profile...')
                const { data: agencyData, error: agencyError } = await supabase
                    .from('agencies')
                    .insert({
                        user_id: existingUser.id,
                        agency_name: 'Test Agency',
                        agency_description: 'A test agency for testing',
                        agency_address: '123 Test Street',
                        agency_phone: '+1234567890',
                        agency_email: testEmail,
                        agency_website: 'https://testagency.com',
                        is_approved: false
                    })
                    .select()
                    .single()

                if (agencyError) {
                    console.error('❌ Agency creation failed:', agencyError.message)
                    return false
                }

                console.log('✅ Agency profile created!')
            } else {
                console.log('✅ Agency profile already exists!')
            }

            console.log('\n🎉 Test agency is ready!')
            console.log('Email:', testEmail)
            console.log('Password:', testPassword)
            console.log('Role: agency')

            return true
        }

        // Create new user
        const { data: authData, error: authError } = await supabase.auth.signUp({
            email: testEmail,
            password: testPassword,
            options: {
                data: {
                    first_name: 'Test',
                    last_name: 'Agency',
                    role: 'agency'
                }
            }
        })

        if (authError || !authData.user) {
            console.error('❌ Agency auth creation failed:', authError?.message)
            return false
        }

        console.log('✅ Agency auth user created!')

        // Wait for profile creation
        await new Promise(resolve => setTimeout(resolve, 2000))

        // Create agency record
        const { data: agencyData, error: agencyError } = await supabase
            .from('agencies')
            .insert({
                user_id: authData.user.id,
                agency_name: 'Test Agency',
                agency_description: 'A test agency for testing',
                agency_address: '123 Test Street',
                agency_phone: '+1234567890',
                agency_email: testEmail,
                agency_website: 'https://testagency.com',
                is_approved: false
            })
            .select()
            .single()

        if (agencyError) {
            console.error('❌ Agency creation failed:', agencyError.message)
            return false
        }

        console.log('✅ Agency record created!')
        console.log('\n🎉 Test agency created successfully!')
        console.log('Email:', testEmail)
        console.log('Password:', testPassword)
        console.log('Role: agency')
        console.log('Agency Name:', agencyData.agency_name)

        return true
    } catch (err) {
        console.error('❌ Agency creation error:', err.message)
        return false
    }
}

async function main() {
    console.log('🚀 Creating test agency user...\n')

    const success = await createTestAgency()
    if (!success) {
        console.log('\n❌ Failed to create test agency.')
        process.exit(1)
    }

    console.log('\n✅ Test agency user is ready for testing!')
    console.log('You can now log in with:')
    console.log('Email: <EMAIL>')
    console.log('Password: TestPassword123!')
}

main().catch(console.error) 