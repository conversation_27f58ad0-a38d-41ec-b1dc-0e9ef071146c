"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Slider } from "@/components/ui/slider"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { FeaturedCarCard } from "@/components/features/car/featured-car-card"
import { SearchIcon, Filter, Car, Bike, Heart, Loader2 } from "lucide-react"
import { useI18n } from "@/i18n/i18n-provider"
import { useSearchParams } from "next/navigation"
import Link from "next/link"
import { DatabaseService } from "@/services/database.service"
import { CAR_BRANDS, CAR_COLORS, CAR_CATEGORIES } from "@/utils/constants"
import { toast } from "sonner"

export default function ListingsPage() {
  const { t } = useI18n()
  const [searchTerm, setSearchTerm] = useState("")
  const [vehicleType, setVehicleType] = useState<"car">("car")
  const [selectedCity, setSelectedCity] = useState<string>("all")
  const [selectedColor, setSelectedColor] = useState<string>("all")
  const [selectedBrand, setSelectedBrand] = useState<string>("all")
  const [selectedCategory, setSelectedCategory] = useState<string>("all")
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 500])
  const [yearRange, setYearRange] = useState<[number, number]>([2015, 2024])
  const searchParams = useSearchParams()
  const [favorites, setFavorites] = useState<string[]>([])
  const [currentPage, setCurrentPage] = useState(1)
  const [cars, setCars] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [totalCars, setTotalCars] = useState(0)
  const pageSize = 9

  const moroccanCities = [
    "Agadir", "Al Hoceima", "Asilah", "Azrou", "Beni Mellal", "Berkane", "Berrechid",
    "Boujdour", "Casablanca", "Chefchaouen", "Dakhla", "El Jadida", "Errachidia",
    "Essaouira", "Fes", "Guelmim", "Ifrane", "Kenitra", "Khemisset", "Khouribga",
    "Laayoune", "Larache", "Marrakech", "Martil", "Meknes", "Mohammedia", "Nador",
    "Ouarzazate", "Oujda", "Rabat", "Safi", "Sale", "Settat", "Sidi Ifni",
    "Tangier", "Taza", "Tetouan", "Tiznit"
  ]

  // Fetch cars from database
  useEffect(() => {
    if (vehicleType === "car") {
      fetchCars()
    }
  }, [searchTerm, selectedCity, selectedColor, selectedBrand, selectedCategory, priceRange, yearRange, vehicleType])

  const fetchCars = async () => {
    try {
      setLoading(true)

      const filters: any = {
        // Temporarily remove status filter to see all cars
        // status: 'available'
      }

      if (selectedBrand !== 'all') filters.brand = selectedBrand
      if (selectedColor !== 'all') filters.color = selectedColor
      if (selectedCategory !== 'all') filters.category = selectedCategory
      if (selectedCity !== 'all') filters.city = selectedCity
      if (priceRange[0] > 0) filters.min_price = priceRange[0]
      if (priceRange[1] < 500) filters.max_price = priceRange[1]
      if (yearRange[0] > 2015) filters.min_year = yearRange[0]
      if (yearRange[1] < 2024) filters.max_year = yearRange[1]

      const { data, error } = await DatabaseService.getCars(filters)

      if (error) {
        console.error('Error fetching cars:', error)
        toast.error('Failed to load cars')
        return
      }

      // Filter by search term on frontend for better UX
      let filteredCars = data || []
      if (searchTerm.trim()) {
        filteredCars = filteredCars.filter((car: any) =>
          car.brand.toLowerCase().includes(searchTerm.toLowerCase()) ||
          car.model.toLowerCase().includes(searchTerm.toLowerCase()) ||
          car.city?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          car.agencies?.agency_name?.toLowerCase().includes(searchTerm.toLowerCase())
        )
      }

      setCars(filteredCars)
      setTotalCars(filteredCars.length)
    } catch (error) {
      console.error('Error fetching cars:', error)
      toast.error('Failed to load cars')
    } finally {
      setLoading(false)
    }
  }

  const handleResetFilters = () => {
    setSearchTerm("")
    setSelectedCity("all")
    setSelectedColor("all")
    setSelectedBrand("all")
    setSelectedCategory("all")
    setPriceRange([0, 500])
    setYearRange([2015, 2024])
  }

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Search is handled by useEffect
  }

  const toggleFavorite = (id: string) => {
    setFavorites(prev =>
      prev.includes(id)
        ? prev.filter(fav => fav !== id)
        : [...prev, id]
    )
  }

  // Only cars are supported now
  const allVehicles = cars
  const filteredVehicles = allVehicles.filter((vehicle) => {
    const matchesSearch = !searchTerm.trim() ||
      vehicle.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      vehicle.location?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      vehicle.category?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      vehicle.brand?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      vehicle.model?.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesCity = selectedCity === "all" ||
      vehicle.location?.toLowerCase().includes(selectedCity.toLowerCase()) ||
      vehicle.city?.toLowerCase().includes(selectedCity.toLowerCase())

    const matchesPrice = vehicle.price >= priceRange[0] && vehicle.price <= priceRange[1]
    const matchesYear = !vehicle.year || (vehicle.year >= yearRange[0] && vehicle.year <= yearRange[1])

    return matchesSearch && matchesCity && matchesPrice && matchesYear
  })

  // Pagination
  const totalPages = Math.ceil(filteredVehicles.length / pageSize)
  const startIndex = (currentPage - 1) * pageSize
  const paginatedVehicles = filteredVehicles.slice(startIndex, startIndex + pageSize)

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Filters Sidebar */}
          <div className="lg:w-80 space-y-6">
            <Card>
              <CardContent className="p-6">
                {/* Vehicle Type - Cars Only */}
                <div className="flex gap-2 mb-6">
                  <Button
                    variant="default"
                    className="flex flex-col items-center gap-1 px-4 py-2 w-full"
                    disabled
                  >
                    <Car className="h-6 w-6 mb-1" />
                    <span className="text-xs font-medium">Cars</span>
                  </Button>
                </div>

                <div className="flex justify-between items-center">
                  <h2 className="text-lg font-semibold">{t("listings.filters.title")}</h2>
                  <Button variant="ghost" size="sm" onClick={handleResetFilters}>
                    Reset
                  </Button>
                </div>

                <div className="space-y-4 mt-4">
                  {/* City Filter */}
                  <div>
                    <Label className="text-sm font-medium mb-2 block">City</Label>
                    <Select value={selectedCity} onValueChange={setSelectedCity}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select city" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Cities</SelectItem>
                        {moroccanCities.map(city => (
                          <SelectItem key={city} value={city.toLowerCase()}>{city}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {vehicleType === "car" && (
                    <>
                      {/* Brand Filter */}
                      <div>
                        <Label className="text-sm font-medium mb-2 block">Brand</Label>
                        <Select value={selectedBrand} onValueChange={setSelectedBrand}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select brand" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">All Brands</SelectItem>
                            {CAR_BRANDS.map(brand => (
                              <SelectItem key={brand} value={brand}>{brand}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      {/* Category Filter */}
                      <div>
                        <Label className="text-sm font-medium mb-2 block">Category</Label>
                        <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select category" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">All Categories</SelectItem>
                            {CAR_CATEGORIES.map(category => (
                              <SelectItem key={category} value={category}>{category}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      {/* Color Filter */}
                      <div>
                        <Label className="text-sm font-medium mb-2 block">Color</Label>
                        <Select value={selectedColor} onValueChange={setSelectedColor}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select color" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">All Colors</SelectItem>
                            {CAR_COLORS.map(color => (
                              <SelectItem key={color} value={color}>{color}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </>
                  )}

                  {/* Price Range */}
                  <div>
                    <Label className="text-sm font-medium mb-2 block">
                      Price Range: ${priceRange[0]} - ${priceRange[1]}
                    </Label>
                    <Slider
                      value={priceRange}
                      onValueChange={(value) => setPriceRange(value as [number, number])}
                      max={500}
                      min={0}
                      step={10}
                      className="w-full"
                    />
                  </div>

                  {/* Year Range */}
                  {vehicleType === "car" && (
                    <div>
                      <Label className="text-sm font-medium mb-2 block">
                        Year Range: {yearRange[0]} - {yearRange[1]}
                      </Label>
                      <Slider
                        value={yearRange}
                        onValueChange={(value) => setYearRange(value as [number, number])}
                        max={2024}
                        min={2015}
                        step={1}
                        className="w-full"
                      />
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Search Results */}
          <div className="flex-1 space-y-6">
            <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold">
                  {t("listings.title")}
                </h1>
                <p className="text-muted-foreground">
                  {loading ? "Loading..." : `${filteredVehicles.length} cars found`}
                </p>
              </div>
              <form onSubmit={handleSearchSubmit} className="flex gap-2 w-full md:w-auto">
                <div className="relative flex-1 md:flex-initial">
                  <SearchIcon className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                  <Input
                    type="text"
                    placeholder="Search cars..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-9 w-full md:w-80"
                  />
                </div>
                <Button type="submit">
                  <SearchIcon className="h-4 w-4" />
                </Button>
              </form>
            </div>

            {loading ? (
              <div className="flex justify-center items-center py-12">
                <Loader2 className="h-8 w-8 animate-spin" />
                <span className="ml-2">Loading cars...</span>
              </div>
            ) : paginatedVehicles.length > 0 ? (
              <>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                  {paginatedVehicles.map((vehicle) => (
                    <Link key={vehicle.id} href={`/listings/car-details/${vehicle.id}`}>
                      <div className="relative">
                        <FeaturedCarCard
                          id={vehicle.id}
                          title={vehicle.title || `${vehicle.brand} ${vehicle.model}`}
                          price={vehicle.daily_rate || vehicle.price}
                          location={vehicle.city || vehicle.address || vehicle.location || 'Morocco'}
                          images={vehicle.images || ["/placeholder.svg"]}
                          rating={vehicle.rating || 4.5}
                          reviews={vehicle.reviews || 0}
                          category={vehicle.category}
                          description={vehicle.description}
                        />
                        <Button
                          variant="ghost"
                          size="icon"
                          className="absolute top-2 right-2 bg-white/80 hover:bg-white"
                          onClick={(e) => {
                            e.preventDefault()
                            toggleFavorite(vehicle.id)
                          }}
                        >
                          <Heart
                            className={`h-4 w-4 ${favorites.includes(vehicle.id) ? 'fill-red-500 text-red-500' : ''}`}
                          />
                        </Button>
                      </div>
                    </Link>
                  ))}
                </div>

                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="flex justify-center gap-2 mt-8">
                    <Button
                      variant="outline"
                      onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                      disabled={currentPage === 1}
                    >
                      Previous
                    </Button>
                    {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                      <Button
                        key={page}
                        variant={currentPage === page ? "default" : "outline"}
                        onClick={() => setCurrentPage(page)}
                      >
                        {page}
                      </Button>
                    ))}
                    <Button
                      variant="outline"
                      onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                      disabled={currentPage === totalPages}
                    >
                      Next
                    </Button>
                  </div>
                )}
              </>
            ) : (
              <div className="text-center py-12">
                <Car className="h-16 w-16 mx-auto text-gray-400 mb-4" />
                <h3 className="text-lg font-semibold mb-2">No {vehicleType}s found</h3>
                <p className="text-muted-foreground mb-4">
                  Try adjusting your filters or search terms
                </p>
                <Button onClick={handleResetFilters}>Reset Filters</Button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
