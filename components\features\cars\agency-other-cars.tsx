"use client"

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { supabase } from '@/lib/supabase/client'
import { MapPin, Star, Users, Fuel, Settings } from 'lucide-react'
import { useCurrency } from '@/contexts/currency-context'

interface Car {
  id: string
  make: string
  model: string
  year: number
  daily_rate: number
  images: string[]
  status: string
  transmission: string
  fuel_type: string
  seats: number
  category: string
}

interface Agency {
  id: string
  agency_name: string
  agency_address?: string
}

interface AgencyOtherCarsProps {
  currentCarId: string
  agencyId: string
  className?: string
}

export function AgencyOtherCars({ currentCarId, agencyId, className }: AgencyOtherCarsProps) {
  const [cars, setCars] = useState<Car[]>([])
  const [agency, setAgency] = useState<Agency | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const { convert } = useCurrency()

  useEffect(() => {
    fetchAgencyAndCars()
  }, [agencyId, currentCarId])

  const fetchAgencyAndCars = async () => {
    try {
      setIsLoading(true)

      // Fetch agency details
      const { data: agencyData, error: agencyError } = await supabase
        .from('agencies')
        .select('id, agency_name, agency_address')
        .eq('id', agencyId)
        .single()

      if (agencyError) {
        console.error('Error fetching agency:', agencyError)
        return
      }

      setAgency(agencyData)

      // Fetch other cars from the same agency (excluding current car)
      const { data: carsData, error: carsError } = await supabase
        .from('cars')
        .select('id, make, model, year, daily_rate, images, status, transmission, fuel_type, seats, category')
        .eq('agency_id', agencyId)
        .neq('id', currentCarId)
        .eq('status', 'available')
        .limit(6)

      if (carsError) {
        console.error('Error fetching cars:', carsError)
        return
      }

      setCars(carsData || [])
    } catch (error) {
      console.error('Error fetching agency cars:', error)
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>More Cars from This Agency</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!cars.length) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>More Cars from {agency?.agency_name}</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-500 text-center py-8">
            No other cars available from this agency at the moment.
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>More Cars from {agency?.agency_name}</span>
          <Badge variant="secondary">{cars.length} available</Badge>
        </CardTitle>
        {agency?.agency_address && (
          <div className="flex items-center text-sm text-gray-600">
            <MapPin className="h-4 w-4 mr-1" />
            {agency.agency_address}
          </div>
        )}
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {cars.map((car) => (
            <div key={car.id} className="border rounded-lg overflow-hidden hover:shadow-lg transition-shadow">
              <div className="relative h-48">
                <img
                  src={car.images?.[0] || '/placeholder.svg?height=200&width=300'}
                  alt={`${car.brand} ${car.model}`}
                  className="w-full h-full object-cover"
                />
                <Badge className="absolute top-2 right-2 bg-green-500">
                  {car.status}
                </Badge>
              </div>

              <div className="p-4">
                <h3 className="font-semibold text-lg mb-2">
                  {car.make} {car.model} {car.year}
                </h3>

                <div className="flex items-center justify-between text-sm text-gray-600 mb-3">
                  <div className="flex items-center gap-3">
                    <div className="flex items-center">
                      <Users className="h-4 w-4 mr-1" />
                      {car.seats}
                    </div>
                    <div className="flex items-center">
                      <Settings className="h-4 w-4 mr-1" />
                      {car.transmission}
                    </div>
                    <div className="flex items-center">
                      <Fuel className="h-4 w-4 mr-1" />
                      {car.fuel_type}
                    </div>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="text-lg font-bold text-primary">
                    {convert(car.daily_rate)}/day
                  </div>
                  <Button size="sm" asChild>
                    <Link href={`/listings/car-details/${car.id}`}>
                      View Details
                    </Link>
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {cars.length >= 6 && (
          <div className="text-center mt-6">
            <Button variant="outline" asChild>
              <Link href={`/agency/details/${agencyId}`}>
                View All Cars from {agency?.agency_name}
              </Link>
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
