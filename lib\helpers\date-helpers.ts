// Date utility functions
import { format, formatDistance, parseISO, isValid, differenceInDays, addDays, startOfDay, endOfDay } from 'date-fns';

/**
 * Format a date to a readable string
 */
export function formatDate(date: Date | string, formatStr: string = 'MMM dd, yyyy'): string {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;

    if (!isValid(dateObj)) {
        return 'Invalid date';
    }

    return format(dateObj, formatStr);
}

/**
 * Format a date to show relative time (e.g., "2 hours ago")
 */
export function formatRelativeTime(date: Date | string): string {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;

    if (!isValid(dateObj)) {
        return 'Invalid date';
    }

    return formatDistance(dateObj, new Date(), { addSuffix: true });
}

/**
 * Calculate the number of days between two dates
 */
export function getDaysDifference(startDate: Date | string, endDate: Date | string): number {
    const start = typeof startDate === 'string' ? parseISO(startDate) : startDate;
    const end = typeof endDate === 'string' ? parseISO(endDate) : endDate;

    if (!isValid(start) || !isValid(end)) {
        return 0;
    }

    return differenceInDays(end, start);
}

/**
 * Add days to a date
 */
export function addDaysToDate(date: Date | string, days: number): Date {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;

    if (!isValid(dateObj)) {
        return new Date();
    }

    return addDays(dateObj, days);
}

/**
 * Get start of day for a date
 */
export function getStartOfDay(date: Date | string): Date {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;

    if (!isValid(dateObj)) {
        return new Date();
    }

    return startOfDay(dateObj);
}

/**
 * Get end of day for a date
 */
export function getEndOfDay(date: Date | string): Date {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;

    if (!isValid(dateObj)) {
        return new Date();
    }

    return endOfDay(dateObj);
}

/**
 * Check if a date is in the past
 */
export function isPastDate(date: Date | string): boolean {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;

    if (!isValid(dateObj)) {
        return false;
    }

    return dateObj < new Date();
}

/**
 * Check if a date is in the future
 */
export function isFutureDate(date: Date | string): boolean {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;

    if (!isValid(dateObj)) {
        return false;
    }

    return dateObj > new Date();
}

/**
 * Check if a date is today
 */
export function isToday(date: Date | string): boolean {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;

    if (!isValid(dateObj)) {
        return false;
    }

    const today = new Date();
    return format(dateObj, 'yyyy-MM-dd') === format(today, 'yyyy-MM-dd');
}

/**
 * Format date for input field (YYYY-MM-DD)
 */
export function formatDateForInput(date: Date | string): string {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;

    if (!isValid(dateObj)) {
        return format(new Date(), 'yyyy-MM-dd');
    }

    return format(dateObj, 'yyyy-MM-dd');
}

/**
 * Format time for input field (HH:mm)
 */
export function formatTimeForInput(date: Date | string): string {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;

    if (!isValid(dateObj)) {
        return format(new Date(), 'HH:mm');
    }

    return format(dateObj, 'HH:mm');
}

/**
 * Get month name from date
 */
export function getMonthName(date: Date | string): string {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;

    if (!isValid(dateObj)) {
        return 'Invalid date';
    }

    return format(dateObj, 'MMMM');
}

/**
 * Get day name from date
 */
export function getDayName(date: Date | string): string {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;

    if (!isValid(dateObj)) {
        return 'Invalid date';
    }

    return format(dateObj, 'EEEE');
}

/**
 * Check if two dates are the same day
 */
export function isSameDay(date1: Date | string, date2: Date | string): boolean {
    const dateObj1 = typeof date1 === 'string' ? parseISO(date1) : date1;
    const dateObj2 = typeof date2 === 'string' ? parseISO(date2) : date2;

    if (!isValid(dateObj1) || !isValid(dateObj2)) {
        return false;
    }

    return format(dateObj1, 'yyyy-MM-dd') === format(dateObj2, 'yyyy-MM-dd');
} 