"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { toast } from "sonner"
import { Sidebar } from "@/components/admin/sidebar"
import { SupabaseAuthService } from "@/services/supabase-auth.service"
import { useAuth } from "@/contexts/auth-context"
import { <PERSON><PERSON><PERSON>cyProvider } from "@/contexts/currency-context"

export default function AdminLayout({ children }: { children: React.ReactNode }) {
    const router = useRouter()
    const [isLoading, setIsLoading] = useState(true)
    const [isAuthed, setIsAuthed] = useState(false)
    const { user } = useAuth()

    useEffect(() => {
        const checkAdminAuth = async () => {
            try {
                console.log('🔍 Admin layout: Checking authentication...')

                // TEMPORARILY SIMPLIFIED AUTH CHECK FOR TESTING
                if (user && user.role === 'admin') {
                    console.log('✅ Admin layout: User authenticated via auth context')
                    setIsAuthed(true)
                    setIsLoading(false)
                    return
                }

                // If no user or not admin, redirect to login
                console.log('❌ Admin layout: No admin user found, redirecting to login')
                toast.error("Admin authentication required")
                router.replace('/admin-login')
            } catch (error) {
                console.error("Admin auth check failed:", error)
                toast.error("Authentication check failed")
                router.replace('/admin-login')
            } finally {
                setIsLoading(false)
            }
        }

        checkAdminAuth()
    }, [user, router])

    if (isLoading) return null
    if (!isAuthed) return null

    return (
        <CurrencyProvider>
            <div className="flex min-h-screen bg-gray-50">
                <Sidebar />
                <div className="flex-1">
                    {children}
                </div>
            </div>
        </CurrencyProvider>
    )
} 
