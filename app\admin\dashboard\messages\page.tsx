'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { 
  Mail, 
  Phone, 
  Calendar, 
  User, 
  MessageSquare, 
  AlertCircle,
  CheckCircle,
  Clock,
  Archive,
  Reply,
  Search,
  Filter
} from 'lucide-react'
import { DatabaseService } from '@/services/database.service'
import { toast } from 'sonner'
import { Database } from '@/types/supabase'

type Message = Database['public']['Tables']['messages']['Row']

export default function AdminMessagesPage() {
  const [messages, setMessages] = useState<Message[]>([])
  const [selectedMessage, setSelectedMessage] = useState<Message | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [filter, setFilter] = useState<'all' | 'unread' | 'contact' | 'payment'>('all')
  const [searchTerm, setSearchTerm] = useState('')
  const [replyText, setReplyText] = useState('')
  const [isReplying, setIsReplying] = useState(false)

  useEffect(() => {
    loadMessages()
  }, [filter])

  const loadMessages = async () => {
    setIsLoading(true)
    try {
      const filters: any = {}
      if (filter === 'unread') filters.status = 'unread'
      if (filter === 'contact') filters.type = 'contact'
      if (filter === 'payment') filters.type = 'payment'

      const { data, error } = await DatabaseService.getMessages(filters)
      if (error) throw error
      setMessages(data || [])
    } catch (error) {
      console.error('Error loading messages:', error)
      toast.error('Failed to load messages')
    } finally {
      setIsLoading(false)
    }
  }

  const handleMarkAsRead = async (messageId: string) => {
    try {
      const { error } = await DatabaseService.markMessageAsRead(messageId)
      if (error) throw error
      
      setMessages(prev => prev.map(msg => 
        msg.id === messageId ? { ...msg, status: 'read' as const } : msg
      ))
      toast.success('Message marked as read')
    } catch (error) {
      console.error('Error marking message as read:', error)
      toast.error('Failed to mark message as read')
    }
  }

  const handleReply = async () => {
    if (!selectedMessage || !replyText.trim()) return

    setIsReplying(true)
    try {
      const { error } = await DatabaseService.updateMessage(selectedMessage.id, {
        status: 'replied',
        admin_notes: replyText,
        replied_at: new Date().toISOString()
      })
      
      if (error) throw error

      setMessages(prev => prev.map(msg => 
        msg.id === selectedMessage.id 
          ? { ...msg, status: 'replied' as const, admin_notes: replyText, replied_at: new Date().toISOString() }
          : msg
      ))
      
      setSelectedMessage(prev => prev ? { 
        ...prev, 
        status: 'replied' as const, 
        admin_notes: replyText,
        replied_at: new Date().toISOString()
      } : null)
      
      setReplyText('')
      toast.success('Reply sent successfully')
    } catch (error) {
      console.error('Error sending reply:', error)
      toast.error('Failed to send reply')
    } finally {
      setIsReplying(false)
    }
  }

  const getStatusBadge = (status: Message['status']) => {
    const variants = {
      unread: { variant: 'destructive' as const, icon: AlertCircle, text: 'Unread' },
      read: { variant: 'secondary' as const, icon: CheckCircle, text: 'Read' },
      replied: { variant: 'default' as const, icon: Reply, text: 'Replied' },
      archived: { variant: 'outline' as const, icon: Archive, text: 'Archived' }
    }
    
    const config = variants[status]
    const Icon = config.icon
    
    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="h-3 w-3" />
        {config.text}
      </Badge>
    )
  }

  const getTypeBadge = (type: Message['type']) => {
    const colors = {
      contact: 'bg-blue-100 text-blue-800',
      payment: 'bg-green-100 text-green-800',
      support: 'bg-purple-100 text-purple-800'
    }
    
    return (
      <Badge className={colors[type]}>
        {type.charAt(0).toUpperCase() + type.slice(1)}
      </Badge>
    )
  }

  const filteredMessages = messages.filter(message =>
    message.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    message.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    message.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
    message.message.toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">Messages</h1>
          <p className="text-muted-foreground">Manage contact and support messages</p>
        </div>
        <div className="flex items-center gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search messages..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-64"
            />
          </div>
          <div className="flex items-center gap-2">
            <Filter className="h-4 w-4 text-muted-foreground" />
            <select
              value={filter}
              onChange={(e) => setFilter(e.target.value as any)}
              className="border rounded-md px-3 py-2 text-sm"
            >
              <option value="all">All Messages</option>
              <option value="unread">Unread</option>
              <option value="contact">Contact</option>
              <option value="payment">Payment</option>
            </select>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Messages List */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                Messages ({filteredMessages.length})
                <Button onClick={loadMessages} variant="outline" size="sm">
                  Refresh
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <div className="max-h-[600px] overflow-y-auto">
                {isLoading ? (
                  <div className="p-4 text-center text-muted-foreground">
                    Loading messages...
                  </div>
                ) : filteredMessages.length === 0 ? (
                  <div className="p-4 text-center text-muted-foreground">
                    No messages found
                  </div>
                ) : (
                  filteredMessages.map((message) => (
                    <div
                      key={message.id}
                      className={`p-4 border-b cursor-pointer hover:bg-muted/50 transition-colors ${
                        selectedMessage?.id === message.id ? 'bg-muted' : ''
                      }`}
                      onClick={() => setSelectedMessage(message)}
                    >
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center gap-2">
                          {getStatusBadge(message.status)}
                          {getTypeBadge(message.type)}
                        </div>
                        <span className="text-xs text-muted-foreground">
                          {new Date(message.created_at).toLocaleDateString()}
                        </span>
                      </div>
                      <h4 className="font-medium text-sm mb-1">{message.subject}</h4>
                      <p className="text-sm text-muted-foreground mb-2">
                        From: {message.name} ({message.email})
                      </p>
                      <p className="text-xs text-muted-foreground line-clamp-2">
                        {message.message}
                      </p>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Message Details */}
        <div className="lg:col-span-2">
          {selectedMessage ? (
            <Card>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <MessageSquare className="h-5 w-5" />
                      {selectedMessage.subject}
                    </CardTitle>
                    <div className="flex items-center gap-4 mt-2">
                      {getStatusBadge(selectedMessage.status)}
                      {getTypeBadge(selectedMessage.type)}
                    </div>
                  </div>
                  {selectedMessage.status === 'unread' && (
                    <Button
                      onClick={() => handleMarkAsRead(selectedMessage.id)}
                      variant="outline"
                      size="sm"
                    >
                      Mark as Read
                    </Button>
                  )}
                </div>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Sender Info */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-muted/50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">{selectedMessage.name}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    <span>{selectedMessage.email}</span>
                  </div>
                  {selectedMessage.phone && (
                    <div className="flex items-center gap-2">
                      <Phone className="h-4 w-4 text-muted-foreground" />
                      <span>{selectedMessage.phone}</span>
                    </div>
                  )}
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span>{new Date(selectedMessage.created_at).toLocaleString()}</span>
                  </div>
                </div>

                {/* Message Content */}
                <div>
                  <h4 className="font-medium mb-2">Message:</h4>
                  <div className="p-4 bg-background border rounded-lg">
                    <p className="whitespace-pre-wrap">{selectedMessage.message}</p>
                  </div>
                </div>

                {/* Admin Notes/Reply */}
                {selectedMessage.admin_notes && (
                  <div>
                    <h4 className="font-medium mb-2">Admin Reply:</h4>
                    <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                      <p className="whitespace-pre-wrap">{selectedMessage.admin_notes}</p>
                      {selectedMessage.replied_at && (
                        <p className="text-xs text-muted-foreground mt-2">
                          Replied on: {new Date(selectedMessage.replied_at).toLocaleString()}
                        </p>
                      )}
                    </div>
                  </div>
                )}

                {/* Reply Form */}
                {selectedMessage.status !== 'replied' && (
                  <div>
                    <Label htmlFor="reply">Reply to Message:</Label>
                    <Textarea
                      id="reply"
                      placeholder="Type your reply here..."
                      value={replyText}
                      onChange={(e) => setReplyText(e.target.value)}
                      className="mt-2"
                      rows={4}
                    />
                    <Button
                      onClick={handleReply}
                      disabled={!replyText.trim() || isReplying}
                      className="mt-2"
                    >
                      {isReplying ? 'Sending...' : 'Send Reply'}
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="flex items-center justify-center h-64">
                <div className="text-center text-muted-foreground">
                  <MessageSquare className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Select a message to view details</p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}
