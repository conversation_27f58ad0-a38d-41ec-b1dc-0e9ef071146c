-- Create an admin user for testing
-- Run this in your Supabase SQL editor

-- First, create the auth user (you'll need to do this through the Supabase dashboard or auth API)
-- Then run this to create the profile:

INSERT INTO public.users (id, email, first_name, last_name, role, is_verified)
VALUES (
    '00000000-0000-0000-0000-000000000001', -- Replace with actual user ID from auth.users
    '<EMAIL>',
    'Admin',
    'User',
    'admin',
    true
) ON CONFLICT (id) DO UPDATE SET
    role = 'admin',
    is_verified = true;

-- To get the actual user ID, run this query in Supabase SQL editor:
-- SELECT id, email FROM auth.users WHERE email = '<EMAIL>'; 