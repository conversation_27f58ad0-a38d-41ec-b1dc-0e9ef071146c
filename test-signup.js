// Test script to verify signup functionality
// Run with: node test-signup.js

// Load environment variables from .env.local
require('dotenv').config({ path: '.env.local' })

const { createClient } = require('@supabase/supabase-js')

// Get values from environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://sagtwjbwgfgvzulnsmhc.supabase.co'
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseKey) {
    console.error('❌ NEXT_PUBLIC_SUPABASE_ANON_KEY not found in environment variables')
    console.log('Please set your environment variables and try again')
    process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseKey)

async function testUserSignup() {
    console.log('👤 Testing user signup...')

    try {
        const testEmail = `user${Date.now()}@gmail.com`
        const testPassword = 'testpassword123'

        console.log(`📝 Attempting to sign up user: ${testEmail}`)

        const { data, error } = await supabase.auth.signUp({
            email: testEmail,
            password: testPassword,
            options: {
                data: {
                    first_name: 'Test',
                    last_name: 'User',
                    role: 'user'
                }
            }
        })

        if (error) {
            console.error('❌ User signup failed:', error.message)
            return false
        }

        console.log('✅ User signup successful!')
        console.log('User ID:', data.user?.id)

        // Test profile creation
        await new Promise(resolve => setTimeout(resolve, 2000))

        const { data: profile, error: profileError } = await supabase
            .from('users')
            .select('*')
            .eq('id', data.user.id)
            .single()

        if (profileError) {
            console.error('❌ Profile fetch failed:', profileError.message)
            return false
        }

        console.log('✅ User profile created!')
        console.log('Profile:', profile)

        // Clean up
        console.log('🧹 Cleaning up test user...')
        await supabase.auth.admin.deleteUser(data.user.id)
        console.log('✅ Test user deleted')

        return true
    } catch (err) {
        console.error('❌ User signup test error:', err.message)
        return false
    }
}

async function testAgencySignup() {
    console.log('\n🏢 Testing agency signup...')

    try {
        const testEmail = `agency${Date.now()}@gmail.com`
        const testPassword = 'testpassword123'

        console.log(`📝 Attempting to sign up agency: ${testEmail}`)

        // First create the user account
        const { data: authData, error: authError } = await supabase.auth.signUp({
            email: testEmail,
            password: testPassword,
            options: {
                data: {
                    first_name: 'Test',
                    last_name: 'Agency',
                    role: 'agency'
                }
            }
        })

        if (authError || !authData.user) {
            console.error('❌ Agency auth signup failed:', authError?.message)
            return false
        }

        console.log('✅ Agency auth signup successful!')

        // Wait for profile creation
        await new Promise(resolve => setTimeout(resolve, 2000))

        // Check if agency already exists
        const { data: existingAgency } = await supabase
            .from('agencies')
            .select('*')
            .eq('user_id', authData.user.id)
            .single()

        let agencyData
        if (existingAgency) {
            console.log('✅ Agency record already exists')
            agencyData = existingAgency
        } else {
            // Create agency record
            const { data: newAgencyData, error: agencyError } = await supabase
                .from('agencies')
                .insert({
                    user_id: authData.user.id,
                    agency_name: 'Test Agency',
                    agency_description: 'A test agency for testing',
                    agency_address: '123 Test Street',
                    agency_phone: '+1234567890',
                    agency_email: testEmail,
                    agency_website: 'https://testagency.com',
                    is_approved: false
                })
                .select()
                .single()

            if (agencyError) {
                console.error('❌ Agency creation failed:', agencyError.message)
                return false
            }

            agencyData = newAgencyData
        }

        console.log('✅ Agency record created!')
        console.log('Agency:', agencyData)

        // Clean up
        console.log('🧹 Cleaning up test agency...')
        await supabase.auth.admin.deleteUser(authData.user.id)
        console.log('✅ Test agency deleted')

        return true
    } catch (err) {
        console.error('❌ Agency signup test error:', err.message)
        return false
    }
}

async function main() {
    console.log('🚀 Starting signup tests...\n')

    const userSignupOk = await testUserSignup()
    if (!userSignupOk) {
        console.log('\n❌ User signup test failed.')
        process.exit(1)
    }

    const agencySignupOk = await testAgencySignup()
    if (!agencySignupOk) {
        console.log('\n❌ Agency signup test failed.')
        process.exit(1)
    }

    console.log('\n🎉 All signup tests passed! Your authentication system is working correctly.')
}

main().catch(console.error) 