// Test script to simulate frontend authentication flow
require('dotenv').config({ path: '.env.local' })

const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://sagtwjbwgfgvzulnsmhc.supabase.co'
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseKey) {
    console.error('❌ NEXT_PUBLIC_SUPABASE_ANON_KEY not found')
    process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseKey)

// Simulate the auth context logic
async function simulateAuthContext() {
    console.log('🔍 Simulating auth context flow...\n')

    // Test admin login
    console.log('👑 Testing admin authentication flow...')
    await simulateUserAuth('<EMAIL>', 'Admin123!', 'admin')

    console.log('\n' + '='.repeat(60) + '\n')

    // Test agency login
    console.log('🏢 Testing agency authentication flow...')
    await simulateUserAuth('<EMAIL>', 'testpassword123', 'agency')

    console.log('\n' + '='.repeat(60) + '\n')

    // Test user login
    console.log('👤 Testing user authentication flow...')
    await simulateUserAuth('<EMAIL>', 'testpassword123', 'user')
}

async function simulateUserAuth(email, password, expectedRole) {
    try {
        console.log(`📝 Step 1: Signing in as ${expectedRole}: ${email}`)

        // Step 1: Sign in
        const { data, error } = await supabase.auth.signInWithPassword({
            email: email,
            password: password
        })

        if (error) {
            console.error('❌ Sign in failed:', error.message)
            return false
        }

        console.log('✅ Step 1: Sign in successful!')
        console.log('   User ID:', data.user?.id)
        console.log('   User Email:', data.user?.email)

        // Step 2: Fetch user profile (simulate fetchUserProfile)
        console.log('\n📝 Step 2: Fetching user profile...')
        const { data: profile, error: profileError } = await supabase
            .from('users')
            .select('*')
            .eq('id', data.user.id)
            .single()

        if (profileError) {
            console.error('❌ Step 2: Profile fetch failed:', profileError.message)
            return false
        }

        console.log('✅ Step 2: Profile fetched successfully!')
        console.log('   Profile Role:', profile.role)
        console.log('   Profile Email:', profile.email)
        console.log('   Profile Name:', profile.first_name, profile.last_name)

        // Step 3: Set user state (simulate setUser)
        console.log('\n📝 Step 3: Setting user state...')
        const userWithProfile = { ...data.user, ...profile }
        console.log('✅ Step 3: User state set!')
        console.log('   Combined User Role:', userWithProfile.role)
        console.log('   Combined User Email:', userWithProfile.email)

        // Step 4: Set admin cookie if needed
        if (profile.role === 'admin' || email === "<EMAIL>") {
            console.log('\n📝 Step 4: Setting admin cookie...')
            console.log('✅ Step 4: Admin cookie would be set in browser')
        }

        // Step 5: Handle redirect (simulate handleUserRedirect)
        console.log('\n📝 Step 5: Handling redirect...')
        const currentPath = '/auth'

        if (profile.role === 'admin' && !currentPath.startsWith('/admin')) {
            console.log('👑 Step 5: Should redirect admin to /admin/dashboard')
        } else if (profile.role === 'agency' && !currentPath.startsWith('/agency')) {
            console.log('🏢 Step 5: Should redirect agency to /agency/dashboard')
        } else if (profile.role === 'user' && !currentPath.startsWith('/user')) {
            console.log('👤 Step 5: Should redirect user to /user/dashboard')
        }

        // Step 6: Check if role matches expected
        if (profile.role === expectedRole) {
            console.log(`✅ Role verification: Expected ${expectedRole}, Got ${profile.role}`)
        } else {
            console.log(`❌ Role verification failed: Expected ${expectedRole}, Got ${profile.role}`)
        }

        // Step 7: Simulate auth page useEffect logic
        console.log('\n📝 Step 7: Simulating auth page useEffect...')
        const isAuthenticated = true
        const isLoading = false

        if (!isLoading && userWithProfile && isAuthenticated) {
            const isAdmin = userWithProfile.email === "<EMAIL>" || userWithProfile.role === "admin"
            if (isAdmin) {
                console.log('👑 Step 7: Auth page would redirect admin to /admin/dashboard')
            } else {
                const redirectPath = userWithProfile.role === "agency" ? "/agency/dashboard" : "/user/dashboard"
                console.log(`🏢 Step 7: Auth page would redirect ${userWithProfile.role} to ${redirectPath}`)
            }
        }

        // Sign out
        await supabase.auth.signOut()
        console.log('\n✅ Signed out successfully')

        return true
    } catch (err) {
        console.error('❌ Simulation error:', err.message)
        return false
    }
}

async function testMiddlewareLogic() {
    console.log('\n🔍 Testing middleware logic...\n')

    // Test admin session
    console.log('👑 Testing admin session with middleware...')
    const { data: adminData } = await supabase.auth.signInWithPassword({
        email: '<EMAIL>',
        password: 'Admin123!'
    })

    if (adminData.session) {
        console.log('✅ Admin session created')

        // Simulate middleware check
        const { data: adminUser } = await supabase
            .from('users')
            .select('role')
            .eq('id', adminData.session.user.id)
            .single()

        if (adminUser?.role === 'admin') {
            console.log('✅ Middleware would allow admin access')
        } else {
            console.log('❌ Middleware would deny admin access')
        }

        await supabase.auth.signOut()
    }

    // Test agency session
    console.log('\n🏢 Testing agency session with middleware...')
    const { data: agencyData } = await supabase.auth.signInWithPassword({
        email: '<EMAIL>',
        password: 'testpassword123'
    })

    if (agencyData.session) {
        console.log('✅ Agency session created')

        // Simulate middleware check
        const { data: agencyUser } = await supabase
            .from('users')
            .select('role')
            .eq('id', agencyData.session.user.id)
            .single()

        if (agencyUser?.role === 'agency') {
            console.log('✅ Middleware would allow agency access')
        } else {
            console.log('❌ Middleware would deny agency access')
        }

        await supabase.auth.signOut()
    }
}

async function main() {
    console.log('🚀 Starting frontend authentication simulation...\n')

    await simulateAuthContext()
    await testMiddlewareLogic()

    console.log('\n✅ Frontend authentication simulation completed!')
}

main().catch(console.error) 