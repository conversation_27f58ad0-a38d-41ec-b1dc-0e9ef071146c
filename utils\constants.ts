// Centralized constants for the application
export const ROUTES = {
  // Public routes
  home: '/',
  about: '/about',
  contact: '/contact',
  pricing: '/pricing',
  faqs: '/faqs',
  blogs: '/blogs',
  howItWorks: '/how-it-works',
  learnMore: '/learn-more',
  terms: '/terms',
  privacyPolicy: '/privacy-policy',

  // Auth routes
  auth: '/auth',
  verifyEmail: '/verify-email',

  // User routes
  userDashboard: '/user/dashboard',
  userSettings: '/user/settings',
  userNotifications: '/user/notifications',

  // Agency routes
  agencyDashboard: '/agency/dashboard',
  agencyDetails: '/agency/details',
  becomeHost: '/become-host',
  forAgencies: '/for-agencies',

  // Admin routes
  adminDashboard: '/admin/dashboard',
  adminLogin: '/admin-login',
  adminUsers: '/admin/users',
  adminAgencies: '/admin/agencies',
  adminCars: '/admin/cars',
  adminAnalytics: '/admin/analytics',
  adminMessages: '/admin/messages',
  adminSettings: '/admin/settings',
  adminPaymentRequests: '/admin/payment-requests',
  adminBlogs: '/admin/dashboard/blogs',

  // Listings routes
  listings: '/listings',
  carDetails: '/listings/car-details',

  // Other routes
  confirmReservation: '/confirm-reservation',
  payment: '/payment',
  accessDenied: '/access-denied',
} as const;

export const API_ENDPOINTS = {
  auth: {
    login: '/api/auth/login',
    register: '/api/auth/register',
    logout: '/api/auth/logout',
    verify: '/api/auth/verify',
  },
  users: {
    profile: '/api/users/profile',
    update: '/api/users/update',
  },
  agencies: {
    list: '/api/agencies',
    details: '/api/agencies/:id',
    create: '/api/agencies',
    update: '/api/agencies/:id',
  },
  cars: {
    list: '/api/cars',
    details: '/api/cars/:id',
    create: '/api/cars',
    update: '/api/cars/:id',
    delete: '/api/cars/:id',
  },
  bookings: {
    list: '/api/bookings',
    create: '/api/bookings',
    update: '/api/bookings/:id',
    cancel: '/api/bookings/:id/cancel',
  },
  gps: {
    location: '/api/gps/location',
    track: '/api/gps/track',
  },
  admin: {
    login: '/api/admin/login',
    dashboard: '/api/admin/dashboard',
    users: '/api/admin/users',
    agencies: '/api/admin/agencies',
    cars: '/api/admin/cars',
    messages: '/api/admin/messages',
    paymentRequests: '/api/admin/payment-requests',
    blogs: '/api/admin/blogs',
    settings: '/api/admin/settings',
  },
} as const;

// Car related constants
export const CAR_BRANDS = [
  "Audi", "BMW", "Chevrolet", "Citroen", "Dacia", "Fiat", "Ford", 
  "Honda", "Hyundai", "Infiniti", "Jaguar", "Jeep", "Kia", 
  "Land Rover", "Lexus", "Mazda", "Mercedes", "Mini", "Mitsubishi", 
  "Nissan", "Opel", "Peugeot", "Porsche", "Renault", "Skoda", 
  "Subaru", "Suzuki", "Toyota", "Volkswagen", "Volvo"
] as const;

export const CAR_COLORS = [
  "White", "Black", "Silver", "Gray", "Red", "Blue", 
  "Green", "Yellow", "Orange", "Purple"
] as const;

export const CAR_CATEGORIES = [
  "Economy", "Compact", "Mid-size", "Full-size", "Premium", 
  "Luxury", "SUV", "Van", "Convertible", "Sports"
] as const;

// Status constants
export const USER_ROLES = {
  USER: 'user',
  AGENCY: 'agency',
  ADMIN: 'admin',
} as const;

export const BOOKING_STATUS = {
  PENDING: 'pending',
  CONFIRMED: 'confirmed',
  ACTIVE: 'active',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled',
} as const;

export const CAR_STATUS = {
  AVAILABLE: 'available',
  BOOKED: 'booked',
  MAINTENANCE: 'maintenance',
  INACTIVE: 'inactive',
} as const;

export const AGENCY_STATUS = {
  PENDING: 'pending',
  VERIFIED: 'verified',
  SUSPENDED: 'suspended',
} as const;
