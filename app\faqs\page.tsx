'use client'

import { useState } from "react"
import { ChevronDown } from "lucide-react"
import { cn } from "@/lib/utils"
import { useI18n } from "@/i18n/i18n-provider"

const faqCategories = [
    {
        titleKey: "faq.categories.booking.title",
        faqs: [
            {
                questionKey: "faq.categories.booking.q1.question",
                answerKey: "faq.categories.booking.q1.answer"
            },
            {
                questionKey: "faq.categories.booking.q2.question",
                answerKey: "faq.categories.booking.q2.answer"
            },
            {
                questionKey: "faq.categories.booking.q3.question",
                answerKey: "faq.categories.booking.q3.answer"
            }
        ]
    },
    {
        titleKey: "faq.categories.requirements.title",
        faqs: [
            {
                questionKey: "faq.categories.requirements.q1.question",
                answerKey: "faq.categories.requirements.q1.answer"
            },
            {
                questionKey: "faq.categories.requirements.q2.question",
                answerKey: "faq.categories.requirements.q2.answer"
            },
            {
                questionKey: "faq.categories.requirements.q3.question",
                answerKey: "faq.categories.requirements.q3.answer"
            }
        ]
    },
    {
        titleKey: "faq.categories.pricing.title",
        faqs: [
            {
                questionKey: "faq.categories.pricing.q1.question",
                answerKey: "faq.categories.pricing.q1.answer"
            },
            {
                questionKey: "faq.categories.pricing.q2.question",
                answerKey: "faq.categories.pricing.q2.answer"
            },
            {
                questionKey: "faq.categories.pricing.q3.question",
                answerKey: "faq.categories.pricing.q3.answer"
            }
        ]
    },
    {
        titleKey: "faq.categories.vehicle.title",
        faqs: [
            {
                questionKey: "faq.categories.vehicle.q1.question",
                answerKey: "faq.categories.vehicle.q1.answer"
            },
            {
                questionKey: "faq.categories.vehicle.q2.question",
                answerKey: "faq.categories.vehicle.q2.answer"
            },
            {
                questionKey: "faq.categories.vehicle.q3.question",
                answerKey: "faq.categories.vehicle.q3.answer"
            }
        ]
    }
]

export default function FAQsPage() {
    const [openFaqs, setOpenFaqs] = useState<{ [key: string]: boolean }>({})
    const { t } = useI18n()

    const toggleFaq = (categoryIndex: number, faqIndex: number) => {
        const key = `${categoryIndex}-${faqIndex}`
        setOpenFaqs(prev => ({
            ...prev,
            [key]: !prev[key]
        }))
    }

    return (
        <div className="container mx-auto px-4 py-12">
            <div className="max-w-4xl mx-auto">
                <div className="text-center mb-12">
                    <h1 className="text-4xl font-bold mb-4">{t("faq.title")}</h1>
                    <p className="text-muted-foreground">
                        {t("faq.subtitle")}
                    </p>
                </div>

                <div className="space-y-8">
                    {faqCategories.map((category, categoryIndex) => (
                        <div key={categoryIndex} className="space-y-4">
                            <h2 className="text-2xl font-semibold">{t(category.titleKey)}</h2>
                            <div className="space-y-4">
                                {category.faqs.map((faq, faqIndex) => {
                                    const key = `${categoryIndex}-${faqIndex}`
                                    const isOpen = openFaqs[key]

                                    return (
                                        <div
                                            key={faqIndex}
                                            className="border rounded-lg overflow-hidden"
                                        >
                                            <button
                                                className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-muted/50 transition-colors"
                                                onClick={() => toggleFaq(categoryIndex, faqIndex)}
                                            >
                                                <span className="font-medium">{t(faq.questionKey)}</span>
                                                <ChevronDown
                                                    className={cn(
                                                        "h-5 w-5 transition-transform",
                                                        isOpen && "transform rotate-180"
                                                    )}
                                                />
                                            </button>
                                            {isOpen && (
                                                <div className="px-6 py-4 bg-muted/50">
                                                    <p className="text-muted-foreground">{t(faq.answerKey)}</p>
                                                </div>
                                            )}
                                        </div>
                                    )
                                })}
                            </div>
                        </div>
                    ))}
                </div>

                <div className="mt-12 text-center">
                    <p className="text-muted-foreground mb-4">
                        {t("faq.contactMessage")}
                    </p>
                    <a
                        href="/contact"
                        className="inline-flex items-center justify-center rounded-md bg-primary px-6 py-3 text-sm font-medium text-white shadow transition-colors hover:bg-primary/90"
                    >
                        {t("faq.contactButton")}
                    </a>
                </div>
            </div>
        </div>
    )
} 