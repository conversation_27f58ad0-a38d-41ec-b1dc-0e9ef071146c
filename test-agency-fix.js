const { createClient } = require('@supabase/supabase-js')

// Get values from environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://sagtwjbwgfgvzulnsmhc.supabase.co'
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseKey) {
    console.error('❌ NEXT_PUBLIC_SUPABASE_ANON_KEY not found in environment variables')
    console.log('Please set your environment variables and try again')
    process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseKey)

async function testAgencyFix() {
    console.log('🧪 Testing Agency Registration Fix...\n')

    try {
        // Test Agency Signup
        console.log('1️⃣ Testing Agency Signup...')
        const testAgencyEmail = `testagency${Date.now()}@example.com`
        const testAgencyPassword = 'testpassword123'

        const { data: agencySignupData, error: agencySignupError } = await supabase.auth.signUp({
            email: testAgencyEmail,
            password: testAgencyPassword,
            options: {
                data: {
                    first_name: 'Test',
                    last_name: 'Agency',
                    role: 'agency'
                }
            }
        })

        if (agencySignupError) {
            console.error('❌ Agency signup failed:', agencySignupError.message)
            return
        }

        console.log('✅ Agency signup successful!')
        console.log('   Agency User ID:', agencySignupData.user?.id)

        // Wait for profile creation
        await new Promise(resolve => setTimeout(resolve, 3000))

        // Test 2: Check Agency Profile
        console.log('\n2️⃣ Testing Agency Profile Creation...')
        const { data: agencyProfile, error: agencyProfileError } = await supabase
            .from('users')
            .select('*')
            .eq('id', agencySignupData.user.id)
            .single()

        if (agencyProfileError) {
            console.error('❌ Agency profile fetch failed:', agencyProfileError.message)
            return
        }

        console.log('✅ Agency profile created successfully!')
        console.log('   Profile:', {
            id: agencyProfile.id,
            email: agencyProfile.email,
            role: agencyProfile.role,
            first_name: agencyProfile.first_name,
            last_name: agencyProfile.last_name
        })

        // Test 3: Create Agency Record
        console.log('\n3️⃣ Testing Agency Record Creation...')
        const { data: agencyRecord, error: agencyRecordError } = await supabase
            .from('agencies')
            .insert({
                user_id: agencySignupData.user.id,
                agency_name: 'Test Agency',
                agency_description: 'A test agency',
                agency_email: testAgencyEmail,
                is_approved: false
            })
            .select()
            .single()

        if (agencyRecordError) {
            console.error('❌ Agency record creation failed:', agencyRecordError.message)

            // If it's a duplicate key error, try to fetch the existing record
            if (agencyRecordError.code === '23505' && agencyRecordError.message.includes('agencies_user_id_key')) {
                console.log('🔄 Duplicate key detected, fetching existing agency record...')
                const { data: existingAgency, error: fetchError } = await supabase
                    .from('agencies')
                    .select('*')
                    .eq('user_id', agencySignupData.user.id)
                    .single()

                if (existingAgency && !fetchError) {
                    console.log('✅ Found existing agency record!')
                    console.log('   Agency ID:', existingAgency.id)
                    console.log('   Agency Name:', existingAgency.agency_name)
                } else {
                    console.error('❌ Failed to fetch existing agency record:', fetchError?.message)
                }
            }
        } else {
            console.log('✅ Agency record created successfully!')
            console.log('   Agency ID:', agencyRecord.id)
        }

        // Test 4: Agency Login
        console.log('\n4️⃣ Testing Agency Login...')
        const { data: agencyLoginData, error: agencyLoginError } = await supabase.auth.signInWithPassword({
            email: testAgencyEmail,
            password: testAgencyPassword
        })

        if (agencyLoginError) {
            console.error('❌ Agency login failed:', agencyLoginError.message)
        } else {
            console.log('✅ Agency login successful!')
        }

        // Cleanup
        console.log('\n🧹 Cleaning up test data...')
        await supabase.auth.signOut()
        console.log('✅ Test cleanup completed')

        console.log('\n🎉 Agency Registration Fix Test Completed!')

    } catch (error) {
        console.error('❌ Test failed with unexpected error:', error)
    }
}

// Run the test
testAgencyFix() 