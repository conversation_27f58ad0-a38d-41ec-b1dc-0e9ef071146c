import { Skeleton } from "@/components/ui/skeleton"

export default function BlogLoading() {
    return (
        <div className="w-full py-8">
            <div className="container px-4 md:px-6">
                <Skeleton className="h-10 w-64 mb-8" />
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    {[1, 2, 3, 4, 5, 6].map((i) => (
                        <div key={i} className="space-y-4">
                            <Skeleton className="h-48 w-full rounded-lg" />
                            <Skeleton className="h-6 w-3/4" />
                            <Skeleton className="h-4 w-1/2" />
                            <Skeleton className="h-4 w-1/3" />
                        </div>
                    ))}
                </div>
            </div>
        </div>
    )
} 