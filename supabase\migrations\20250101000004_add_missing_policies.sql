-- Add missing RLS policies for proper user registration

-- Add INSERT policy for users table (if it doesn't exist)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'users' 
        AND policyname = 'Users can insert own profile'
    ) THEN
        CREATE POLICY "Users can insert own profile" ON public.users
            FOR INSERT WITH CHECK (auth.uid() = id);
    END IF;
END $$;

-- Add INSERT policy for agencies table (if it doesn't exist)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'agencies' 
        AND policyname = 'Agencies can insert own agency'
    ) THEN
        CREATE POLICY "Agencies can insert own agency" ON public.agencies
            FOR INSERT WITH CHECK (user_id = auth.uid());
    END IF;
END $$;

-- Add admin INSERT policies (if they don't exist)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'users' 
        AND policyname = 'Ad<PERSON> can insert profiles'
    ) THEN
        CREATE POLICY "Admins can insert profiles" ON public.users
            FOR INSERT WITH CHECK (
                EXISTS (
                    SELECT 1 FROM public.users 
                    WHERE id = auth.uid() AND role = 'admin'
                )
            );
    END IF;
END $$;

DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'agencies' 
        AND policyname = 'Admins can insert agencies'
    ) THEN
        CREATE POLICY "Admins can insert agencies" ON public.agencies
            FOR INSERT WITH CHECK (
                EXISTS (
                    SELECT 1 FROM public.users 
                    WHERE id = auth.uid() AND role = 'admin'
                )
            );
    END IF;
END $$; 