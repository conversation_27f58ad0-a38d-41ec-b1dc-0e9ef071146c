"use client"

import { CheckIcon } from "lucide-react"
import { useRouter } from "next/navigation"

const features = [
    "✔️ Verified agency profile",
    "✔️ List unlimited cars",
    "✔️ Fleet management dashboard",
    "✔️ Receive booking requests",
    "✔️ Accept/reject bookings",
    "✔️ Advanced analytics & reports",
    "✔️ Automated booking confirmations",
    "✔️ Customer review management",
    "✔️ Use custom coupon codes",
    "✔️ Get agency direct link to share",
]

export default function PricingPage() {
    const router = useRouter()

    return (
        <div className="bg-gray-50 py-12 sm:py-16">
            <div className="container mx-auto px-4">
                <div className="max-w-3xl mx-auto text-center">
                    <h2 className="text-4xl font-extrabold text-gray-900 sm:text-5xl">Choose Your Perfect Plan</h2>
                    <p className="mt-4 text-xl text-gray-500">Grow your car rental business with our comprehensive platform</p>
                </div>

                <div className="mt-10 text-center">
                    <div className="inline-block bg-gradient-to-r from-orange-500 to-yellow-500 rounded-lg p-1 shadow-lg">
                        <div className="bg-white rounded-md px-6 py-4">
                            <p className="text-xl font-bold text-orange-600">🎁FREE 2-WEEK TRIAL🎁</p>
                            <p className="text-bold text-black-600">Start your journey after registering your agency!</p>
                        </div>
                    </div>
                </div>

                <div className="mt-12 space-y-8 lg:grid lg:grid-cols-3 lg:gap-8 lg:space-y-0">
                    {/* Monthly Plan */}
                    <div className="bg-white border rounded-2xl shadow-sm divide-y divide-gray-200">
                        <div className="p-8 text-center">
                            <h3 className="text-2xl font-semibold text-gray-900">Monthly Plan</h3>
                            <p className="mt-4 flex items-baseline justify-center">
                                <span className="text-5xl font-extrabold text-gray-900 tracking-tight">Price on Request</span>
                                <span className="ml-1 text-xl font-semibold text-gray-500"></span>
                            </p>
                        </div>
                        <div className="pt-8 pb-8 px-6">
                            <h4 className="text-sm font-medium text-gray-900 uppercase">What's included</h4>
                            <ul className="mt-6 space-y-4">
                                {features.map((feature) => (
                                    <li key={feature} className="flex space-x-3">
                                        <span className="text-base text-gray-500">{feature}</span>
                                    </li>
                                ))}
                            </ul>
                        </div>
                        <div className="p-8">
                            <button
                                className="w-full bg-gradient-to-r from-orange-500 to-yellow-500 hover:from-orange-600 hover:to-yellow-600 text-white font-bold py-3 px-4 rounded-lg shadow-lg transform transition-transform hover:scale-105"
                                onClick={() => router.push('/payment?plan=monthly')}
                            >
                                Get Started
                            </button>
                        </div>
                    </div>

                    {/* 6-Month Plan */}
                    <div className="relative bg-white border-2 border-orange-500 rounded-2xl shadow-xl divide-y divide-gray-200">
                        <div className="absolute top-0 -translate-y-1/2 inset-x-0 text-center">
                            <span className="inline-flex items-center px-4 py-1 bg-orange-500 text-white text-sm font-semibold tracking-wider rounded-full">
                                Most Popular
                            </span>
                        </div>
                        <div className="p-8 text-center">
                            <h3 className="text-2xl font-semibold text-gray-900">6-Month Plan</h3>
                            <div className="flex items-center justify-center mt-4">
                                <p className="flex items-baseline">
                                    <span className="text-5xl font-extrabold text-gray-900 tracking-tight">Price on Request</span>
                                    <span className="ml-1 text-xl font-semibold text-gray-500"></span>
                                </p>
                                <span className="ml-4 inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                                    1 Month Free - Save 17%
                                </span>
                            </div>
                        </div>
                        <div className="pt-8 pb-8 px-6">
                            <h4 className="text-sm font-medium text-gray-900 uppercase">What's included</h4>
                            <ul className="mt-6 space-y-4">
                                {features.map((feature) => (
                                    <li key={feature} className="flex space-x-3">
                                        <span className="text-base text-gray-500">{feature}</span>
                                    </li>
                                ))}
                            </ul>
                        </div>
                        <div className="p-8">
                            <button
                                className="w-full bg-gradient-to-r from-orange-500 to-yellow-500 hover:from-orange-600 hover:to-yellow-600 text-white font-bold py-3 px-4 rounded-lg shadow-lg transform transition-transform hover:scale-105"
                                onClick={() => router.push('/payment?plan=6months')}
                            >
                                Choose 6-Month Plan
                            </button>
                        </div>
                    </div>

                    {/* Yearly Plan */}
                    <div className="bg-white border rounded-2xl shadow-sm divide-y divide-gray-200">
                        <div className="p-8 text-center">
                            <h3 className="text-2xl font-semibold text-gray-900">Yearly Plan</h3>
                            <div className="flex items-center justify-center mt-4">
                                <p className="flex items-baseline">
                                    <span className="text-5xl font-extrabold text-gray-900 tracking-tight">Price on Request</span>
                                    <span className="ml-1 text-xl font-semibold text-gray-500"></span>
                                </p>
                                <span className="ml-4 inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                                    2 Months Free - Save 17%
                                </span>
                            </div>
                        </div>
                        <div className="pt-8 pb-8 px-6">
                            <h4 className="text-sm font-medium text-gray-900 uppercase">What's included</h4>
                            <ul className="mt-6 space-y-4">
                                {features.map((feature) => (
                                    <li key={feature} className="flex space-x-3">
                                        <span className="text-base text-gray-500">{feature}</span>
                                    </li>
                                ))}
                            </ul>
                        </div>
                        <div className="p-8">
                            <button
                                className="w-full bg-gradient-to-r from-orange-500 to-yellow-500 hover:from-orange-600 hover:to-yellow-600 text-white font-bold py-3 px-4 rounded-lg shadow-lg transform transition-transform hover:scale-105"
                                onClick={() => router.push('/payment?plan=yearly')}
                            >
                                Choose Yearly Plan
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
} 