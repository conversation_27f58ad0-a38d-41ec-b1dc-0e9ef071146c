"use client"

import React, { createContext, useContext, useState, useEffect } from "react"

interface PaymentRequest {
    agencyName: string
    ownerName: string
    email: string
    phone: string
    plan: string
    receiptUrl: string
    date: string
    status: "pending" | "approved" | "rejected"
}

interface PaymentRequestsContextType {
    paymentRequests: PaymentRequest[]
    addPaymentRequest: (req: Omit<PaymentRequest, "status">) => void
}

const PaymentRequestsContext = createContext<PaymentRequestsContextType | undefined>(undefined)

export function PaymentRequestsProvider({ children }: { children: React.ReactNode }) {
    const [paymentRequests, setPaymentRequests] = useState<PaymentRequest[]>([])

    useEffect(() => {
        const saved = localStorage.getItem("paymentRequests")
        if (saved) setPaymentRequests(JSON.parse(saved))
    }, [])

    useEffect(() => {
        localStorage.setItem("paymentRequests", JSON.stringify(paymentRequests))
    }, [paymentRequests])

    const addPaymentRequest = (req: Omit<PaymentRequest, "status">) => {
        setPaymentRequests(prev => [
            { ...req, status: "pending" as const },
            ...prev
        ])
    }

    return (
        <PaymentRequestsContext.Provider value={{ paymentRequests, addPaymentRequest }}>
            {children}
        </PaymentRequestsContext.Provider>
    )
}

export function usePaymentRequests() {
    const ctx = useContext(PaymentRequestsContext)
    if (!ctx) throw new Error("usePaymentRequests must be used within a PaymentRequestsProvider")
    return ctx
} 