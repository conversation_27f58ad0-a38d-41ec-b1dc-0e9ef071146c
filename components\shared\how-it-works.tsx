"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Search, Car, CreditCard, MapPin, UserPlus, Calendar, BarChart } from "lucide-react"
import { useI18n } from "@/i18n/i18n-provider"
import Image from "next/image"

export function HowItWorks({ className = "" }: { className?: string }) {
  const { t } = useI18n()

  return (
    <section className={`w-full py-12 md:py-24 lg:py-32 bg-[#EFF0EF] ${className}`}>
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <div className="space-y-2">
            <h2 className="text-3xl font-bold tracking-tighter md:text-4xl">{t("howItWorks.title")}</h2>
            <p className="max-w-[900px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
              {t("howItWorks.subtitle")}
            </p>
          </div>
        </div>

        <div className="mx-auto max-w-5xl mt-12">
          <Tabs defaultValue="clients" className="w-full">
            <TabsList className="flex w-full mb-8 rounded-t-2xl overflow-hidden border border-gray-200">
              <TabsTrigger value="clients" className="flex-1 flex items-center justify-center gap-2 py-4 text-lg font-semibold rounded-tl-2xl rounded-bl-2xl transition-all data-[state=active]:bg-white data-[state=active]:text-black data-[state=inactive]:bg-[#EFF0EF] data-[state=inactive]:text-gray-500 border-none shadow-none">
                <Image src="/icons/user.svg" alt="User" width={24} height={24} className="w-6 h-6" />
                {t("howItWorks.forClients")}
              </TabsTrigger>
              <TabsTrigger value="agencies" className="flex-1 flex items-center justify-center gap-2 py-4 text-lg font-semibold rounded-tr-2xl rounded-br-2xl transition-all data-[state=active]:bg-white data-[state=active]:text-black data-[state=inactive]:bg-[#EFF0EF] data-[state=inactive]:text-gray-500 border-none shadow-none">
                <Image src="/icons/office.svg" alt="Office" width={24} height={24} className="w-6 h-6" />
                {t("howItWorks.forAgencies")}
              </TabsTrigger>
            </TabsList>

            <TabsContent value="clients" className="space-y-8">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <Card>
                  <CardHeader className="flex flex-col items-center space-y-2 pb-2">
                    <div className="p-2 bg-primary/10 rounded-full">
                      <Search className="h-6 w-6 text-primary" />
                    </div>
                    <CardTitle className="text-center text-xl">{t("howItWorks.clients.step1.title")}</CardTitle>
                  </CardHeader>
                  <CardContent className="text-center text-sm text-muted-foreground">
                    {t("howItWorks.clients.step1.description")}
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-col items-center space-y-2 pb-2">
                    <div className="p-2 bg-primary/10 rounded-full">
                      <Car className="h-6 w-6 text-primary" />
                    </div>
                    <CardTitle className="text-center text-xl">{t("howItWorks.clients.step2.title")}</CardTitle>
                  </CardHeader>
                  <CardContent className="text-center text-sm text-muted-foreground">
                    {t("howItWorks.clients.step2.description")}
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-col items-center space-y-2 pb-2">
                    <div className="p-2 bg-primary/10 rounded-full">
                      <CreditCard className="h-6 w-6 text-primary" />
                    </div>
                    <CardTitle className="text-center text-xl">{t("howItWorks.clients.step3.title")}</CardTitle>
                  </CardHeader>
                  <CardContent className="text-center text-sm text-muted-foreground">
                    {t("howItWorks.clients.step3.description")}
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-col items-center space-y-2 pb-2">
                    <div className="p-2 bg-primary/10 rounded-full">
                      <MapPin className="h-6 w-6 text-primary" />
                    </div>
                    <CardTitle className="text-center text-xl">{t("howItWorks.clients.step4.title")}</CardTitle>
                  </CardHeader>
                  <CardContent className="text-center text-sm text-muted-foreground">
                    {t("howItWorks.clients.step4.description")}
                  </CardContent>
                </Card>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle>{t("howItWorks.clients.benefits.title")}</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    <li className="flex items-center gap-2">
                      <div className="h-2 w-2 rounded-full bg-primary"></div>
                      {t("howItWorks.clients.benefits.item1")}
                    </li>
                    <li className="flex items-center gap-2">
                      <div className="h-2 w-2 rounded-full bg-primary"></div>
                      {t("howItWorks.clients.benefits.item2")}
                    </li>
                    <li className="flex items-center gap-2">
                      <div className="h-2 w-2 rounded-full bg-primary"></div>
                      {t("howItWorks.clients.benefits.item3")}
                    </li>
                    <li className="flex items-center gap-2">
                      <div className="h-2 w-2 rounded-full bg-primary"></div>
                      {t("howItWorks.clients.benefits.item4")}
                    </li>
                  </ul>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="agencies" className="space-y-8">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <Card>
                  <CardHeader className="flex flex-col items-center space-y-2 pb-2">
                    <div className="p-2 bg-primary/10 rounded-full">
                      <UserPlus className="h-6 w-6 text-primary" />
                    </div>
                    <CardTitle className="text-center text-xl">{t("howItWorks.agencies.step1.title")}</CardTitle>
                  </CardHeader>
                  <CardContent className="text-center text-sm text-muted-foreground">
                    {t("howItWorks.agencies.step1.description")}
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-col items-center space-y-2 pb-2">
                    <div className="p-2 bg-primary/10 rounded-full">
                      <Car className="h-6 w-6 text-primary" />
                    </div>
                    <CardTitle className="text-center text-xl">{t("howItWorks.agencies.step2.title")}</CardTitle>
                  </CardHeader>
                  <CardContent className="text-center text-sm text-muted-foreground">
                    {t("howItWorks.agencies.step2.description")}
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-col items-center space-y-2 pb-2">
                    <div className="p-2 bg-primary/10 rounded-full">
                      <Calendar className="h-6 w-6 text-primary" />
                    </div>
                    <CardTitle className="text-center text-xl">{t("howItWorks.agencies.step3.title")}</CardTitle>
                  </CardHeader>
                  <CardContent className="text-center text-sm text-muted-foreground">
                    {t("howItWorks.agencies.step3.description")}
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-col items-center space-y-2 pb-2">
                    <div className="p-2 bg-primary/10 rounded-full">
                      <BarChart className="h-6 w-6 text-primary" />
                    </div>
                    <CardTitle className="text-center text-xl">{t("howItWorks.agencies.step4.title")}</CardTitle>
                  </CardHeader>
                  <CardContent className="text-center text-sm text-muted-foreground">
                    {t("howItWorks.agencies.step4.description")}
                  </CardContent>
                </Card>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle>{t("howItWorks.agencies.benefits.title")}</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    <li className="flex items-center gap-2">
                      <div className="h-2 w-2 rounded-full bg-primary"></div>
                      {t("howItWorks.agencies.benefits.item1")}
                    </li>
                    <li className="flex items-center gap-2">
                      <div className="h-2 w-2 rounded-full bg-primary"></div>
                      {t("howItWorks.agencies.benefits.item2")}
                    </li>
                    <li className="flex items-center gap-2">
                      <div className="h-2 w-2 rounded-full bg-primary"></div>
                      {t("howItWorks.agencies.benefits.item3")}
                    </li>
                    <li className="flex items-center gap-2">
                      <div className="h-2 w-2 rounded-full bg-primary"></div>
                      {t("howItWorks.agencies.benefits.item4")}
                    </li>
                  </ul>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </section>
  )
}
