"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { DatePickerWithRange } from "@/components/shared/date-range-picker"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { VerificationBadge } from "@/components/ui/verification-badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
    StarIcon,
    MapPin,
    Calendar,
    Info,
    Shield,
    Fuel,
    Settings,
    Users,
    Bluetooth,
    Navigation,
    Music,
    Coffee,
    DollarSign,
} from "lucide-react"
import Link from "next/link"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { useParams } from "next/navigation"
import whatsappIcon from '@/public/icons/whatsapp-svg.svg';
import callIcon from '@/public/icons/call-svg.svg';
import Image from 'next/image';
import ReactCalendar from 'react-calendar'
import 'react-calendar/dist/Calendar.css'
import { useState, useEffect } from 'react'
import { useCurrency } from "@/contexts/currency-context"
import { supabase } from '@/lib/supabase/client'
import { CarAvailabilityCalendar } from '@/components/features/cars/car-availability-calendar'
import { AgencyOtherCars } from '@/components/features/cars/agency-other-cars'

const vehiclesData = [
    // Cars
    {
        id: "car1",
        type: "car",
        title: "Hyundai Creta 5-Seater 2022",
        priceDay: 520,
        priceWeek: 3450,
        priceMonth: 13650,
        location: "Agadir International Airport, Agadir",
        city: "Agadir",
        images: [
            "/creta1.jpg",
            "/creta2.jpg",
            "/creta3.jpg",
            "/creta4.jpg",
        ],
        rating: 4.9,
        reviews: 234,
        category: "Crossover",
        description: "Rent Hyundai Creta 5-Seater 2022 in Agadir. Premium crossover with automatic transmission, diesel engine, and all modern features.",
        features: {
            bodyType: "Crossover",
            brand: "Hyundai",
            model: "Creta 5-Seater",
            fuelType: "Diesel",
            gearbox: "Auto",
            people: 5,
            trunk: 2,
            year: 2022,
            ac: true,
            engineCapacity: "1.6 Ltrs",
            doors: 4,
            seats: 5,
            color: "Gray / Black",
            deposit: 5000,
            insuranceType: "Comprehensive",
            bluetooth: true,
            gps: true,
            usbCharger: true,
            sunroof: true,
        },
    },
    // Motorcycles
    {
        id: "moto1",
        type: "motorcycle",
        title: "Yamaha MT-07",
        price: 40,
        location: "Marrakech",
        images: [
            "/placeholder.svg?height=400&width=600",
            "/placeholder.svg?height=400&width=600",
        ],
        rating: 4.7,
        reviews: 98,
        category: "Naked",
        description: "Powerful and agile naked bike perfect for city riding and weekend adventures.",
        features: {
            engine: "689cc",
            transmission: "6-speed",
            type: "Naked",
            year: 2023,
        },
    },
    // Add more vehicles as needed
]

// Function to slugify agency name
function slugifyAgencyName(name: string) {
    return name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '')
}

// This will be replaced with real agency data from the component

// Function to get agency rental policy from settings
const getAgencyRentalPolicy = () => {
    const defaultPolicy = [
        "• Drivers must be 21+ years old with valid driver's license",
        "• Security deposit required (varies by vehicle)",
        "• Vehicle must be returned in the same condition as received",
        "• Fuel tank must be returned full",
        "• Late returns may incur additional charges",
        "• Free cancellation up to 48 hours before pickup",
        "• All cars delivered with full tank and should be returned the same"
    ]
    // Only access localStorage on client
    if (typeof window !== 'undefined') {
        const storedPolicy = localStorage.getItem("agencyRentalPolicy")
        if (storedPolicy) {
            return storedPolicy.split('\n').filter(line => line.trim())
        }
    }
    return defaultPolicy
}

// Define a features config at the top of the file:
const featuresConfig = [
    { key: 'bodyType', name: 'Body Type', icon: '/icons/car-body.svg', type: 'select' },
    { key: 'fuelType', name: 'Fuel Type', icon: '/icons/gas-svgrepo-com.svg', type: 'select' },
    { key: 'ac', name: 'AC', icon: '/icons/car-air-conditioning-svgrepo-com.svg', type: 'boolean' },
    { key: 'bluetooth', name: 'Bluetooth', icon: '/icons/bluetooth-on-svgrepo-com.svg', type: 'boolean' },
    { key: 'airbags', name: 'Airbags', icon: '/icons/airbags.svg', type: 'boolean' },
    { key: 'gps', name: 'GPS', icon: '/icons/gps.svg', type: 'boolean' },
    { key: 'usbCharger', name: 'USB Charger', icon: '/icons/usb.svg', type: 'boolean' },
    { key: 'sunroof', name: 'Sunroof', icon: '/icons/sunroof.svg', type: 'boolean' },
];

// Mock booked dates for demo
const bookedDates = [
    new Date(2025, 5, 3),
    new Date(2025, 5, 6),
    new Date(2025, 5, 11),
]

// Mock booking details for demo
const bookingDetails = {
    '2025-06-03': { name: 'John Doe', info: 'Booked by John Doe (3-6 June)' },
    '2025-06-06': { name: 'Jane Smith', info: 'Booked by Jane Smith (6 June)' },
    '2025-06-11': { name: 'Ali Ben', info: 'Booked by Ali Ben (11 June)' },
}

export default function VehicleDetailsPage() {
    const params = useParams()
    const vehicleId = params?.id as string
    const [vehicle, setVehicle] = useState<any>(null)
    const [agency, setAgency] = useState<any>(null)
    const [isLoading, setIsLoading] = useState(true)
    const [selectedDate, setSelectedDate] = useState<Date | null>(null)
    const [tooltip, setTooltip] = useState<string | null>(null)
    const { convert } = useCurrency()

    useEffect(() => {
        fetchVehicleData()
    }, [vehicleId])

    const fetchVehicleData = async () => {
        try {
            setIsLoading(true)

            // Fetch car details with agency information
            const { data: carData, error: carError } = await supabase
                .from('cars')
                .select(`
                    *,
                    agencies (
                        id,
                        agency_name,
                        agency_address,
                        agency_phone,
                        agency_email,
                        agency_website,
                        is_verified
                    )
                `)
                .eq('id', vehicleId)
                .single()

            if (carError) {
                console.error('Error fetching car:', carError)
                return
            }

            setVehicle(carData)
            setAgency(carData.agencies)
        } catch (error) {
            console.error('Error fetching vehicle data:', error)
        } finally {
            setIsLoading(false)
        }
    }

    if (isLoading) {
        return (
            <div className="container mx-auto px-4 py-6">
                <div className="flex items-center justify-center h-64">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
                </div>
            </div>
        )
    }

    if (!vehicle) {
        return <div className="container mx-auto px-4 py-6"><h1 className="text-2xl font-bold">Vehicle Not Found</h1></div>
    }

    // Create features object from vehicle data
    const features = {
        transmission: vehicle.transmission || 'Manual',
        year: vehicle.year || 2022,
        ac: true, // Default features
        engineCapacity: vehicle.engine_capacity || '1.6L',
        bluetooth: true,
        airbags: true,
        gps: vehicle.features?.includes('GPS') || false,
        usbCharger: true,
        sunroof: vehicle.features?.includes('Sunroof') || false,
        brand: vehicle.make || 'Unknown',
        model: vehicle.model || 'Unknown',
        color: vehicle.color || 'Unknown'
    }

    // In the component, map the features object to an array:
    const featuresArray = featuresConfig.map(f => ({
        ...f,
        value: features[f.key as keyof typeof features]
    })).slice(0, 8);

    return (
        <div className="container mx-auto px-4 py-6 flex flex-col lg:flex-row gap-8">
            {/* Main Content */}
            <div className="flex-1">
                <div className="flex items-center gap-3 mb-2">
                    <img
                        src={`/car-logos/${features.brand?.toLowerCase() || 'default'}.svg`}
                        alt={features.brand || 'Car Logo'}
                        className="h-10 w-10 object-contain"
                        onError={(e) => {
                            // Prevent infinite loop by checking if we're already using the default
                            if (e.currentTarget.src !== '/car-logos/default.svg') {
                                e.currentTarget.src = '/car-logos/default.svg'
                            } else {
                                // If default also fails, hide the image
                                e.currentTarget.style.display = 'none'
                            }
                        }}
                    />
                    <span className="text-2xl font-bold">{vehicle.make} {vehicle.model} {vehicle.year}</span>
                </div>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-2 mb-2">
                    {(vehicle.images || ['/placeholder.svg?height=400&width=600']).map((img: string, i: number) => (
                        <img key={i} src={img} alt={`${vehicle.make} ${vehicle.model} image ${i + 1}`} className="rounded-lg w-full h-40 object-cover" />
                    ))}
                </div>

                <div className="flex items-center gap-2 text-muted-foreground mb-6">
                    <MapPin className="h-4 w-4" />
                    <span>{vehicle.address || agency?.agency_address || 'Location not specified'}</span>
                </div>

                <Card className="mb-6">
                    <CardHeader>
                        <CardTitle>Car Overview</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                            {featuresArray.map((feature, idx) => (
                                <div key={feature.key} className="flex items-center gap-2">
                                    {feature.icon && <Image src={feature.icon} alt={feature.name} width={24} height={24} />}
                                    <span className="font-medium">{feature.name}:</span>
                                    {feature.type === 'boolean' ? (
                                        feature.value ? <span className="text-green-600">✔</span> : <span className="text-red-500">✘</span>
                                    ) : (
                                        <span>{feature.value || 'N/A'}</span>
                                    )}
                                </div>
                            ))}
                        </div>
                    </CardContent>
                </Card>

                <Card className="mb-6">
                    <CardHeader>
                        <CardTitle>Guarantee Deposit</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="flex items-center gap-2">
                            <DollarSign className="h-5 w-5 text-primary" />
                            <span className="font-medium">Deposit Amount:</span>
                            <span>{convert(vehicle.features.deposit || 0) || 'N/A'}</span>
                        </div>
                        <p className="text-sm text-muted-foreground mt-2">This amount is required as a refundable security deposit for this car.</p>
                    </CardContent>
                </Card>

                <Card className="mb-6">
                    <CardHeader>
                        <CardTitle>Rental Policy</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <ul className="list-disc pl-5 space-y-1">
                            {getAgencyRentalPolicy().map((policy, idx) => (
                                <li key={idx}>{policy}</li>
                            ))}
                        </ul>
                    </CardContent>
                </Card>

                {/* Reviews & Ratings Section */}
                <Card>
                    <CardHeader>
                        <CardTitle>Reviews & Ratings</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {/* Example review, replace with real data */}
                            <div className="border-b pb-4">
                                <div className="flex items-center gap-2 mb-1">
                                    <span className="font-semibold">John Doe</span>
                                    <span className="text-xs text-muted-foreground">April 2024</span>
                                </div>
                                <div className="flex items-center gap-1 mb-1">
                                    {[...Array(5)].map((_, i) => (
                                        <StarIcon key={i} className={`h-4 w-4 ${i < 4 ? 'text-yellow-500' : 'text-gray-300'}`} />
                                    ))}
                                </div>
                                <div className="text-sm">Great car and service. Highly recommend!</div>
                            </div>
                            {/* Add more reviews as needed */}
                        </div>
                    </CardContent>
                </Card>

                {/* Recommended Cars Section */}
                {/* {recommendedCars.length > 0 && (
                    <div className="mt-12">
                        <h2 className="text-2xl font-bold mb-4">Recommended Cars from {agencyInfo.name}</h2>
                        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
                            {recommendedCars.map((car) => (
                                <Card key={car.id} className="hover:shadow-lg transition-shadow">
                                    <CardContent className="p-4">
                                        <Link href={`/listings/car-details/${car.id}`} className="block">
                                            <img src={car.images[0]} alt={car.title} className="w-full h-40 object-cover rounded-lg mb-2" />
                                            <div className="font-semibold text-lg mb-1">{car.title}</div>
                                            <div className="text-sm text-muted-foreground mb-1">{car.location}</div>
                                            <div className="text-primary font-bold">MAD {car.priceDay || car.price || 0} / day</div>
                                        </Link>
                                    </CardContent>
                                </Card>
                            ))}
                        </div>
                    </div>
                )} */}
            </div>
            {/* Right Fixed Container */}
            <div className="w-full lg:w-[340px] flex-shrink-0">
                <div className="bg-white rounded-xl shadow-lg p-6 flex flex-col gap-4 border">
                    {/* Agency Avatar and Name (clickable) */}
                    <Link href={`/agency/details/${agency?.id}`} className="flex flex-col items-center gap-2 group hover:underline mb-2">
                        <Avatar className="h-16 w-16">
                            <AvatarImage src="/placeholder.svg?height=100&width=100" />
                            <AvatarFallback>{agency?.agency_name?.slice(0, 2).toUpperCase() || 'AG'}</AvatarFallback>
                        </Avatar>
                        <span className="font-bold text-xl group-hover:underline text-center">{agency?.agency_name || 'Agency Name'}</span>
                        <VerificationBadge isVerified={agency?.is_verified || false} size="sm" />
                    </Link>
                    {/* Contact the agency */}
                    <div className="text-center text-muted-foreground text-sm mb-2">Contact the Agency</div>
                    {/* WhatsApp and Call Buttons (SVG icons) */}
                    <div className="flex gap-3 mb-2">
                        <a
                            href={`https://wa.me/${(agency?.agency_phone || '').replace(/[^\d]/g, "")}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex-1 flex items-center justify-center gap-2 rounded-lg py-2 font-semibold text-white bg-[#25D366] hover:bg-[#1ebe57] transition"
                        >
                            <Image src={whatsappIcon} alt="WhatsApp" width={20} height={20} /> WhatsApp
                        </a>
                        <a
                            href={`tel:${agency?.agency_phone || ''}`}
                            className="flex-1 flex items-center justify-center gap-2 rounded-lg py-2 font-semibold text-white bg-[#FF8533] hover:bg-[#FFB733] transition"
                        >
                            <Image src={callIcon} alt="Call" width={20} height={20} /> Call
                        </a>
                    </div>
                    {/* Book directly from here */}
                    <div className="text-center text-muted-foreground text-sm mb-2">Book directly from here</div>
                    {/* Book Now Button */}
                    <Button className="w-full text-lg py-2" size="lg" asChild>
                        <Link href="/confirm-reservation">Book Now</Link>
                    </Button>
                    {/* Pricing (smaller, at the bottom) */}
                    <div className="flex flex-wrap gap-2 justify-center mt-4">
                        <div className="bg-primary text-white rounded-lg px-4 py-2 font-bold text-sm">{convert(vehicle.daily_rate || 0)} / day</div>
                        <div className="bg-secondary text-white rounded-lg px-4 py-2 font-bold text-sm">{convert((vehicle.daily_rate || 0) * 7)} / week</div>
                        <div className="bg-accent text-white rounded-lg px-4 py-2 font-bold text-sm">{convert((vehicle.daily_rate || 0) * 30)} / month</div>
                    </div>
                    {/* Car Availability Calendar */}
                    <CarAvailabilityCalendar carId={vehicleId} className="mt-6" />

                    {/* Other Cars from Same Agency */}
                    {agency && (
                        <AgencyOtherCars
                            currentCarId={vehicleId}
                            agencyId={agency.id}
                            className="mt-6"
                        />
                    )}
                </div>
            </div>
        </div>
    )
} 