"use client"

import { But<PERSON> } from "@/components/ui/button"
import { useRouter } from "next/navigation"
import { useAuth } from "@/contexts/auth-context"

export default function AccessDenied() {
  const router = useRouter()
  const { user } = useAuth()

  return (
    <div className="flex flex-col items-center justify-center min-h-[70vh] px-4">
      <h1 className="text-4xl font-bold mb-4">Access Denied</h1>
      <p className="text-xl mb-8 text-center">You don't have permission to access this page.</p>
      <div className="flex flex-col sm:flex-row gap-4">
        <Button onClick={() => router.push("/")}>Return to Home</Button>
        {user?.role === "user" ? (
          <Button onClick={() => router.push("/user/dashboard")} variant="outline">
            Go to User Dashboard
          </Button>
        ) : (
          <Button onClick={() => router.push("/auth")} variant="outline">
            Sign In
          </Button>
        )}
      </div>
    </div>
  )
}
