"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Calendar, MapPin, Users, DollarSign, Phone, Mail, CheckCircle, XCircle } from "lucide-react"
import { useParams, useRouter } from "next/navigation"
import { toast } from "sonner"
import { AuthGuard } from "@/components/features/auth/auth-guard"

// Sample booking data
const bookingData = {
  id: "REQ-001",
  customerName: "<PERSON>",
  customerPhone: "+212 612345678",
  carName: "Tesla Model 3",
  pickupLocation: "Marrakech",
  dropoffLocation: "Marrakech",
  pickupDate: "Apr 1, 2025",
  dropoffDate: "Apr 8, 2025",
  withChauffeur: true,
  withChildSeat: true,
  paymentMethod: "Credit Card",
  totalAmount: 8850,
  status: "pending",
  requestDate: "Mar 31, 2025",
  idCardImage: "/placeholder.svg?height=300&width=500",
  licenseImage: "/placeholder.svg?height=300&width=500",
  firstName: "Ahmed",
  lastName: "Khalid",
  email: "<EMAIL>",
  carImage: "/placeholder.svg?height=400&width=600",
  insuranceType: "comprehensive",
  deposit: 8000,
}

export default function BookingDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const bookingId = params.id as string

  const [booking, setBooking] = useState(bookingData)
  const [isProcessing, setIsProcessing] = useState(false)

  useEffect(() => {
    // In a real app, you would fetch the booking data based on the ID
    console.log(`Fetching booking with ID: ${bookingId}`)
  }, [bookingId])

  const handleAcceptBooking = () => {
    setIsProcessing(true)

    // Simulate API call
    setTimeout(() => {
      setBooking({ ...booking, status: "accepted" })
      setIsProcessing(false)
      toast.success("Booking request accepted successfully")
    }, 1500)
  }

  const handleDenyBooking = () => {
    setIsProcessing(true)

    // Simulate API call
    setTimeout(() => {
      setBooking({ ...booking, status: "denied" })
      setIsProcessing(false)
      toast.error("Booking request denied")
    }, 1500)
  }

  return (
    <AuthGuard requiredRole="agency">
      <div className="container py-10">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold">Booking Details</h1>
            <p className="text-muted-foreground">
              Request ID: {booking.id} • {booking.requestDate}
            </p>
          </div>
          <div className="flex items-center gap-2 mt-4 md:mt-0">
            <Button variant="outline" onClick={() => router.back()}>
              Back to Dashboard
            </Button>
            <Badge
              className={
                booking.status === "pending"
                  ? "bg-yellow-500"
                  : booking.status === "accepted"
                    ? "bg-green-500"
                    : "bg-red-500"
              }
            >
              {booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}
            </Badge>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Customer Information</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="flex items-start gap-2">
                      <Users className="h-5 w-5 text-muted-foreground mt-0.5" />
                      <div>
                        <p className="font-medium">Full Name</p>
                        <p className="text-muted-foreground">
                          {booking.firstName} {booking.lastName}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start gap-2">
                      <Phone className="h-5 w-5 text-muted-foreground mt-0.5" />
                      <div>
                        <p className="font-medium">Phone Number</p>
                        <p className="text-muted-foreground">{booking.customerPhone}</p>
                      </div>
                    </div>

                    <div className="flex items-start gap-2">
                      <Mail className="h-5 w-5 text-muted-foreground mt-0.5" />
                      <div>
                        <p className="font-medium">Email</p>
                        <p className="text-muted-foreground">{booking.email}</p>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-start gap-2">
                      <Calendar className="h-5 w-5 text-muted-foreground mt-0.5" />
                      <div>
                        <p className="font-medium">Rental Period</p>
                        <p className="text-muted-foreground">
                          {booking.pickupDate} to {booking.dropoffDate}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start gap-2">
                      <MapPin className="h-5 w-5 text-muted-foreground mt-0.5" />
                      <div>
                        <p className="font-medium">Locations</p>
                        <p className="text-muted-foreground">Pickup: {booking.pickupLocation}</p>
                        <p className="text-muted-foreground">Dropoff: {booking.dropoffLocation}</p>
                      </div>
                    </div>

                    <div className="flex items-start gap-2">
                      <DollarSign className="h-5 w-5 text-muted-foreground mt-0.5" />
                      <div>
                        <p className="font-medium">Payment Details</p>
                        <p className="text-muted-foreground">
                          {booking.paymentMethod} • Total: {booking.totalAmount} MAD
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Uploaded Documents</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <p className="font-medium">ID Card</p>
                    <div className="border rounded-md overflow-hidden">
                      <img src={booking.idCardImage || "/placeholder.svg"} alt="ID Card" className="w-full h-auto" />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <p className="font-medium">Driving License</p>
                    <div className="border rounded-md overflow-hidden">
                      <img
                        src={booking.licenseImage || "/placeholder.svg"}
                        alt="Driving License"
                        className="w-full h-auto"
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {booking.status === "pending" && (
              <div className="flex justify-end gap-4">
                <Button
                  variant="outline"
                  className="border-red-500 text-red-500 hover:bg-red-500 hover:text-white"
                  onClick={handleDenyBooking}
                  disabled={isProcessing}
                >
                  <XCircle className="mr-2 h-4 w-4" />
                  Deny Request
                </Button>
                <Button
                  className="bg-green-600 hover:bg-green-700"
                  onClick={handleAcceptBooking}
                  disabled={isProcessing}
                >
                  <CheckCircle className="mr-2 h-4 w-4" />
                  Accept Request
                </Button>
              </div>
            )}
          </div>

          <div>
            <Card>
              <CardHeader>
                <CardTitle>Car Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="rounded-md overflow-hidden">
                  <img src={booking.carImage || "/placeholder.svg"} alt={booking.carName} className="w-full h-auto" />
                </div>

                <div className="space-y-4">
                  <div>
                    <p className="font-medium">{booking.carName}</p>
                    <p className="text-sm text-muted-foreground">Electric Sedan</p>
                  </div>

                  <div className="space-y-2 pt-4 border-t">
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Insurance Type</span>
                      <span className="text-sm font-medium capitalize">{booking.insuranceType}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Security Deposit</span>
                      <span className="text-sm font-medium">{booking.deposit} MAD</span>
                    </div>
                  </div>

                  <div className="space-y-2 pt-4 border-t">
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Additional Services</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Chauffeur</span>
                      <span className="text-sm font-medium">{booking.withChauffeur ? "Yes (+300 MAD/day)" : "No"}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Child Seat</span>
                      <span className="text-sm font-medium">{booking.withChildSeat ? "Yes (+100 MAD/day)" : "No"}</span>
                    </div>
                  </div>

                  <div className="pt-4 border-t">
                    <div className="flex justify-between mb-2">
                      <span>750 MAD x 7 days</span>
                      <span>5,250 MAD</span>
                    </div>
                    <div className="flex justify-between mb-2">
                      <span>Chauffeur service</span>
                      <span>2,100 MAD</span>
                    </div>
                    <div className="flex justify-between mb-2">
                      <span>Child seat</span>
                      <span>700 MAD</span>
                    </div>
                    <div className="flex justify-between mb-2">
                      <span>Service fee</span>
                      <span>800 MAD</span>
                    </div>
                    <div className="flex justify-between font-bold pt-2 border-t mt-2">
                      <span>Total</span>
                      <span>{booking.totalAmount} MAD</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </AuthGuard>
  )
}
