"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
    Car,
    Calendar,
    MessageSquare,
    FileText,
    DollarSign,
    Settings,
    Users,
    Star,
    TrendingUp,
    Shield,
    MapPin,
    Clock,
    CheckCircle,
    BarChart3,
    Zap,
    Globe,
    Building2
} from "lucide-react"
import Link from "next/link"
import Image from "next/image"
import { useRouter } from "next/navigation"
import { useI18n } from "@/i18n/i18n-provider"

export default function ForAgenciesPage() {
    const router = useRouter()
    const { t } = useI18n()

    const features = [
        {
            title: t("forAgencies.features.carManagement.title"),
            description: t("forAgencies.features.carManagement.description"),
            image: "/images/partner-section.png",
            icon: Car,
            benefits: [
                t("forAgencies.features.carManagement.benefit1"),
                t("forAgencies.features.carManagement.benefit2"),
                t("forAgencies.features.carManagement.benefit3"),
                t("forAgencies.features.carManagement.benefit4"),
                t("forAgencies.features.carManagement.benefit5")
            ]
        },
        {
            title: t("forAgencies.features.bookingSystem.title"),
            description: t("forAgencies.features.bookingSystem.description"),
            image: "/images/partner2.png",
            icon: Calendar,
            benefits: [
                t("forAgencies.features.bookingSystem.benefit1"),
                t("forAgencies.features.bookingSystem.benefit2"),
                t("forAgencies.features.bookingSystem.benefit3"),
                t("forAgencies.features.bookingSystem.benefit4"),
                t("forAgencies.features.bookingSystem.benefit5")
            ]
        },
        {
            title: t("forAgencies.features.reviews.title"),
            description: t("forAgencies.features.reviews.description"),
            image: "/images/partner.png",
            icon: Star,
            benefits: [
                t("forAgencies.features.reviews.benefit1"),
                t("forAgencies.features.reviews.benefit2"),
                t("forAgencies.features.reviews.benefit3"),
                t("forAgencies.features.reviews.benefit4"),
                t("forAgencies.features.reviews.benefit5")
            ]
        },
        {
            title: t("forAgencies.features.gps.title"),
            description: t("forAgencies.features.gps.description"),
            image: "/images/image1.png",
            icon: MapPin,
            benefits: [
                t("forAgencies.features.gps.benefit1"),
                t("forAgencies.features.gps.benefit2"),
                t("forAgencies.features.gps.benefit3"),
                t("forAgencies.features.gps.benefit4"),
                t("forAgencies.features.gps.benefit5")
            ]
        },
        {
            title: t("forAgencies.features.coupons.title"),
            description: t("forAgencies.features.coupons.description"),
            image: "/images/image2.png",
            icon: DollarSign,
            benefits: [
                t("forAgencies.features.coupons.benefit1"),
                t("forAgencies.features.coupons.benefit2"),
                t("forAgencies.features.coupons.benefit3"),
                t("forAgencies.features.coupons.benefit4"),
                t("forAgencies.features.coupons.benefit5")
            ]
        },
        {
            title: t("forAgencies.features.analytics.title"),
            description: t("forAgencies.features.analytics.description"),
            image: "/images/image3.png",
            icon: BarChart3,
            benefits: [
                t("forAgencies.features.analytics.benefit1"),
                t("forAgencies.features.analytics.benefit2"),
                t("forAgencies.features.analytics.benefit3"),
                t("forAgencies.features.analytics.benefit4"),
                t("forAgencies.features.analytics.benefit5")
            ]
        }
    ]

    const stats = [
        { label: t("forAgencies.stats.activeAgencies"), value: "500+", icon: Building2 },
        { label: t("forAgencies.stats.totalRevenue"), value: "MAD 2.5M+", icon: TrendingUp },
        { label: t("forAgencies.stats.customerSatisfaction"), value: "98%", icon: Star },
        { label: t("forAgencies.stats.averageRating"), value: "4.8/5", icon: CheckCircle }
    ]

    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
            {/* Hero Section */}
            <section className="relative overflow-hidden bg-gradient-to-r from-orange-600 via-yelow-600 to-purple-600 text-white">
                {/* bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-700 */}
                <div className="absolute inset-0 bg-black/20"></div>
                <div className="container mx-auto px-4 py-10 relative z-10">
                    <div className="max-w-4xl mx-auto text-center">
                        <Badge variant="secondary" className="mb-6 bg-white/20 text-white border-white/30">
                            {t("forAgencies.hero.badge")}
                        </Badge>
                        <h1 className="text-5xl md:text-6xl font-bold mb-6 leading-tight">
                            {t("forAgencies.hero.title")}
                        </h1>
                        <p className="text-xl md:text-2xl mb-8 text-blue-100 leading-relaxed">
                            {t("forAgencies.hero.subtitle")}
                        </p>
                        <div className="flex flex-col sm:flex-row gap-4 justify-center">
                            <Button size="lg" className="bg-white text-blue-600 hover:bg-blue-50 text-lg px-8 py-3 transition-all duration-300 transform hover:scale-105 hover:shadow-lg" onClick={() => router.push('/pricing')}>
                                <Zap className="mr-2 h-5 w-5" />
                                {t("forAgencies.hero.getStarted")}
                            </Button>
                            <Button size="lg" variant="outline" className="border-white text-black hover:bg-gradient-to-r from-blue-600 to-purple-600 hover:text-white text-lg px-8 py-3 transition-all duration-300 transform hover:scale-105 hover:shadow-lg hover:border-white/80">
                                <Globe className="mr-2 h-5 w-5" />
                                {t("forAgencies.hero.viewDemo")}
                            </Button>
                        </div>
                    </div>
                </div>
            </section>

            {/* Stats Section */}
            <section className="py-16 bg-white">
                <div className="container mx-auto px-4">
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
                        {stats.map((stat, index) => (
                            <div key={index} className="text-center">
                                <div className="flex justify-center mb-4">
                                    <div className="p-3 bg-blue-100 rounded-full">
                                        <stat.icon className="h-8 w-8 text-blue-600" />
                                    </div>
                                </div>
                                <div className="text-3xl font-bold text-gray-900 mb-2">{stat.value}</div>
                                <div className="text-gray-600">{stat.label}</div>
                            </div>
                        ))}
                    </div>
                </div>
            </section>

            {/* Features Section */}
            <section className="py-20">
                <div className="container mx-auto px-4">
                    <div className="text-center mb-16">
                        <h2 className="text-4xl font-bold text-gray-900 mb-4">
                            {t("forAgencies.features.title")}
                        </h2>
                        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                            {t("forAgencies.features.subtitle")}
                        </p>
                    </div>

                    <div className="space-y-32">
                        {features.map((feature, index) => (
                            <div key={index} className={`flex flex-col ${index % 2 === 0 ? 'lg:flex-row' : 'lg:flex-row-reverse'} items-center gap-12`}>
                                {/* Content */}
                                <div className="flex-1 space-y-6">
                                    <div className="flex items-center gap-3">
                                        <div className="p-3 bg-blue-100 rounded-full">
                                            <feature.icon className="h-8 w-8 text-blue-600" />
                                        </div>
                                        <h3 className="text-3xl font-bold text-gray-900">{feature.title}</h3>
                                    </div>
                                    <p className="text-lg text-gray-600 leading-relaxed">
                                        {feature.description}
                                    </p>
                                    <div className="space-y-3">
                                        {feature.benefits.map((benefit, benefitIndex) => (
                                            <div key={benefitIndex} className="flex items-center gap-3">
                                                <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0" />
                                                <span className="text-gray-700">{benefit}</span>
                                            </div>
                                        ))}
                                    </div>
                                </div>

                                {/* Image */}
                                <div className="flex-1">
                                    <div className="relative">
                                        <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-2xl transform rotate-3"></div>
                                        <div className="relative bg-white rounded-2xl shadow-2xl overflow-hidden transform -rotate-1 hover:rotate-0 transition-transform duration-300">
                                            <Image
                                                src={feature.image}
                                                alt={feature.title}
                                                width={600}
                                                height={400}
                                                className="w-full h-auto object-cover"
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </section>

            {/* CTA Section */}
            <section className="py-20 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
                <div className="container mx-auto px-4 text-center">
                    <h2 className="text-4xl font-bold mb-6">
                        {t("forAgencies.cta.title")}
                    </h2>
                    <p className="text-xl mb-8 text-blue-100 max-w-2xl mx-auto">
                        {t("forAgencies.cta.subtitle")}
                    </p>
                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                        <Button size="lg" className="bg-white text-blue-600 hover:bg-blue-50 text-lg px-8 py-3 transition-all duration-300 transform hover:scale-105 hover:shadow-lg" onClick={() => router.push('/pricing')}>
                            <Shield className="mr-2 h-5 w-5" />
                            {t("forAgencies.cta.register")}
                        </Button>
                        <Button size="lg" variant="outline" className="border-white text-black hover:bg-gradient-to-r from-blue-600 to-purple-600 text-black text-lg px-8 py-3 transition-all duration-300 transform hover:scale-105 hover:shadow-lg hover:border-white/80">
                            <Clock className="mr-2 h-5 w-5" />
                            {t("forAgencies.cta.scheduleDemo")}
                        </Button>
                    </div>
                </div>
            </section>
        </div>
    )
} 