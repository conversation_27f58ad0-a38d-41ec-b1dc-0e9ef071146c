// Custom hook for authentication
import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import authService from '../services/auth.service';
import type { User, LoginCredentials, RegisterCredentials, UpdateProfileData } from '../types/shared/auth';

interface UseAuthReturn {
    user: User | null;
    isAuthenticated: boolean;
    isLoading: boolean;
    error: string | null;
    login: (credentials: LoginCredentials) => Promise<void>;
    register: (credentials: RegisterCredentials) => Promise<void>;
    logout: () => Promise<void>;
    updateProfile: (data: UpdateProfileData) => Promise<void>;
    clearError: () => void;
}

export function useAuth(): UseAuthReturn {
    const [user, setUser] = useState<User | null>(null);
    const [isAuthenticated, setIsAuthenticated] = useState(false);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    const router = useRouter();

    // Initialize auth state
    useEffect(() => {
        const initializeAuth = async () => {
            try {
                setIsLoading(true);

                // Check if user is authenticated
                if (authService.isAuthenticated()) {
                    const storedUser = authService.getStoredUser();
                    if (storedUser) {
                        setUser(storedUser);
                        setIsAuthenticated(true);
                    } else {
                        // Try to get fresh user data
                        const freshUser = await authService.getProfile();
                        setUser(freshUser);
                        setIsAuthenticated(true);
                    }
                }
            } catch (err) {
                console.error('Auth initialization failed:', err);
                // Clear invalid auth data
                authService.logout();
            } finally {
                setIsLoading(false);
            }
        };

        initializeAuth();
    }, []);

    // Login function
    const login = useCallback(async (credentials: LoginCredentials) => {
        try {
            setIsLoading(true);
            setError(null);

            const response = await authService.login(credentials);

            // Store user data
            authService.storeUserData(response.user, response.token, response.refreshToken);

            setUser(response.user);
            setIsAuthenticated(true);

            // Redirect based on user role
            if (response.user.role === 'admin') {
                router.push('/admin/dashboard');
            } else if (response.user.role === 'agency') {
                router.push('/agency/dashboard');
            } else {
                router.push('/user/dashboard');
            }
        } catch (err: any) {
            setError(err.message || 'Login failed');
            throw err;
        } finally {
            setIsLoading(false);
        }
    }, [router]);

    // Register function
    const register = useCallback(async (credentials: RegisterCredentials) => {
        try {
            setIsLoading(true);
            setError(null);

            const response = await authService.register(credentials);

            // Store user data
            authService.storeUserData(response.user, response.token, response.refreshToken);

            setUser(response.user);
            setIsAuthenticated(true);

            // Redirect based on user role
            if (response.user.role === 'agency') {
                router.push('/agency/dashboard');
            } else {
                router.push('/user/dashboard');
            }
        } catch (err: any) {
            setError(err.message || 'Registration failed');
            throw err;
        } finally {
            setIsLoading(false);
        }
    }, [router]);

    // Logout function
    const logout = useCallback(async () => {
        try {
            setIsLoading(true);
            await authService.logout();

            setUser(null);
            setIsAuthenticated(false);

            // Redirect to home page
            router.push('/');
        } catch (err: any) {
            console.error('Logout error:', err);
            // Even if logout fails, clear local state
            setUser(null);
            setIsAuthenticated(false);
            router.push('/');
        } finally {
            setIsLoading(false);
        }
    }, [router]);

    // Update profile function
    const updateProfile = useCallback(async (data: UpdateProfileData) => {
        try {
            setIsLoading(true);
            setError(null);

            const updatedUser = await authService.updateProfile(data);

            setUser(updatedUser);

            // Update stored user data
            const token = localStorage.getItem('auth_token');
            const refreshToken = localStorage.getItem('refresh_token');
            if (token && refreshToken) {
                authService.storeUserData(updatedUser, token, refreshToken);
            }
        } catch (err: any) {
            setError(err.message || 'Profile update failed');
            throw err;
        } finally {
            setIsLoading(false);
        }
    }, []);

    // Clear error function
    const clearError = useCallback(() => {
        setError(null);
    }, []);

    return {
        user,
        isAuthenticated,
        isLoading,
        error,
        login,
        register,
        logout,
        updateProfile,
        clearError,
    };
} 