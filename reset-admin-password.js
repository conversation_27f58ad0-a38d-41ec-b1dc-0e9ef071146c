// Script to reset admin password
require('dotenv').config({ path: '.env.local' })

const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://sagtwjbwgfgvzulnsmhc.supabase.co'
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseKey) {
    console.error('❌ SUPABASE_SERVICE_ROLE_KEY not found')
    process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseKey, {
    auth: {
        autoRefreshToken: false,
        persistSession: false
    }
})

async function resetAdminPassword() {
    console.log('🔧 Resetting admin password...')

    try {
        const adminEmail = '<EMAIL>'
        const newPassword = 'Admin123!'

        // Use admin API to update password
        const { data, error } = await supabase.auth.admin.updateUserById(
            '98c74e6a-3741-4992-9a12-ea49005a4a4f', // Admin user ID from debug
            { password: newPassword }
        )

        if (error) {
            console.error('❌ Failed to reset admin password:', error.message)
            return false
        }

        console.log('✅ Admin password reset successfully!')
        console.log('New credentials:')
        console.log('Email:', adminEmail)
        console.log('Password:', newPassword)

        // Test the new password
        console.log('\n🧪 Testing new password...')
        const { data: testData, error: testError } = await supabase.auth.signInWithPassword({
            email: adminEmail,
            password: newPassword
        })

        if (testError) {
            console.error('❌ Password test failed:', testError.message)
            return false
        }

        console.log('✅ Password test successful!')
        console.log('User ID:', testData.user?.id)
        console.log('User Email:', testData.user?.email)

        // Sign out
        await supabase.auth.signOut()
        console.log('✅ Signed out successfully')

        return true
    } catch (err) {
        console.error('❌ Error resetting admin password:', err.message)
        return false
    }
}

async function main() {
    console.log('🚀 Starting admin password reset...\n')

    const success = await resetAdminPassword()
    if (!success) {
        console.log('\n❌ Failed to reset admin password.')
        process.exit(1)
    }

    console.log('\n✅ Admin password reset completed!')
}

main().catch(console.error) 