// Script to check environment variables
// Run with: node check-env.js

// Try to load from .env.local first
try {
    require('dotenv').config({ path: '.env.local' })
    console.log('✅ Loaded .env.local')
} catch (error) {
    console.log('❌ Could not load .env.local')
}

// Try to load from .env
try {
    require('dotenv').config({ path: '.env' })
    console.log('✅ Loaded .env')
} catch (error) {
    console.log('❌ Could not load .env')
}

// Check environment variables
console.log('\n🔍 Environment Variables Check:')
console.log('NEXT_PUBLIC_SUPABASE_URL:', process.env.NEXT_PUBLIC_SUPABASE_URL ? '✅ Set' : '❌ Not set')
console.log('NEXT_PUBLIC_SUPABASE_ANON_KEY:', process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? '✅ Set' : '❌ Not set')
console.log('SUPABASE_SERVICE_ROLE_KEY:', process.env.SUPABASE_SERVICE_ROLE_KEY ? '✅ Set' : '❌ Not set')

if (!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
    console.log('\n❌ NEXT_PUBLIC_SUPABASE_ANON_KEY is not set!')
    console.log('Please create a .env.local file with your Supabase credentials:')
    console.log(`
NEXT_PUBLIC_SUPABASE_URL=https://sagtwjbwgfgvzulnsmhc.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_actual_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_actual_service_role_key_here
    `)
} else {
    console.log('\n✅ Environment variables are loaded correctly!')
    console.log('You can now run:')
    console.log('  node create-admin.js')
    console.log('  node test-signup.js')
} 