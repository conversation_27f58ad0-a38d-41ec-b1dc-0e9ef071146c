"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Search, Mail, User, Building2, Calendar, Phone, MapPin } from "lucide-react"
import { toast } from "sonner"
import { useMessages } from "@/contexts/messages-context"

export default function AdminMessagesPage() {
    const { messages, markAsRead, markAsReplied, unreadCount } = useMessages()
    const [filteredMessages, setFilteredMessages] = useState(messages)
    const [searchTerm, setSearchTerm] = useState("")
    const [statusFilter, setStatusFilter] = useState<string>("all")
    const [userTypeFilter, setUserTypeFilter] = useState<string>("all")
    const [selectedMessage, setSelectedMessage] = useState<any>(null)
    const [replyText, setReplyText] = useState("")

    useEffect(() => {
        let filtered = messages

        // Filter by search term
        if (searchTerm) {
            filtered = filtered.filter(
                msg =>
                    msg.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    msg.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    msg.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    msg.message.toLowerCase().includes(searchTerm.toLowerCase())
            )
        }

        // Filter by status
        if (statusFilter !== "all") {
            filtered = filtered.filter(msg => msg.status === statusFilter)
        }

        // Filter by user type
        if (userTypeFilter !== "all") {
            filtered = filtered.filter(msg => msg.userType === userTypeFilter)
        }

        setFilteredMessages(filtered)
    }, [messages, searchTerm, statusFilter, userTypeFilter])

    const handleMarkAsReplied = (messageId: string) => {
        if (!replyText.trim()) {
            toast.error("Please enter a reply message")
            return
        }

        markAsReplied(messageId)
        setReplyText("")
        setSelectedMessage(null)
        toast.success("Reply sent successfully")
    }

    const getStatusColor = (status: string) => {
        switch (status) {
            case "unread":
                return "bg-red-100 text-red-800"
            case "read":
                return "bg-blue-100 text-blue-800"
            case "replied":
                return "bg-green-100 text-green-800"
            default:
                return "bg-gray-100 text-gray-800"
        }
    }

    const getUserTypeIcon = (userType: string) => {
        return userType === "agency" ? <Building2 className="h-4 w-4" /> : <User className="h-4 w-4" />
    }

    return (
        <div className="p-6 space-y-6">
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-3xl font-bold">Messages</h1>
                    <p className="text-muted-foreground">Manage contact form submissions from users and agencies</p>
                </div>
                <div className="flex items-center gap-2">
                    <Badge variant="destructive" className="text-sm">
                        {unreadCount} unread
                    </Badge>
                </div>
            </div>

            {/* Filters */}
            <Card>
                <CardContent className="pt-6">
                    <div className="flex flex-col md:flex-row gap-4">
                        <div className="flex-1">
                            <div className="relative">
                                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                                <Input
                                    placeholder="Search messages..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="pl-10"
                                />
                            </div>
                        </div>
                        <Select value={statusFilter} onValueChange={setStatusFilter}>
                            <SelectTrigger className="w-full md:w-40">
                                <SelectValue placeholder="Status" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="all">All Status</SelectItem>
                                <SelectItem value="unread">Unread</SelectItem>
                                <SelectItem value="read">Read</SelectItem>
                                <SelectItem value="replied">Replied</SelectItem>
                            </SelectContent>
                        </Select>
                        <Select value={userTypeFilter} onValueChange={setUserTypeFilter}>
                            <SelectTrigger className="w-full md:w-40">
                                <SelectValue placeholder="User Type" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="all">All Users</SelectItem>
                                <SelectItem value="customer">Customers</SelectItem>
                                <SelectItem value="agency">Agencies</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                </CardContent>
            </Card>

            {/* Messages List */}
            <div className="space-y-4">
                {filteredMessages.map((message) => (
                    <Card key={message.id} className={`cursor-pointer transition-all hover:shadow-md ${message.status === "unread" ? "border-l-4 border-l-red-500" : ""}`}>
                        <CardContent className="pt-6">
                            <div className="flex items-start justify-between">
                                <div className="flex-1 space-y-3">
                                    <div className="flex items-center gap-3">
                                        <div className="flex items-center gap-2">
                                            {getUserTypeIcon(message.userType)}
                                            <span className="font-medium">{message.name}</span>
                                            <Badge variant="outline" className="text-xs">
                                                {message.userType === "agency" ? "Agency" : "Customer"}
                                            </Badge>
                                        </div>
                                        <Badge className={`text-xs ${getStatusColor(message.status)}`}>
                                            {message.status}
                                        </Badge>
                                    </div>

                                    <div>
                                        <h3 className="font-semibold text-lg">{message.subject}</h3>
                                        <p className="text-muted-foreground line-clamp-2">{message.message}</p>
                                    </div>

                                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                                        <div className="flex items-center gap-1">
                                            <Mail className="h-3 w-3" />
                                            {message.email}
                                        </div>
                                        <div className="flex items-center gap-1">
                                            <Phone className="h-3 w-3" />
                                            {message.phone}
                                        </div>
                                        {message.location && (
                                            <div className="flex items-center gap-1">
                                                <MapPin className="h-3 w-3" />
                                                {message.location}
                                            </div>
                                        )}
                                        <div className="flex items-center gap-1">
                                            <Calendar className="h-3 w-3" />
                                            {new Date(message.createdAt).toLocaleDateString()}
                                        </div>
                                    </div>
                                </div>

                                <div className="flex gap-2 ml-4">
                                    <Dialog>
                                        <DialogTrigger asChild>
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => {
                                                    setSelectedMessage(message)
                                                    if (message.status === "unread") {
                                                        markAsRead(message.id)
                                                    }
                                                }}
                                            >
                                                View Details
                                            </Button>
                                        </DialogTrigger>
                                        <DialogContent className="max-w-2xl">
                                            <DialogHeader>
                                                <DialogTitle>{message.subject}</DialogTitle>
                                                <DialogDescription>
                                                    Message from {message.name} ({message.email})
                                                </DialogDescription>
                                            </DialogHeader>
                                            <div className="space-y-4">
                                                <div className="bg-gray-50 p-4 rounded-lg">
                                                    <p className="whitespace-pre-wrap">{message.message}</p>
                                                </div>

                                                <div className="space-y-2">
                                                    <h4 className="font-medium">Reply</h4>
                                                    <Textarea
                                                        placeholder="Type your reply..."
                                                        value={replyText}
                                                        onChange={(e) => setReplyText(e.target.value)}
                                                        rows={4}
                                                    />
                                                    <div className="flex gap-2">
                                                        <Button
                                                            onClick={() => handleMarkAsReplied(message.id)}
                                                            disabled={!replyText.trim()}
                                                        >
                                                            Send Reply
                                                        </Button>
                                                        <Button variant="outline" onClick={() => setReplyText("")}>
                                                            Clear
                                                        </Button>
                                                    </div>
                                                </div>
                                            </div>
                                        </DialogContent>
                                    </Dialog>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                ))}

                {filteredMessages.length === 0 && (
                    <Card>
                        <CardContent className="pt-6">
                            <div className="text-center py-8">
                                <Mail className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                                <h3 className="text-lg font-medium mb-2">No messages found</h3>
                                <p className="text-muted-foreground">
                                    {searchTerm || statusFilter !== "all" || userTypeFilter !== "all"
                                        ? "Try adjusting your filters"
                                        : "No contact form submissions yet"}
                                </p>
                            </div>
                        </CardContent>
                    </Card>
                )}
            </div>
        </div>
    )
} 