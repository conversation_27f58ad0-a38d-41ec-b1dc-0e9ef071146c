// Test script to verify Supabase connection and authentication
// Run with: node test-auth.js

const { createClient } = require('@supabase/supabase-js')

// You'll need to replace these with your actual values
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://sagtwjbwgfgvzulnsmhc.supabase.co'
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseKey) {
    console.error('❌ NEXT_PUBLIC_SUPABASE_ANON_KEY not found in environment variables')
    console.log('Please set your environment variables and try again')
    process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseKey)

async function testConnection() {
    console.log('🔍 Testing Supabase connection...')

    try {
        // Test basic connection
        const { data, error } = await supabase.from('users').select('count').limit(1)

        if (error) {
            console.error('❌ Connection failed:', error.message)
            return false
        }

        console.log('✅ Connection successful!')
        return true
    } catch (err) {
        console.error('❌ Connection error:', err.message)
        return false
    }
}

async function testAuth() {
    console.log('\n🔐 Testing authentication...')

    try {
        // Test sign up
        const testEmail = `test-${Date.now()}@example.com`
        const testPassword = 'testpassword123'

        console.log(`📝 Attempting to sign up: ${testEmail}`)

        const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
            email: testEmail,
            password: testPassword,
            options: {
                data: {
                    first_name: 'Test',
                    last_name: 'User',
                    role: 'user'
                }
            }
        })

        if (signUpError) {
            console.error('❌ Sign up failed:', signUpError.message)
            return false
        }

        console.log('✅ Sign up successful!')
        console.log('User ID:', signUpData.user?.id)

        // Test sign in
        console.log('🔑 Attempting to sign in...')

        const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
            email: testEmail,
            password: testPassword
        })

        if (signInError) {
            console.error('❌ Sign in failed:', signInError.message)
            return false
        }

        console.log('✅ Sign in successful!')

        // Test user profile
        console.log('👤 Testing user profile...')

        const { data: profile, error: profileError } = await supabase
            .from('users')
            .select('*')
            .eq('id', signInData.user.id)
            .single()

        if (profileError) {
            console.error('❌ Profile fetch failed:', profileError.message)
            return false
        }

        console.log('✅ User profile found!')
        console.log('Profile:', profile)

        // Clean up - delete the test user
        console.log('🧹 Cleaning up test user...')
        await supabase.auth.admin.deleteUser(signInData.user.id)
        console.log('✅ Test user deleted')

        return true
    } catch (err) {
        console.error('❌ Auth test error:', err.message)
        return false
    }
}

async function main() {
    console.log('🚀 Starting Supabase authentication test...\n')

    const connectionOk = await testConnection()
    if (!connectionOk) {
        console.log('\n❌ Connection test failed. Please check your environment variables.')
        process.exit(1)
    }

    const authOk = await testAuth()
    if (!authOk) {
        console.log('\n❌ Authentication test failed.')
        process.exit(1)
    }

    console.log('\n🎉 All tests passed! Your Supabase setup is working correctly.')
}

main().catch(console.error) 