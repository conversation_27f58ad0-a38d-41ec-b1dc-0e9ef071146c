// Shared booking types
import type { User, Agency } from './auth';
import type { Car } from './car';
import type { Payment } from './payment';

export interface Booking {
    id: string;
    userId: string;
    carId: string;
    agencyId: string;
    startDate: string;
    endDate: string;
    pickupLocation: string;
    returnLocation: string;
    totalPrice: number;
    status: BookingStatus;
    paymentStatus: BookingPaymentStatus;
    createdAt: string;
    updatedAt: string;

    // Relations
    user?: User;
    car?: Car;
    agency?: Agency;
    payment?: Payment;
}

export interface BookingCreateData {
    carId: string;
    startDate: string;
    endDate: string;
    pickupLocation: string;
    returnLocation: string;
    specialRequests?: string;
}

export interface BookingUpdateData {
    startDate?: string;
    endDate?: string;
    pickupLocation?: string;
    returnLocation?: string;
    specialRequests?: string;
}

export type BookingStatus =
    | 'pending'
    | 'confirmed'
    | 'active'
    | 'completed'
    | 'cancelled'
    | 'rejected';

export type BookingPaymentStatus =
    | 'pending'
    | 'paid'
    | 'failed'
    | 'refunded'
    | 'partially_refunded';

export interface BookingFilters {
    status?: BookingStatus;
    startDate?: string;
    endDate?: string;
    agencyId?: string;
    userId?: string;
    page?: number;
    limit?: number;
}

export interface BookingStats {
    total: number;
    pending: number;
    confirmed: number;
    active: number;
    completed: number;
    cancelled: number;
    revenue: number;
}

export interface BookingTimeline {
    status: BookingStatus;
    timestamp: string;
    description: string;
    updatedBy?: string;
} 