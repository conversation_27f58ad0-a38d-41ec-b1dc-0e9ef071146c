-- Completely disable <PERSON><PERSON> to fix infinite recursion issues
-- This is a temporary fix to get authentication working

-- Disable <PERSON><PERSON> on all tables
ALTER TABLE public.users DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.agencies DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.cars DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.bookings DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.reviews DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.payments DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.blogs DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.messages DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.agency_documents DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.coupons DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.gps_tracking DISABLE ROW LEVEL SECURITY;

-- Drop all policies to prevent any conflicts
DROP POLICY IF EXISTS "Users can view own profile" ON public.users;
DROP POLICY IF EXISTS "Users can update own profile" ON public.users;
DROP POLICY IF EXISTS "Users can insert own profile" ON public.users;
DROP POLICY IF EXISTS "Enable read access for all users" ON public.users;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON public.users;
DROP POLICY IF EXISTS "Enable update for users based on user_id" ON public.users;
DROP POLICY IF EXISTS "Enable delete for users based on user_id" ON public.users;

DROP POLICY IF EXISTS "Agencies can view own data" ON public.agencies;
DROP POLICY IF EXISTS "Agencies can update own data" ON public.agencies;
DROP POLICY IF EXISTS "Agencies can insert own data" ON public.agencies;
DROP POLICY IF EXISTS "Enable read access for all agencies" ON public.agencies;
DROP POLICY IF EXISTS "Enable insert for authenticated agencies only" ON public.agencies;
DROP POLICY IF EXISTS "Enable update for agencies based on user_id" ON public.agencies;
DROP POLICY IF EXISTS "Enable delete for agencies based on user_id" ON public.agencies;

DROP POLICY IF EXISTS "Cars can view own data" ON public.cars;
DROP POLICY IF EXISTS "Cars can update own data" ON public.cars;
DROP POLICY IF EXISTS "Cars can insert own data" ON public.cars;
DROP POLICY IF EXISTS "Enable read access for all cars" ON public.cars;
DROP POLICY IF EXISTS "Enable insert for authenticated agencies only" ON public.cars;
DROP POLICY IF EXISTS "Enable update for cars based on agency_id" ON public.cars;
DROP POLICY IF EXISTS "Enable delete for cars based on agency_id" ON public.cars;

DROP POLICY IF EXISTS "Bookings can view own data" ON public.bookings;
DROP POLICY IF EXISTS "Bookings can update own data" ON public.bookings;
DROP POLICY IF EXISTS "Bookings can insert own data" ON public.bookings;
DROP POLICY IF EXISTS "Enable read access for all bookings" ON public.bookings;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON public.bookings;
DROP POLICY IF EXISTS "Enable update for bookings based on user_id" ON public.bookings;
DROP POLICY IF EXISTS "Enable delete for bookings based on user_id" ON public.bookings;

-- Grant all permissions to authenticated and anonymous users
GRANT ALL ON public.users TO anon, authenticated;
GRANT ALL ON public.agencies TO anon, authenticated;
GRANT ALL ON public.cars TO anon, authenticated;
GRANT ALL ON public.bookings TO anon, authenticated;
GRANT ALL ON public.reviews TO anon, authenticated;
GRANT ALL ON public.payments TO anon, authenticated;
GRANT ALL ON public.notifications TO anon, authenticated;
GRANT ALL ON public.blogs TO anon, authenticated;
GRANT ALL ON public.messages TO anon, authenticated;
GRANT ALL ON public.agency_documents TO anon, authenticated;
GRANT ALL ON public.coupons TO anon, authenticated;
GRANT ALL ON public.gps_tracking TO anon, authenticated;

-- Grant sequence permissions
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;

-- Note: This is a temporary fix. In production, you should re-enable RLS with proper policies
-- that don't cause infinite recursion. The current setup allows full access to all users. 