// <PERSON>ript to create an admin user for testing
// Run with: node create-admin.js

// Load environment variables from .env.local
require('dotenv').config({ path: '.env.local' })

const { createClient } = require('@supabase/supabase-js')

// Get values from environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://sagtwjbwgfgvzulnsmhc.supabase.co'
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseKey) {
    console.error('❌ NEXT_PUBLIC_SUPABASE_ANON_KEY not found in environment variables')
    console.log('Please set your environment variables and try again')
    process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseKey)

async function createAdminUser() {
    console.log('👑 Creating admin user...')

    try {
        const adminEmail = '<EMAIL>'
        const adminPassword = 'Admin123!'

        console.log(`📝 Creating admin user: ${adminEmail}`)

        // Create the auth user
        const { data: authData, error: authError } = await supabase.auth.signUp({
            email: adminEmail,
            password: adminPassword,
            options: {
                data: {
                    first_name: 'Admin',
                    last_name: 'User',
                    role: 'admin'
                }
            }
        })

        if (authError) {
            console.error('❌ Admin auth creation failed:', authError.message)
            return false
        }

        console.log('✅ Admin auth user created!')
        console.log('Admin User ID:', authData.user?.id)

        // Wait for profile creation
        await new Promise(resolve => setTimeout(resolve, 2000))

        // Verify profile was created
        const { data: profile, error: profileError } = await supabase
            .from('users')
            .select('*')
            .eq('id', authData.user.id)
            .single()

        if (profileError) {
            console.error('❌ Admin profile fetch failed:', profileError.message)
            return false
        }

        console.log('✅ Admin profile created!')
        console.log('Admin Profile:', profile)

        // Update profile to ensure admin role
        const { data: updatedProfile, error: updateError } = await supabase
            .from('users')
            .update({ role: 'admin', is_verified: true })
            .eq('id', authData.user.id)
            .select()
            .single()

        if (updateError) {
            console.error('❌ Admin profile update failed:', updateError.message)
            return false
        }

        console.log('✅ Admin profile updated!')
        console.log('Updated Profile:', updatedProfile)

        console.log('\n🎉 Admin user created successfully!')
        console.log('Email:', adminEmail)
        console.log('Password:', adminPassword)
        console.log('Role: admin')

        return true
    } catch (err) {
        console.error('❌ Admin creation error:', err.message)
        return false
    }
}

async function main() {
    console.log('🚀 Creating admin user for testing...\n')

    const success = await createAdminUser()
    if (!success) {
        console.log('\n❌ Admin user creation failed.')
        process.exit(1)
    }

    console.log('\n✅ Admin user is ready for testing!')
    console.log('You can now log <NAME_EMAIL> and password: Admin123!')
}

main().catch(console.error) 