"use client"

import type React from "react"
import { createContext, useContext, useState, useMemo } from "react"

// Define the exchange rates relative to MAD
const exchangeRates = {
    MAD: 1,
    USD: 0.1, // Approximate: 1 MAD = 0.10 USD
    EUR: 0.09, // Approximate: 1 MAD = 0.09 EUR
    GBP: 0.08, // Approximate: 1 MAD = 0.08 GBP
}

type Currency = "MAD" | "USD" | "EUR" | "GBP"

interface CurrencyContextType {
    currency: Currency
    setCurrency: (currency: Currency) => void
    convert: (amount: number) => string
}

const CurrencyContext = createContext<CurrencyContextType | undefined>(undefined)

export function CurrencyProvider({ children }: { children: React.ReactNode }) {
    const [currency, setCurrency] = useState<Currency>("MAD")

    const convert = (amount: number) => {
        const rate = exchangeRates[currency]
        const convertedAmount = amount * rate
        return new Intl.NumberFormat("en-US", {
            style: "currency",
            currency,
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
        }).format(convertedAmount)
    }

    const value = useMemo(
        () => ({
            currency,
            setCurrency,
            convert,
        }),
        [currency]
    )

    return <CurrencyContext.Provider value={value}>{children}</CurrencyContext.Provider>
}

export function useCurrency() {
    const context = useContext(CurrencyContext)
    if (context === undefined) {
        throw new Error("useCurrency must be used within a CurrencyProvider")
    }
    return context
} 