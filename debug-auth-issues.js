// Debug script to test authentication flow and role detection
require('dotenv').config({ path: '.env.local' })

const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://sagtwjbwgfgvzulnsmhc.supabase.co'
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseKey) {
    console.error('❌ NEXT_PUBLIC_SUPABASE_ANON_KEY not found')
    process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseKey)

async function testAuthFlow() {
    console.log('🔍 Testing authentication flow...\n')

    // Test admin login
    console.log('👑 Testing admin login...')
    await testUserLogin('<EMAIL>', 'Admin123!', 'admin')

    console.log('\n' + '='.repeat(50) + '\n')

    // Test agency login
    console.log('🏢 Testing agency login...')
    await testUserLogin('<EMAIL>', 'testpassword123', 'agency')

    console.log('\n' + '='.repeat(50) + '\n')

    // Test user login
    console.log('👤 Testing user login...')
    await testUserLogin('<EMAIL>', 'testpassword123', 'user')
}

async function testUserLogin(email, password, expectedRole) {
    try {
        console.log(`📝 Signing in as ${expectedRole}: ${email}`)

        const { data, error } = await supabase.auth.signInWithPassword({
            email: email,
            password: password
        })

        if (error) {
            console.error('❌ Login failed:', error.message)
            return false
        }

        console.log('✅ Login successful!')
        console.log('User ID:', data.user?.id)
        console.log('User Email:', data.user?.email)

        // Get user profile
        const { data: profile, error: profileError } = await supabase
            .from('users')
            .select('*')
            .eq('id', data.user.id)
            .single()

        if (profileError) {
            console.error('❌ Profile fetch failed:', profileError.message)
            return false
        }

        console.log('✅ User profile loaded!')
        console.log('Profile Role:', profile.role)
        console.log('Profile Email:', profile.email)
        console.log('Profile Name:', profile.first_name, profile.last_name)
        console.log('Is Verified:', profile.is_verified)

        // Check if role matches expected
        if (profile.role === expectedRole) {
            console.log(`✅ Role matches expected: ${expectedRole}`)
        } else {
            console.log(`❌ Role mismatch! Expected: ${expectedRole}, Got: ${profile.role}`)
        }

        // Test redirect logic
        console.log('\n📍 Testing redirect logic...')
        const currentPath = '/auth'

        if (profile.role === 'admin' && !currentPath.startsWith('/admin')) {
            console.log('👑 Should redirect admin to /admin/dashboard')
        } else if (profile.role === 'agency' && !currentPath.startsWith('/agency')) {
            console.log('🏢 Should redirect agency to /agency/dashboard')
        } else if (profile.role === 'user' && !currentPath.startsWith('/user')) {
            console.log('👤 Should redirect user to /user/dashboard')
        }

        // Sign out
        await supabase.auth.signOut()
        console.log('✅ Signed out successfully')

        return true
    } catch (err) {
        console.error('❌ Test error:', err.message)
        return false
    }
}

async function testProfileFetching() {
    console.log('\n🔍 Testing profile fetching directly...\n')

    // Test with admin user ID
    const adminEmail = '<EMAIL>'

    // First get the user ID
    const { data: users, error: usersError } = await supabase
        .from('users')
        .select('*')
        .eq('email', adminEmail)
        .single()

    if (usersError) {
        console.error('❌ Failed to find admin user:', usersError.message)
        return
    }

    console.log('👑 Admin user found:')
    console.log('ID:', users.id)
    console.log('Email:', users.email)
    console.log('Role:', users.role)
    console.log('Name:', users.first_name, users.last_name)

    // Test the getUserProfile method logic
    console.log('\n🔍 Testing getUserProfile method...')

    const { data: profile, error: profileError } = await supabase
        .from('users')
        .select('*')
        .eq('id', users.id)
        .single()

    if (profileError) {
        console.error('❌ Profile fetch error:', profileError.message)
    } else {
        console.log('✅ Profile fetched successfully:')
        console.log('Role:', profile.role)
        console.log('Email:', profile.email)
    }
}

async function main() {
    console.log('🚀 Starting authentication debug...\n')

    await testAuthFlow()
    await testProfileFetching()

    console.log('\n✅ Debug completed!')
}

main().catch(console.error)
