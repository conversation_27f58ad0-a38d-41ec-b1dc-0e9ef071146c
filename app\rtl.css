.rtl {
  direction: rtl;
  text-align: right;
}

.rtl .ml-auto {
  margin-left: 0 !important;
  margin-right: auto !important;
}

.rtl .mr-auto {
  margin-right: 0 !important;
  margin-left: auto !important;
}

.rtl .ml-1,
.rtl .ml-2,
.rtl .ml-3,
.rtl .ml-4,
.rtl .ml-6,
.rtl .ml-8 {
  margin-left: 0 !important;
  margin-right: 0.25rem !important;
}

.rtl .mr-1,
.rtl .mr-2,
.rtl .mr-3,
.rtl .mr-4,
.rtl .mr-6,
.rtl .mr-8 {
  margin-right: 0 !important;
  margin-left: 0.25rem !important;
}

.rtl .text-left {
  text-align: right !important;
}

.rtl .text-right {
  text-align: left !important;
}

.rtl .right-0 {
  right: auto !important;
  left: 0 !important;
}

.rtl .left-0 {
  left: auto !important;
  right: 0 !important;
}

.rtl .space-x-2 > :not([hidden]) ~ :not([hidden]),
.rtl .space-x-3 > :not([hidden]) ~ :not([hidden]),
.rtl .space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
}

.rtl .flex-row {
  flex-direction: row-reverse;
}

/* Fix dropdown in RTL mode */
.rtl [data-radix-popper-content-wrapper] {
  transform: translateX(var(--radix-popper-transform-x)) translateY(var(--radix-popper-transform-y)) !important;
}
