-- Fix admin user role and remove email confirmation requirements
-- This migration ensures proper admin access and removes email confirmation blocking

-- Update the admin user role in the users table
UPDATE public.users 
SET role = 'admin', 
    is_verified = true,
    first_name = 'Admin',
    last_name = 'User'
WHERE email = '<EMAIL>';

-- If the admin user doesn't exist in public.users, create it
INSERT INTO public.users (id, email, first_name, last_name, role, is_verified)
SELECT 
    au.id,
    au.email,
    'Admin',
    'User',
    'admin',
    true
FROM auth.users au
WHERE au.email = '<EMAIL>'
AND NOT EXISTS (
    SELECT 1 FROM public.users pu WHERE pu.id = au.id
);

-- Ensure all users have is_verified = true (remove email confirmation requirement)
UPDATE public.users 
SET is_verified = true 
WHERE is_verified = false;

-- Update auth.users to mark all users as confirmed
UPDATE auth.users 
SET email_confirmed_at = COALESCE(email_confirmed_at, NOW())
WHERE email_confirmed_at IS NULL;

-- Create a function to automatically verify new users
CREATE OR REPLACE FUNCTION auto_verify_new_users()
RETURNS TRIGGER AS $$
BEGIN
    -- Set email_confirmed_at if it's null
    IF NEW.email_confirmed_at IS NULL THEN
        NEW.email_confirmed_at = NOW();
    END IF;
    
    -- Ensure the user profile is marked as verified
    UPDATE public.users 
    SET is_verified = true 
    WHERE id = NEW.id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to auto-verify new users
DROP TRIGGER IF EXISTS auto_verify_users_trigger ON auth.users;
CREATE TRIGGER auto_verify_users_trigger
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION auto_verify_new_users();

-- Also create a trigger to update existing users when they sign in
CREATE OR REPLACE FUNCTION verify_user_on_signin()
RETURNS TRIGGER AS $$
BEGIN
    -- Set email_confirmed_at if it's null
    IF NEW.email_confirmed_at IS NULL THEN
        NEW.email_confirmed_at = NOW();
    END IF;
    
    -- Ensure the user profile is marked as verified
    UPDATE public.users 
    SET is_verified = true 
    WHERE id = NEW.id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to verify users on signin
DROP TRIGGER IF EXISTS verify_user_signin_trigger ON auth.users;
CREATE TRIGGER verify_user_signin_trigger
    AFTER UPDATE ON auth.users
    FOR EACH ROW EXECUTE FUNCTION verify_user_on_signin(); 