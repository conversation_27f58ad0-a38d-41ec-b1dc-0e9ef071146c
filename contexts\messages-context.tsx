"use client"

import React, { createContext, useContext, useState, useEffect } from 'react'

interface Message {
    id: string
    name: string
    email: string
    phone: string
    subject: string
    message: string
    userType: "customer" | "agency"
    status: "unread" | "read" | "replied"
    createdAt: string
    location?: string
}

interface MessagesContextType {
    messages: Message[]
    addMessage: (message: Omit<Message, 'id' | 'status' | 'createdAt'>) => void
    markAsRead: (messageId: string) => void
    markAsReplied: (messageId: string) => void
    unreadCount: number
}

const MessagesContext = createContext<MessagesContextType | undefined>(undefined)

export function MessagesProvider({ children }: { children: React.ReactNode }) {
    const [messages, setMessages] = useState<Message[]>([])

    // Load messages from localStorage on mount
    useEffect(() => {
        const savedMessages = localStorage.getItem('adminMessages')
        if (savedMessages) {
            try {
                setMessages(JSON.parse(savedMessages))
            } catch (error) {
                console.error('Error loading messages from localStorage:', error)
            }
        }
    }, [])

    // Save messages to localStorage whenever messages change
    useEffect(() => {
        localStorage.setItem('adminMessages', JSON.stringify(messages))
    }, [messages])

    const addMessage = (messageData: Omit<Message, 'id' | 'status' | 'createdAt'>) => {
        const newMessage: Message = {
            ...messageData,
            id: `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            status: 'unread',
            createdAt: new Date().toISOString(),
        }
        setMessages(prev => [newMessage, ...prev])
    }

    const markAsRead = (messageId: string) => {
        setMessages(prev =>
            prev.map(msg =>
                msg.id === messageId ? { ...msg, status: 'read' as const } : msg
            )
        )
    }

    const markAsReplied = (messageId: string) => {
        setMessages(prev =>
            prev.map(msg =>
                msg.id === messageId ? { ...msg, status: 'replied' as const } : msg
            )
        )
    }

    const unreadCount = messages.filter(msg => msg.status === 'unread').length

    return (
        <MessagesContext.Provider value={{
            messages,
            addMessage,
            markAsRead,
            markAsReplied,
            unreadCount
        }}>
            {children}
        </MessagesContext.Provider>
    )
}

export function useMessages() {
    const context = useContext(MessagesContext)
    if (context === undefined) {
        throw new Error('useMessages must be used within a MessagesProvider')
    }
    return context
} 