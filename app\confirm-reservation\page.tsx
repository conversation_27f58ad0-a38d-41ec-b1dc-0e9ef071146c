"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Upload, Calendar, Clock, MapPin, X, CheckCircle, User, Car, DollarSign, RefreshCw } from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { toast } from "sonner"
import { useI18n } from "@/i18n/i18n-provider"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { useCurrency } from "@/contexts/currency-context"
import { DatePickerWithRange } from "@/components/shared/date-range-picker"
import { useBookings } from "@/contexts/bookings-context"
import type { DateRange } from "react-day-picker"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"

const moroccanCities = [
  "Agadir", "Al Hoceima", "Asilah", "Azrou", "Beni Mellal", "Berkane", "Berrechid", "Boujdour", "Casablanca", "Chefchaouen", "Dakhla", "El Jadida", "Errachidia", "Essaouira", "Fes", "Guelmim", "Ifrane", "Kenitra", "Khemisset", "Khouribga", "Laayoune", "Larache", "Marrakech", "Martil", "Meknes", "Mohammedia", "Nador", "Ouarzazate", "Oujda", "Rabat", "Safi", "Sale", "Settat", "Sidi Ifni", "Tangier", "Taza", "Tetouan", "Tiznit"
];
const timeOptions = Array.from({ length: 24 }, (_, i) => `${i.toString().padStart(2, "0")}:00`);

export default function ConfirmReservationPage() {
  const { t } = useI18n()
  const router = useRouter()
  const [firstName, setFirstName] = useState("")
  const [lastName, setLastName] = useState("")
  const [phone, setPhone] = useState("")
  const [pickupLocation, setPickupLocation] = useState("")
  const [pickupTime, setPickupTime] = useState("")
  const [dropoffLocation, setDropoffLocation] = useState("")
  const [dropoffTime, setDropoffTime] = useState("")
  const [agreeToTerms, setAgreeToTerms] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [contractUrl, setContractUrl] = useState<string>("")
  const [deliveryOption, setDeliveryOption] = useState("pickup")
  const [deliveryAddress, setDeliveryAddress] = useState("")
  const [withChauffeur, setWithChauffeur] = useState(false)
  const [withChildSeat, setWithChildSeat] = useState(false)
  const [paymentMethod, setPaymentMethod] = useState("bank_transfer")
  const [showSuccessModal, setShowSuccessModal] = useState(false)
  const [bookingId, setBookingId] = useState("")
  const [showVerification, setShowVerification] = useState(false)

  // For file uploads
  const idCardInputRef = useRef<HTMLInputElement>(null)
  const licenseInputRef = useRef<HTMLInputElement>(null)
  const [idCardImage, setIdCardImage] = useState<string | null>(null)
  const [licenseImage, setLicenseImage] = useState<string | null>(null)

  // Add coupon state
  const [couponCode, setCouponCode] = useState("")
  const [couponPercent, setCouponPercent] = useState<number | null>(null)
  const [couponError, setCouponError] = useState("")

  const { convert } = useCurrency()

  // Example: fetch agency coupon and delivery fee (in real app, fetch from API)
  const agencyCoupon = { code: "ABC123", percent: 10 } // Replace with real fetch
  const agencyDeliveryFee = 150; // Mock fee, replace with real fetch
  const chauffeurFee = 300; // Mock chauffeur fee per day
  const childSeatFee = 100; // Mock child seat fee per day

  const { addBooking } = useBookings();
  const [dateRange, setDateRange] = useState<DateRange | undefined>(undefined);
  const [unavailableDates, setUnavailableDates] = useState<Date[]>([]);

  useEffect(() => {
    // Mock fetch unavailable dates for the car (rented days)
    setUnavailableDates([
      new Date("2025-04-10"),
      new Date("2025-04-11"),
      new Date("2025-04-12"),
      new Date("2025-04-15"),
      new Date("2025-04-16"),
      new Date("2025-04-20"),
      new Date("2025-04-21"),
      new Date("2025-04-22"),
      new Date("2025-04-23"),
    ]);
  }, []);

  useEffect(() => {
    // Simulate fetching the contract URL from localStorage (set in agency dashboard settings)
    const url = localStorage.getItem("agencyContractUrl")
    if (url) setContractUrl(url)
  }, [])

  const handleIdCardUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0]
      const imageUrl = URL.createObjectURL(file)
      setIdCardImage(imageUrl)
    }
  }

  const handleLicenseUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0]
      const imageUrl = URL.createObjectURL(file)
      setLicenseImage(imageUrl)
    }
  }

  const removeIdCardImage = () => {
    setIdCardImage(null)
    if (idCardInputRef.current) {
      idCardInputRef.current.value = ""
    }
  }

  const removeLicenseImage = () => {
    setLicenseImage(null)
    if (licenseInputRef.current) {
      licenseInputRef.current.value = ""
    }
  }

  const validateForm = () => {
    const errors: string[] = []

    if (!firstName.trim()) errors.push("First name is required")
    if (!lastName.trim()) errors.push("Last name is required")
    if (!phone.trim()) errors.push("Phone number is required")
    if (!pickupLocation) errors.push("Pickup location is required")
    if (!dateRange?.from || !dateRange?.to) errors.push("Rental dates are required")
    if (!pickupTime) errors.push("Pickup time is required")
    if (!dropoffLocation) errors.push("Dropoff location is required")
    if (!dropoffTime) errors.push("Dropoff time is required")
    if (!idCardImage) errors.push("ID card or passport is required")
    if (!licenseImage) errors.push("Driving license is required")
    if (!agreeToTerms) errors.push("You must agree to the agency contract")

    return errors
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    const errors = validateForm()
    if (errors.length > 0) {
      toast.error(errors[0])
      return
    }

    // Show verification page instead of success modal
    setShowVerification(true)
  }

  const handleConfirmReservation = () => {
    setIsSubmitting(true)

    // Generate booking ID
    const newBookingId = `BK-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`
    setBookingId(newBookingId)

    // Add booking to agency dashboard
    if (!dateRange || !dateRange.from || !dateRange.to || !idCardImage || !licenseImage) return;
    addBooking({
      carId: "car1", // Replace with actual car id
      startDate: dateRange.from.toISOString().split("T")[0],
      endDate: dateRange.to.toISOString().split("T")[0],
      customerName: `${firstName} ${lastName}`,
      status: "pending",
      images: [idCardImage, licenseImage]
    });

    // Show success modal after a short delay
    setTimeout(() => {
      setIsSubmitting(false)
      setShowVerification(false)
      setShowSuccessModal(true)
    }, 1500)
  }

  const handleBackToForm = () => {
    setShowVerification(false)
  }

  const rentalDuration = dateRange?.from && dateRange?.to ?
    Math.ceil((dateRange.to.getTime() - dateRange.from.getTime()) / (1000 * 60 * 60 * 24)) : 7
  const rentalCost = 75 * rentalDuration
  const deliveryCost = deliveryOption === "delivery" ? agencyDeliveryFee : 0
  const chauffeurCost = withChauffeur ? chauffeurFee * rentalDuration : 0
  const childSeatCost = withChildSeat ? childSeatFee * rentalDuration : 0
  const discount = couponPercent ? (rentalCost * couponPercent) / 100 : 0
  const total = rentalCost + deliveryCost + chauffeurCost + childSeatCost - discount

  const handleApplyCoupon = () => {
    if (couponCode === agencyCoupon.code) {
      setCouponPercent(agencyCoupon.percent)
      setCouponError("")
    } else {
      setCouponPercent(null)
      setCouponError("Invalid or not applicable for this agency.")
    }
  }

  // Success Modal Component
  const SuccessModal = () => (
    <Dialog open={showSuccessModal} onOpenChange={setShowSuccessModal}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CheckCircle className="h-6 w-6 text-green-600" />
            {t("reservation.successModal.title")}
          </DialogTitle>
          <DialogDescription>
            {t("reservation.successModal.message")}
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4">
          <div className="bg-gray-50 p-4 rounded-lg">
            <p className="text-sm font-medium text-gray-700">
              {t("reservation.successModal.bookingId")}: <span className="font-mono">{bookingId}</span>
            </p>
          </div>
          <div className="text-sm text-gray-600">
            <p>• We've sent a confirmation email to your registered email address</p>
            <p>• The agency will contact you within 24 hours to confirm your booking</p>
            <p>• You can track your booking status in your dashboard</p>
          </div>
        </div>
        <DialogFooter>
          <Button onClick={() => {
            setShowSuccessModal(false)
            router.push("/")
          }} className="w-full">
            {t("reservation.successModal.continue")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )

  // Verification Page Component
  const VerificationPage = () => (
    <div className="container mx-auto py-10 px-4">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8 text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Verify Your Reservation</h1>
          <p className="text-lg text-gray-600">Please review all information before confirming your booking</p>
        </div>

        <Card className="border-2 border-gray-200 shadow-xl">
          <CardHeader className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-t-lg">
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-6 w-6" />
              Car Rental Agreement
            </CardTitle>
            <p className="text-blue-100">Booking ID: {bookingId || `BK-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`}</p>
          </CardHeader>

          <CardContent className="p-8 space-y-8">
            {/* Customer Information */}
            <div className="border-b border-gray-200 pb-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <User className="h-5 w-5 text-blue-600" />
                Customer Information
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-gray-500">Full Name</p>
                  <p className="text-lg font-semibold">{firstName} {lastName}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Phone Number</p>
                  <p className="text-lg font-semibold">{phone}</p>
                </div>
              </div>
            </div>

            {/* Vehicle Information */}
            <div className="border-b border-gray-200 pb-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <Car className="h-5 w-5 text-blue-600" />
                Vehicle Information
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-gray-500">Car Type</p>
                  <p className="text-lg font-semibold">Toyota Corolla 2022</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Transmission</p>
                  <p className="text-lg font-semibold">Automatic</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Color</p>
                  <p className="text-lg font-semibold">Red</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Insurance</p>
                  <p className="text-lg font-semibold">Comprehensive</p>
                </div>
              </div>
            </div>

            {/* Rental Period */}
            <div className="border-b border-gray-200 pb-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <Calendar className="h-5 w-5 text-blue-600" />
                Rental Period
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-gray-500">Pickup Date & Time</p>
                  <p className="text-lg font-semibold">
                    {dateRange?.from?.toLocaleDateString()} at {pickupTime}
                  </p>
                  <p className="text-sm text-gray-600">Location: {pickupLocation}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Return Date & Time</p>
                  <p className="text-lg font-semibold">
                    {dateRange?.to?.toLocaleDateString()} at {dropoffTime}
                  </p>
                  <p className="text-sm text-gray-600">Location: {dropoffLocation}</p>
                </div>
              </div>
              <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                <p className="text-sm font-medium text-blue-900">
                  Duration: {rentalDuration} {rentalDuration === 1 ? 'day' : 'days'}
                </p>
              </div>
            </div>

            {/* Additional Services */}
            <div className="border-b border-gray-200 pb-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-blue-600" />
                Additional Services
              </h3>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-gray-700">Chauffeur Service</span>
                  <Badge variant={withChauffeur ? "default" : "secondary"}>
                    {withChauffeur ? "Included" : "Not Selected"}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700">Child Seat</span>
                  <Badge variant={withChildSeat ? "default" : "secondary"}>
                    {withChildSeat ? "Included" : "Not Selected"}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700">Delivery Option</span>
                  <Badge variant="default">
                    {deliveryOption === "delivery" ? "Location Delivery" : "Agency Pickup"}
                  </Badge>
                </div>
                {deliveryOption === "delivery" && deliveryAddress && (
                  <div className="mt-2 p-3 bg-gray-50 rounded-lg">
                    <p className="text-sm font-medium text-gray-700">Delivery Address</p>
                    <p className="text-gray-600">{deliveryAddress}</p>
                  </div>
                )}
              </div>
            </div>

            {/* Payment Information */}
            <div className="border-b border-gray-200 pb-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <DollarSign className="h-5 w-5 text-blue-600" />
                Payment Information
              </h3>
              <div className="space-y-3">
                <div className="flex justify-between items-center py-2 border-b border-gray-200">
                  <span className="text-gray-700">Rental Cost ({rentalDuration} {rentalDuration === 1 ? 'day' : 'days'})</span>
                  <span className="font-semibold">{convert(rentalCost)}</span>
                </div>
                {deliveryOption === "delivery" && (
                  <div className="flex justify-between items-center py-2 border-b border-gray-200">
                    <span className="text-gray-700">Delivery Fee</span>
                    <span className="font-semibold">{convert(deliveryCost)}</span>
                  </div>
                )}
                {withChauffeur && (
                  <div className="flex justify-between items-center py-2 border-b border-gray-200">
                    <span className="text-gray-700">Chauffeur Service</span>
                    <span className="font-semibold">{convert(chauffeurCost)}</span>
                  </div>
                )}
                {withChildSeat && (
                  <div className="flex justify-between items-center py-2 border-b border-gray-200">
                    <span className="text-gray-700">Child Seat</span>
                    <span className="font-semibold">{convert(childSeatCost)}</span>
                  </div>
                )}
                {discount > 0 && (
                  <div className="flex justify-between items-center py-2 border-b border-green-200 bg-green-50 rounded px-3">
                    <span className="text-green-700 font-medium">Discount</span>
                    <span className="font-bold text-green-700">-{convert(discount)}</span>
                  </div>
                )}
                <div className="border-t-2 border-blue-300 pt-4 mt-4">
                  <div className="flex justify-between items-center">
                    <span className="text-xl font-bold text-gray-900">Total Amount</span>
                    <span className="text-2xl font-bold text-blue-600">{convert(total)}</span>
                  </div>
                </div>
                <div className="mt-4 p-3 bg-amber-50 rounded-lg">
                  <p className="text-sm font-medium text-amber-900">
                    Security Deposit: {convert(5000)} (refundable)
                  </p>
                </div>
              </div>
            </div>

            {/* Terms and Conditions */}
            <div className="bg-gray-50 p-6 rounded-lg">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Terms and Conditions</h3>
              <ul className="text-sm text-gray-700 space-y-2">
                <li>• Driver must be 21+ years old with valid driver's license</li>
                <li>• Security deposit is required and will be refunded after vehicle inspection</li>
                <li>• Vehicle must be returned in the same condition as received</li>
                <li>• Fuel tank must be returned full</li>
                <li>• Late returns may incur additional charges</li>
                <li>• Cancellation is free up to 48 hours before pickup</li>
              </ul>
            </div>
          </CardContent>

          <CardFooter className="bg-gray-50 p-6 flex flex-col sm:flex-row gap-4">
            <Button
              variant="outline"
              onClick={handleBackToForm}
              className="flex-1"
            >
              Back to Edit
            </Button>
            <Button
              onClick={handleConfirmReservation}
              disabled={isSubmitting}
              className="flex-1 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700"
            >
              {isSubmitting ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                <>
                  <CheckCircle className="mr-2 h-4 w-4" />
                  Confirm Reservation
                </>
              )}
            </Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  )

  return (
    <div className="container mx-auto py-10 px-4">
      {showVerification ? (
        <VerificationPage />
      ) : (
        <>
          <div className="mb-8">
            <h1 className="text-3xl font-bold">{t("reservation.title")}</h1>
            <p className="text-muted-foreground mt-2">{t("reservation.subtitle")}</p>
          </div>

          <form onSubmit={handleSubmit}>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Left side - Form */}
              <div className="lg:col-span-2 space-y-8">
                {/* Personal Information */}
                <Card>
                  <CardHeader>
                    <CardTitle>{t("reservation.personalInfo.title")}</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="first-name">{t("reservation.personalInfo.firstName")}</Label>
                        <Input
                          id="first-name"
                          value={firstName}
                          onChange={(e) => setFirstName(e.target.value)}
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="last-name">{t("reservation.personalInfo.lastName")}</Label>
                        <Input
                          id="last-name"
                          value={lastName}
                          onChange={(e) => setLastName(e.target.value)}
                          required
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="phone">{t("reservation.personalInfo.phone")}</Label>
                      <Input
                        id="phone"
                        type="tel"
                        placeholder={t("reservation.personalInfo.phonePlaceholder")}
                        value={phone}
                        onChange={(e) => setPhone(e.target.value)}
                        required
                      />
                    </div>

                    {/* Document Uploads */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="id-card">{t("reservation.personalInfo.idOrPassport")}</Label>
                        <div className="space-y-2">
                          <input
                            ref={idCardInputRef}
                            type="file"
                            accept="image/*,.pdf"
                            onChange={handleIdCardUpload}
                            className="hidden"
                            required
                          />
                          <Button
                            type="button"
                            variant="outline"
                            onClick={() => idCardInputRef.current?.click()}
                            className="w-full"
                          >
                            <Upload className="mr-2 h-4 w-4" />
                            {t("reservation.personalInfo.uploadHint")}
                          </Button>
                          {idCardImage && (
                            <div className="relative">
                              <img
                                src={idCardImage}
                                alt="ID Card"
                                className="w-full h-32 object-cover rounded border"
                              />
                              <Button
                                type="button"
                                variant="destructive"
                                size="sm"
                                onClick={removeIdCardImage}
                                className="absolute top-2 right-2"
                              >
                                <X className="h-4 w-4" />
                              </Button>
                            </div>
                          )}
                          <p className="text-xs text-muted-foreground">
                            {t("reservation.personalInfo.uploadFormat")}
                          </p>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="license">{t("reservation.personalInfo.license")}</Label>
                        <div className="space-y-2">
                          <input
                            ref={licenseInputRef}
                            type="file"
                            accept="image/*,.pdf"
                            onChange={handleLicenseUpload}
                            className="hidden"
                            required
                          />
                          <Button
                            type="button"
                            variant="outline"
                            onClick={() => licenseInputRef.current?.click()}
                            className="w-full"
                          >
                            <Upload className="mr-2 h-4 w-4" />
                            {t("reservation.personalInfo.uploadHint")}
                          </Button>
                          {licenseImage && (
                            <div className="relative">
                              <img
                                src={licenseImage}
                                alt="License"
                                className="w-full h-32 object-cover rounded border"
                              />
                              <Button
                                type="button"
                                variant="destructive"
                                size="sm"
                                onClick={removeLicenseImage}
                                className="absolute top-2 right-2"
                              >
                                <X className="h-4 w-4" />
                              </Button>
                            </div>
                          )}
                          <p className="text-xs text-muted-foreground">
                            {t("reservation.personalInfo.uploadFormat")}
                          </p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Rental Information */}
                <Card>
                  <CardHeader>
                    <CardTitle>{t("reservation.rentalInfo.title")}</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {/* Date Range Picker with Unavailable Dates Highlighting */}
                    <div className="space-y-2">
                      <Label>{t("reservation.rentalInfo.date")}</Label>
                      <DatePickerWithRange
                        date={dateRange}
                        setDate={setDateRange}
                        unavailableDates={unavailableDates}
                        className="w-full"
                      />
                      <p className="text-xs text-muted-foreground">
                        Red dates indicate when the car is already rented
                      </p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="pickup-location">{t("reservation.rentalInfo.pickup")}</Label>
                        <Select value={pickupLocation} onValueChange={setPickupLocation} required>
                          <SelectTrigger>
                            <SelectValue placeholder={t("reservation.rentalInfo.locationPlaceholder")} />
                          </SelectTrigger>
                          <SelectContent>
                            {moroccanCities.map((city) => (
                              <SelectItem key={city} value={city}>{city}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="pickup-time">{t("reservation.rentalInfo.time")}</Label>
                        <Select value={pickupTime} onValueChange={setPickupTime} required>
                          <SelectTrigger>
                            <SelectValue placeholder={t("reservation.rentalInfo.timePlaceholder")} />
                          </SelectTrigger>
                          <SelectContent>
                            {timeOptions.map((time) => (
                              <SelectItem key={time} value={time}>{time}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="dropoff-location">{t("reservation.rentalInfo.dropoff")}</Label>
                        <Select value={dropoffLocation} onValueChange={setDropoffLocation} required>
                          <SelectTrigger>
                            <SelectValue placeholder={t("reservation.rentalInfo.locationPlaceholder")} />
                          </SelectTrigger>
                          <SelectContent>
                            {moroccanCities.map((city) => (
                              <SelectItem key={city} value={city}>{city}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="dropoff-time">{t("reservation.rentalInfo.time")}</Label>
                        <Select value={dropoffTime} onValueChange={setDropoffTime} required>
                          <SelectTrigger>
                            <SelectValue placeholder={t("reservation.rentalInfo.timePlaceholder")} />
                          </SelectTrigger>
                          <SelectContent>
                            {timeOptions.map((time) => (
                              <SelectItem key={time} value={time}>{time}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Delivery Options */}
                <Card>
                  <CardHeader>
                    <CardTitle>{t("reservation.delivery.title")}</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <RadioGroup value={deliveryOption} onValueChange={setDeliveryOption}>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="pickup" id="pickup" />
                        <Label htmlFor="pickup">{t("reservation.delivery.pickup")}</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="delivery" id="delivery" />
                        <Label htmlFor="delivery">{t("reservation.delivery.delivery")}</Label>
                      </div>
                    </RadioGroup>
                    {deliveryOption === "delivery" && (
                      <div className="space-y-2">
                        <Label htmlFor="delivery-address">{t("reservation.delivery.address")}</Label>
                        <Input
                          id="delivery-address"
                          placeholder={t("reservation.delivery.addressPlaceholder")}
                          value={deliveryAddress}
                          onChange={(e) => setDeliveryAddress(e.target.value)}
                        />
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Additional Services */}
                <Card>
                  <CardHeader>
                    <CardTitle>{t("reservation.extras.title")}</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="chauffeur"
                        checked={withChauffeur}
                        onCheckedChange={(checked) => setWithChauffeur(checked as boolean)}
                      />
                      <Label htmlFor="chauffeur">{t("reservation.extras.chauffeur")}</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="child-seat"
                        checked={withChildSeat}
                        onCheckedChange={(checked) => setWithChildSeat(checked as boolean)}
                      />
                      <Label htmlFor="child-seat">{t("reservation.extras.childSeat")}</Label>
                    </div>
                  </CardContent>
                </Card>

                {/* Payment Method */}
                <Card>
                  <CardHeader>
                    <CardTitle>{t("reservation.payment.title")}</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <RadioGroup value={paymentMethod} onValueChange={setPaymentMethod}>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="bank_transfer" id="bank_transfer" />
                        <Label htmlFor="bank_transfer">{t("reservation.payment.bankTransfer")}</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="cash" id="cash" />
                        <Label htmlFor="cash">{t("reservation.payment.cash")}</Label>
                      </div>
                    </RadioGroup>
                  </CardContent>
                </Card>

                {/* Coupon Code */}
                <Card>
                  <CardHeader>
                    <CardTitle>{t("reservation.coupon.title")}</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex space-x-2">
                      <Input
                        placeholder={t("reservation.coupon.placeholder")}
                        value={couponCode}
                        onChange={(e) => setCouponCode(e.target.value)}
                      />
                      <Button type="button" onClick={handleApplyCoupon}>
                        {t("reservation.coupon.apply")}
                      </Button>
                    </div>
                    {couponError && (
                      <p className="text-sm text-red-600">{couponError}</p>
                    )}
                    {couponPercent && (
                      <p className="text-sm text-green-600">
                        {couponPercent}% discount applied!
                      </p>
                    )}
                  </CardContent>
                </Card>

                {/* Terms Agreement */}
                <Card>
                  <CardContent className="pt-6">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="terms"
                        checked={agreeToTerms}
                        onCheckedChange={(checked) => setAgreeToTerms(checked as boolean)}
                        required
                      />
                      <Label htmlFor="terms" className="text-sm">
                        {t("reservation.terms.agree")}{" "}
                        {contractUrl ? (
                          <Link href={contractUrl} target="_blank" className="text-primary hover:underline">
                            {t("reservation.terms.contract")}
                          </Link>
                        ) : (
                          <span className="text-primary">{t("reservation.terms.contract")}</span>
                        )}
                      </Label>
                    </div>
                  </CardContent>
                </Card>

                {/* Submit Button */}
                <Button
                  type="submit"
                  className="w-full"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? t("reservation.submitting") : t("reservation.submit")}
                </Button>
              </div>

              {/* Right side - Fixed Summary */}
              <div className="lg:sticky lg:top-8 lg:h-fit">
                {/* Booking Summary */}
                <Card className="bg-gradient-to-br from-blue-50 to-indigo-100 border-blue-200 shadow-lg">
                  <CardHeader className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-t-lg">
                    <CardTitle className="flex items-center gap-2">
                      <CheckCircle className="h-5 w-5" />
                      {t("reservation.summary.title")}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4 p-6">
                    <div className="flex justify-between items-center py-2 border-b border-blue-200">
                      <span className="text-gray-700">Rental ({rentalDuration} {rentalDuration === 1 ? t("reservation.summary.day") : t("reservation.summary.days")})</span>
                      <span className="font-semibold text-gray-900">{convert(rentalCost)}</span>
                    </div>
                    {deliveryOption === "delivery" && (
                      <div className="flex justify-between items-center py-2 border-b border-blue-200">
                        <span className="text-gray-700">Delivery</span>
                        <span className="font-semibold text-gray-900">{convert(deliveryCost)}</span>
                      </div>
                    )}
                    {withChauffeur && (
                      <div className="flex justify-between items-center py-2 border-b border-blue-200">
                        <span className="text-gray-700">{t("reservation.summary.chauffeur")}</span>
                        <span className="font-semibold text-gray-900">{convert(chauffeurCost)}</span>
                      </div>
                    )}
                    {withChildSeat && (
                      <div className="flex justify-between items-center py-2 border-b border-blue-200">
                        <span className="text-gray-700">{t("reservation.summary.childSeat")}</span>
                        <span className="font-semibold text-gray-900">{convert(childSeatCost)}</span>
                      </div>
                    )}
                    {discount > 0 && (
                      <div className="flex justify-between items-center py-2 border-b border-green-200 bg-green-50 rounded px-3">
                        <span className="text-green-700 font-medium">Discount</span>
                        <span className="font-bold text-green-700">-{convert(discount)}</span>
                      </div>
                    )}
                    <div className="border-t-2 border-blue-300 pt-4 mt-4">
                      <div className="flex justify-between items-center">
                        <span className="text-lg font-bold text-gray-900">{t("reservation.summary.total")}</span>
                        <span className="text-xl font-bold text-blue-600">{convert(total)}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </form>
        </>
      )}

      {/* Success Modal */}
      <SuccessModal />
    </div>
  )
}
