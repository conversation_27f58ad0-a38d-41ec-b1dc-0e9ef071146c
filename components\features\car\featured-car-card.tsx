"use client"

import type React from "react"

import { useState } from "react"
import Link from "next/link"
import { StarIcon, ChevronLeft, ChevronRight } from "lucide-react"
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { useCurrency } from "@/contexts/currency-context"

interface FeaturedCarCardProps {
  id: string
  title: string
  price: number
  location: string
  images: string[] // Changed from single image to array of images
  rating: number
  reviews: number
  category: string
  href?: string
  children?: React.ReactNode
}

export function FeaturedCarCard({
  id,
  title,
  price,
  location,
  images,
  rating,
  reviews,
  category,
  href = `/listings/car-details/${id}`,
  children,
}: FeaturedCarCardProps) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const { convert } = useCurrency()

  // Ensure we have at least one image
  const imageArray = images && images.length > 0 ? images : ["/placeholder.svg"]

  // Only show the city (last part after comma)
  const city = location.includes(',') ? location.split(',').pop()?.trim() : location

  const nextImage = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setCurrentImageIndex((prev) => (prev + 1) % imageArray.length)
  }

  const prevImage = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setCurrentImageIndex((prev) => (prev - 1 + imageArray.length) % imageArray.length)
  }

  return (
    <Link href={href}>
      <Card className="overflow-hidden transition-all hover:shadow-lg group">
        <div className="relative">
          <div className="w-full h-48 overflow-hidden">
            <img
              alt={title}
              className="w-full h-full object-cover transition-transform duration-300"
              height={200}
              src={imageArray[currentImageIndex] || "/placeholder.svg"}
              style={{
                aspectRatio: "300/200",
                objectFit: "cover",
              }}
              width={300}
            />
          </div>

          {/* Navigation controls - only show when multiple images exist */}
          {imageArray.length > 1 && (
            <>
              <Button
                variant="ghost"
                size="icon"
                className="absolute left-1 top-1/2 -translate-y-1/2 bg-black/40 text-white rounded-full h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                onClick={prevImage}
                aria-label="Previous image"
              >
                <ChevronLeft className="h-5 w-5" />
              </Button>

              <Button
                variant="ghost"
                size="icon"
                className="absolute right-1 top-1/2 -translate-y-1/2 bg-black/40 text-white rounded-full h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                onClick={nextImage}
                aria-label="Next image"
              >
                <ChevronRight className="h-5 w-5" />
              </Button>

              {/* Image indicators */}
              <div className="absolute bottom-2 left-0 right-0 flex justify-center gap-1">
                {imageArray.map((_, index) => (
                  <div
                    key={index}
                    className={`h-1.5 rounded-full transition-all ${index === currentImageIndex ? "w-4 bg-white" : "w-1.5 bg-white/60"
                      }`}
                  />
                ))}
              </div>
            </>
          )}
        </div>
        <CardContent className="p-4">
          <div className="flex items-start justify-between">
            <div>
              <h3 className="font-semibold">{title}</h3>
              <p className="text-sm text-muted-foreground">{city}</p>
            </div>
            <div className="flex items-center">
              <StarIcon className="h-4 w-4 fill-current text-yellow-500" />
              <span className="ml-1 text-sm font-medium">{rating}</span>
              <span className="ml-1 text-xs text-muted-foreground">({reviews})</span>
            </div>
          </div>
        </CardContent>
        <CardFooter className="p-4 pt-0 flex justify-between items-center">
          <div>
            <span className="font-bold text-primary">{convert(price)}</span>
            <span className="text-sm text-muted-foreground"> / day</span>
          </div>
        </CardFooter>
      </Card>
    </Link>
  )
}
