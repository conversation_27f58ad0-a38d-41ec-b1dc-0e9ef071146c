-- Fix admin user role and authentication issues
-- This script ensures the admin user has the correct role and can access the admin dashboard

-- First, let's check if the admin user exists in auth.users
-- If not, we'll need to create it manually

-- Update the admin user role in the users table
UPDATE public.users 
SET role = 'admin', 
    is_verified = true,
    first_name = 'Admin',
    last_name = 'User'
WHERE email = '<EMAIL>';

-- If the admin user doesn't exist in public.users, create it
INSERT INTO public.users (id, email, first_name, last_name, role, is_verified)
SELECT 
    au.id,
    au.email,
    'Admin',
    'User',
    'admin',
    true
FROM auth.users au
WHERE au.email = '<EMAIL>'
AND NOT EXISTS (
    SELECT 1 FROM public.users pu WHERE pu.id = au.id
);

-- Ensure all users have is_verified = true for testing (remove email confirmation requirement)
UPDATE public.users 
SET is_verified = true 
WHERE is_verified = false;

-- Check the current admin user status
SELECT 
    id,
    email,
    role,
    is_verified,
    created_at
FROM public.users 
WHERE email = '<EMAIL>'; 