import { supabase } from '@/lib/supabase/client'

export class StorageService {
    // Upload a single file
    static async uploadFile(
        bucket: string,
        path: string,
        file: File,
        options?: {
            cacheControl?: string
            upsert?: boolean
        }
    ) {
        try {
            const { data, error } = await supabase.storage
                .from(bucket)
                .upload(path, file, {
                    cacheControl: options?.cacheControl || '3600',
                    upsert: options?.upsert || false
                })

            if (error) {
                throw error
            }

            return { data, error: null }
        } catch (error) {
            console.error('Upload error:', error)
            return { data: null, error }
        }
    }

    // Upload multiple files
    static async uploadMultipleFiles(
        bucket: string,
        files: Array<{ path: string; file: File }>,
        options?: {
            cacheControl?: string
            upsert?: boolean
        }
    ) {
        const uploadPromises = files.map(({ path, file }) =>
            this.uploadFile(bucket, path, file, options)
        )

        try {
            const results = await Promise.all(uploadPromises)
            const errors = results.filter(result => result.error)

            if (errors.length > 0) {
                return { data: null, error: errors[0].error }
            }

            return { data: results.map(r => r.data), error: null }
        } catch (error) {
            return { data: null, error }
        }
    }

    // Get public URL for a file
    static getPublicUrl(bucket: string, path: string) {
        const { data } = supabase.storage
            .from(bucket)
            .getPublicUrl(path)

        return data.publicUrl
    }

    // Delete a file
    static async deleteFile(bucket: string, path: string) {
        try {
            const { error } = await supabase.storage
                .from(bucket)
                .remove([path])

            if (error) {
                throw error
            }

            return { error: null }
        } catch (error) {
            console.error('Delete error:', error)
            return { error }
        }
    }

    // List files in a bucket/folder
    static async listFiles(bucket: string, folder?: string) {
        try {
            const { data, error } = await supabase.storage
                .from(bucket)
                .list(folder || '')

            if (error) {
                throw error
            }

            return { data, error: null }
        } catch (error) {
            console.error('List error:', error)
            return { data: null, error }
        }
    }

    // Download a file
    static async downloadFile(bucket: string, path: string) {
        try {
            const { data, error } = await supabase.storage
                .from(bucket)
                .download(path)

            if (error) {
                throw error
            }

            return { data, error: null }
        } catch (error) {
            console.error('Download error:', error)
            return { data: null, error }
        }
    }

    // Car-specific methods
    static async uploadCarImage(file: File, carId: string): Promise<{ data: string | null, error: any }> {
        const fileExt = file.name.split('.').pop()
        const fileName = `${carId}/${Date.now()}.${fileExt}`

        const uploadResult = await this.uploadFile('car-images', fileName, file)

        if (uploadResult.error) {
            return { data: null, error: uploadResult.error }
        }

        const publicUrl = this.getPublicUrl('car-images', fileName)
        return { data: publicUrl, error: null }
    }

    static async uploadMultipleCarImages(files: File[], carId: string): Promise<{ data: string[] | null, error: any }> {
        const uploadData = files.map((file, index) => ({
            path: `${carId}/${Date.now()}_${index}.${file.name.split('.').pop()}`,
            file
        }))

        const result = await this.uploadMultipleFiles('car-images', uploadData)

        if (result.error) {
            return { data: null, error: result.error }
        }

        const urls = uploadData.map(item => this.getPublicUrl('car-images', item.path))
        return { data: urls, error: null }
    }

    // Agency-specific methods
    static async uploadAgencyLogo(file: File, agencyId: string): Promise<{ data: string | null, error: any }> {
        const fileExt = file.name.split('.').pop()
        const fileName = `${agencyId}/logo.${fileExt}`

        const uploadResult = await this.uploadFile('agency-logos', fileName, file, { upsert: true })

        if (uploadResult.error) {
            return { data: null, error: uploadResult.error }
        }

        const publicUrl = this.getPublicUrl('agency-logos', fileName)
        return { data: publicUrl, error: null }
    }

    // User-specific methods
    static async uploadUserAvatar(file: File, userId: string): Promise<{ data: string | null, error: any }> {
        const fileExt = file.name.split('.').pop()
        const fileName = `${userId}/avatar.${fileExt}`

        const uploadResult = await this.uploadFile('avatars', fileName, file, { upsert: true })

        if (uploadResult.error) {
            return { data: null, error: uploadResult.error }
        }

        const publicUrl = this.getPublicUrl('avatars', fileName)
        return { data: publicUrl, error: null }
    }

    // Document upload (for verification, contracts, etc.)
    static async uploadDocument(file: File, folder: string, fileName?: string): Promise<{ data: string | null, error: any }> {
        const fileExt = file.name.split('.').pop()
        const finalFileName = fileName ? `${fileName}.${fileExt}` : `${Date.now()}.${fileExt}`
        const filePath = `${folder}/${finalFileName}`

        const uploadResult = await this.uploadFile('documents', filePath, file)

        if (uploadResult.error) {
            return { data: null, error: uploadResult.error }
        }

        const publicUrl = this.getPublicUrl('documents', filePath)
        return { data: publicUrl, error: null }
    }

    // Validation methods
    static validateImageFile(file: File): { valid: boolean, error?: string } {
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
        const maxSize = 5 * 1024 * 1024 // 5MB

        if (!allowedTypes.includes(file.type)) {
            return { valid: false, error: 'Only JPEG, PNG, and WebP images are allowed' }
        }

        if (file.size > maxSize) {
            return { valid: false, error: 'File size must be less than 5MB' }
        }

        return { valid: true }
    }

    static validateDocumentFile(file: File): { valid: boolean, error?: string } {
        const allowedTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png']
        const maxSize = 10 * 1024 * 1024 // 10MB

        if (!allowedTypes.includes(file.type)) {
            return { valid: false, error: 'Only PDF, JPEG, and PNG files are allowed' }
        }

        if (file.size > maxSize) {
            return { valid: false, error: 'File size must be less than 10MB' }
        }

        return { valid: true }
    }

    // Utility methods
    static generateFileName(prefix: string, extension: string): string {
        const timestamp = Date.now()
        const random = Math.random().toString(36).substring(2, 8)
        return `${prefix}_${timestamp}_${random}.${extension}`
    }

    static extractFilePathFromUrl(url: string, bucket: string): string | null {
        const bucketPath = `/${bucket}/`
        const index = url.indexOf(bucketPath)

        if (index === -1) {
            return null
        }

        return url.substring(index + bucketPath.length)
    }
}