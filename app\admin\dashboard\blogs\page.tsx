'use client'

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { DatabaseService } from "@/services/database.service"
import { useAuth } from "@/contexts/auth-context"
import { toast } from "sonner"
import dynamic from "next/dynamic"
import "react-quill/dist/quill.snow.css"

const ReactQuill = dynamic(() => import("react-quill"), { ssr: false })

interface BlogFormState {
    title: string
    content: string
    excerpt: string
    image: File | null
    tags: string
    status: 'draft' | 'published'
}

export default function AdminBlogsPage() {
    const [form, setForm] = useState<BlogFormState>({
        title: "",
        content: "",
        excerpt: "",
        image: null,
        tags: "",
        status: 'draft'
    })
    const [isSubmitting, setIsSubmitting] = useState(false)
    const [preview, setPreview] = useState<string | null>(null)
    const router = useRouter()
    const { user } = useAuth()

    function handleChange(e: React.ChangeEvent<HTMLInputElement>) {
        const { name, value } = e.target
        setForm((prev) => ({ ...prev, [name]: value }))
    }

    function handleContentChange(value: string) {
        setForm((prev) => ({ ...prev, content: value }))
    }

    function handleImageChange(e: React.ChangeEvent<HTMLInputElement>) {
        const file = e.target.files?.[0] || null
        setForm((prev) => ({ ...prev, image: file }))
        if (file) setPreview(URL.createObjectURL(file))
        else setPreview(null)
    }

    async function handleSubmit(e: React.FormEvent) {
        e.preventDefault()
        setIsSubmitting(true)

        try {
            if (!form.title || !form.content) {
                toast.error("Please fill in title and content")
                return
            }

            // Create slug from title
            const slug = form.title
                .toLowerCase()
                .replace(/[^a-z0-9]+/g, '-')
                .replace(/(^-|-$)/g, '')

            // Parse tags
            const tags = form.tags
                .split(',')
                .map(tag => tag.trim())
                .filter(tag => tag.length > 0)

            // Create blog post
            const { error } = await DatabaseService.createBlog({
                title: form.title,
                content: form.content,
                excerpt: form.excerpt || form.content.substring(0, 200) + '...',
                slug,
                status: form.status,
                author_id: user?.id,
                tags,
                published_at: form.status === 'published' ? new Date().toISOString() : null
            })

            if (error) {
                console.error('Error creating blog:', error)
                toast.error('Failed to create blog post')
                return
            }

            toast.success(`Blog post ${form.status === 'published' ? 'published' : 'saved as draft'} successfully!`)
            setForm({
                title: "",
                content: "",
                excerpt: "",
                image: null,
                tags: "",
                status: 'draft'
            })
            setPreview(null)

        } catch (error) {
            console.error('Error creating blog:', error)
            toast.error('Failed to create blog post')
        } finally {
            setIsSubmitting(false)
        }
    }

    return (
        <div className="max-w-4xl mx-auto p-6">
            <h1 className="text-3xl font-bold mb-6">Create Blog Post</h1>
            <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label className="block mb-2 font-medium">Title *</label>
                        <Input
                            name="title"
                            placeholder="Enter blog title"
                            value={form.title}
                            onChange={handleChange}
                            required
                        />
                    </div>
                    <div>
                        <label className="block mb-2 font-medium">Status</label>
                        <select
                            name="status"
                            value={form.status}
                            onChange={(e) => setForm(prev => ({ ...prev, status: e.target.value as 'draft' | 'published' }))}
                            className="w-full p-2 border rounded-md"
                        >
                            <option value="draft">Draft</option>
                            <option value="published">Published</option>
                        </select>
                    </div>
                </div>

                <div>
                    <label className="block mb-2 font-medium">Excerpt</label>
                    <Input
                        name="excerpt"
                        placeholder="Brief description of the blog post"
                        value={form.excerpt}
                        onChange={handleChange}
                    />
                </div>

                <div>
                    <label className="block mb-2 font-medium">Tags</label>
                    <Input
                        name="tags"
                        placeholder="Enter tags separated by commas (e.g., travel, cars, tips)"
                        value={form.tags}
                        onChange={handleChange}
                    />
                </div>

                <div>
                    <label className="block mb-2 font-medium">Content *</label>
                    <ReactQuill
                        value={form.content}
                        onChange={handleContentChange}
                        theme="snow"
                        className="bg-white"
                        style={{ minHeight: 300 }}
                    />
                </div>

                <div>
                    <label className="block mb-2 font-medium">Featured Image</label>
                    <Input type="file" accept="image/*" onChange={handleImageChange} />
                    {preview && (
                        <img src={preview} alt="Preview" className="mt-4 rounded w-full max-h-64 object-cover" />
                    )}
                </div>
                <div className="flex gap-4">
                    <Button type="submit" disabled={isSubmitting}>
                        {isSubmitting ? "Saving..." : form.status === 'published' ? "Publish Blog" : "Save Draft"}
                    </Button>
                    <Button type="button" variant="outline" onClick={() => router.back()}>
                        Cancel
                    </Button>
                </div>
            </form>
        </div>
    )
} 