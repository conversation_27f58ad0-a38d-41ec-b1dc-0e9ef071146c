"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { toast } from "sonner"
import {
  Users,
  Building2,
  Car,
  CreditCard,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Eye,
  MoreHorizontal,
  Calendar,
  DollarSign,
  Activity,
  Shield,
  Zap
} from "lucide-react"
import { useAuth } from "@/contexts/auth-context"
import { DatabaseService } from "@/services/database.service"

export default function AdminDashboardPage() {
  const { user } = useAuth()
  const [pendingVerifications, setPendingVerifications] = useState([])
  const [isLoading, setIsLoading] = useState(true)
  const [adminStats, setAdminStats] = useState({
    totalUsers: 0,
    activeAgencies: 0,
    totalCars: 0,
    totalBookings: 0,
    recentActivities: []
  })

  // Enhanced stats with real data
  const stats = [
    {
      title: "Total Users",
      value: adminStats.totalUsers.toLocaleString(),
      icon: Users,
      change: "+12%",
      trend: "up",
      color: "from-blue-500 to-blue-600",
      bgColor: "bg-blue-50",
      iconColor: "text-blue-600"
    },
    {
      title: "Active Agencies",
      value: adminStats.activeAgencies.toLocaleString(),
      icon: Building2,
      change: "+5%",
      trend: "up",
      color: "from-green-500 to-green-600",
      bgColor: "bg-green-50",
      iconColor: "text-green-600"
    },
    {
      title: "Listed Cars",
      value: adminStats.totalCars.toLocaleString(),
      icon: Car,
      change: "+18%",
      trend: "up",
      color: "from-purple-500 to-purple-600",
      bgColor: "bg-purple-50",
      iconColor: "text-purple-600"
    },
    {
      title: "Total Bookings",
      value: adminStats.totalBookings.toLocaleString(),
      icon: CreditCard,
      change: "+24%",
      trend: "up",
      color: "from-orange-500 to-orange-600",
      bgColor: "bg-orange-50",
      iconColor: "text-orange-600"
    },
  ]

  // Enhanced recent activities with unique booking IDs
  const recentActivities = [
    {
      id: "ACT-001",
      action: "New user registered",
      user: "John Doe",
      time: "2 hours ago",
      type: "user",
      icon: Users
    },
    {
      id: "ACT-002",
      action: "New car listed",
      user: "Marrakech Motors",
      time: "4 hours ago",
      type: "car",
      icon: Car,
      bookingId: "BK-2024-001"
    },
    {
      id: "ACT-003",
      action: "Booking completed",
      user: "Sarah Johnson",
      time: "6 hours ago",
      type: "booking",
      icon: Calendar,
      bookingId: "BK-2024-002"
    },
    {
      id: "ACT-004",
      action: "Agency verified",
      user: "Admin",
      time: "8 hours ago",
      type: "agency",
      icon: Shield
    },
    {
      id: "ACT-005",
      action: "Payment processed",
      user: "System",
      time: "12 hours ago",
      type: "payment",
      icon: DollarSign,
      amount: "MAD 1,250"
    },
  ]

  // Load admin data
  useEffect(() => {
    const loadAdminData = async () => {
      try {
        setIsLoading(true)

        // Load admin statistics
        const { data: statsData, error: statsError } = await DatabaseService.getAdminStats()
        if (statsError) {
          console.error('Error loading admin stats:', statsError)
          toast.error('Failed to load dashboard statistics')
        } else if (statsData) {
          setAdminStats(statsData)
          setPendingVerifications(statsData.pendingAgencies)
        }
      } catch (error) {
        console.error('Error loading admin data:', error)
        toast.error('Failed to load dashboard data')
      } finally {
        setIsLoading(false)
      }
    }

    loadAdminData()
  }, [])

  const handleVerifyAgency = async (id: string) => {
    try {
      const { error } = await DatabaseService.verifyAgency(id, user?.id || '')
      if (error) {
        console.error('Error verifying agency:', error)
        toast.error('Failed to verify agency')
        return
      }

      setPendingVerifications(prev => prev.filter(agency => agency.id !== id))
      toast.success('Agency has been verified successfully')

      // Reload stats to update active agencies count
      const { data: statsData } = await DatabaseService.getAdminStats()
      if (statsData) {
        setAdminStats(statsData)
      }
    } catch (error) {
      console.error('Error verifying agency:', error)
      toast.error('Failed to verify agency')
    }
  }

  const handleRejectAgency = (id: string) => {
    setPendingVerifications(prev => prev.filter(agency => agency.id !== id))
    toast.success('Agency has been rejected')
  }

  return (
    <div className="p-8">
      {/* Header */}
      <div className="flex justify-between items-center mb-8">
        <div className="space-y-2">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Admin Dashboard
          </h1>
          <p className="text-muted-foreground text-lg">
            Welcome back, <span className="font-semibold text-blue-600">{user?.first_name || "Admin"}</span>
          </p>
        </div>
        <div className="flex items-center gap-4">
          <Badge variant="secondary" className="bg-green-100 text-green-800">
            <Activity className="w-3 h-3 mr-1" />
            System Online
          </Badge>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {stats.map((stat, index) => (
          <Card
            key={index}
            className="group hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 border-0 shadow-md"
            style={{ animationDelay: `${index * 100}ms` }}
          >
            <CardContent className="p-6">
              <div className="flex justify-between items-start">
                <div className="space-y-2">
                  <p className="text-sm font-medium text-muted-foreground">{stat.title}</p>
                  <h3 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
                    {stat.value}
                  </h3>
                </div>
                <div className={`p-3 rounded-full ${stat.bgColor} group-hover:scale-110 transition-transform duration-300`}>
                  <stat.icon className={`h-6 w-6 ${stat.iconColor}`} />
                </div>
              </div>
              <div className="mt-2 flex items-center">
                <span className={`text-sm ${stat.trend === 'up' ? 'text-green-600' : 'text-red-600'}`}>
                  {stat.change}
                </span>
                <TrendingUp className="h-4 w-4 ml-1 text-green-600" />
                <span className="text-xs text-muted-foreground ml-2">vs last month</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Tabs Section */}
      <Tabs defaultValue="verifications" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2 bg-white shadow-md">
          <TabsTrigger value="verifications" className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-purple-500 data-[state=active]:text-white">
            <AlertTriangle className="w-4 h-4 mr-2" />
            Pending Verifications
          </TabsTrigger>
          <TabsTrigger value="activities" className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-purple-500 data-[state=active]:text-white">
            <Activity className="w-4 h-4 mr-2" />
            Recent Activities
          </TabsTrigger>
        </TabsList>

        <TabsContent value="verifications" className="space-y-4">
          <Card className="border-0 shadow-lg">
            <CardHeader className="bg-gradient-to-r from-amber-50 to-orange-50 border-b">
              <CardTitle className="flex items-center gap-3 text-amber-800">
                <AlertTriangle className="h-6 w-6" />
                Agencies Awaiting Verification
              </CardTitle>
              <CardDescription className="text-amber-700">
                Review and verify these agencies to allow them to list cars
              </CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              <div className="divide-y">
                {isLoading ? (
                  <div className="p-6 text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                    <p className="mt-2 text-gray-600">Loading pending verifications...</p>
                  </div>
                ) : pendingVerifications.length === 0 ? (
                  <div className="p-6 text-center text-gray-500">
                    <CheckCircle className="h-12 w-12 mx-auto mb-2 text-green-500" />
                    <p>No pending agency verifications</p>
                  </div>
                ) : (
                  pendingVerifications.map((agency) => (
                    <div key={agency.id} className="p-6 hover:bg-gray-50 transition-colors duration-200">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          <Avatar className="h-12 w-12">
                            <AvatarImage src={agency.logo_url || "/placeholder.svg"} />
                            <AvatarFallback className="bg-gradient-to-r from-blue-500 to-purple-500 text-white">
                              {agency.name.slice(0, 2).toUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="flex items-center gap-2">
                              <h3 className="font-semibold text-lg">{agency.name}</h3>
                              <Badge variant="outline" className="text-xs">
                                {agency.id}
                              </Badge>
                            </div>
                            <p className="text-muted-foreground">{agency.email}</p>
                            <p className="text-sm text-muted-foreground">
                              Applied: {new Date(agency.created_at).toLocaleDateString()}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button
                            size="sm"
                            className="bg-green-600 hover:bg-green-700 text-white"
                            onClick={() => handleVerifyAgency(agency.id)}
                          >
                            <CheckCircle className="w-4 h-4 mr-1" />
                            Verify
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            className="text-red-600 border-red-200 hover:bg-red-50"
                            onClick={() => handleRejectAgency(agency.id)}
                          >
                            <XCircle className="w-4 h-4 mr-1" />
                            Reject
                          </Button>
                          <Button size="sm" variant="ghost">
                            <Eye className="w-4 h-4 mr-1" />
                            View
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="activities" className="space-y-4">
          <Card className="border-0 shadow-lg">
            <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
              <CardTitle className="flex items-center gap-3 text-blue-800">
                <Activity className="h-6 w-6" />
                Recent Activities
              </CardTitle>
              <CardDescription className="text-blue-700">
                Latest actions performed in the system
              </CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              <div className="divide-y">
                {recentActivities.map((activity) => (
                  <div key={activity.id} className="p-6 hover:bg-gray-50 transition-colors duration-200">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className={`p-2 rounded-full ${activity.type === 'user' ? 'bg-blue-100' :
                          activity.type === 'car' ? 'bg-purple-100' :
                            activity.type === 'booking' ? 'bg-green-100' :
                              activity.type === 'agency' ? 'bg-orange-100' :
                                'bg-gray-100'
                          }`}>
                          <activity.icon className={`w-5 h-5 ${activity.type === 'user' ? 'text-blue-600' :
                            activity.type === 'car' ? 'text-purple-600' :
                              activity.type === 'booking' ? 'text-green-600' :
                                activity.type === 'agency' ? 'text-orange-600' :
                                  'text-gray-600'
                            }`} />
                        </div>
                        <div>
                          <div className="flex items-center gap-2">
                            <p className="font-medium">{activity.action}</p>
                            {activity.bookingId && (
                              <Badge variant="secondary" className="text-xs">
                                {activity.bookingId}
                              </Badge>
                            )}
                            {activity.amount && (
                              <Badge variant="outline" className="text-xs text-green-600">
                                {activity.amount}
                              </Badge>
                            )}
                          </div>
                          <p className="text-sm text-muted-foreground">By {activity.user}</p>
                        </div>
                      </div>
                      <div className="text-sm text-muted-foreground">{activity.time}</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}




