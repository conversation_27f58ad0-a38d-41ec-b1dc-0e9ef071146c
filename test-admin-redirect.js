// Test script to simulate admin redirect flow
require('dotenv').config({ path: '.env.local' })

const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://sagtwjbwgfgvzulnsmhc.supabase.co'
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseKey) {
    console.error('❌ NEXT_PUBLIC_SUPABASE_ANON_KEY not found')
    process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseKey)

async function testAdminRedirectFlow() {
    console.log('🔍 Testing admin redirect flow...\n')

    try {
        // Step 1: Sign in as admin
        console.log('📝 Step 1: Signing in as admin...')
        const { data, error } = await supabase.auth.signInWithPassword({
            email: '<EMAIL>',
            password: 'Admin123!'
        })

        if (error) {
            console.error('❌ Admin sign in failed:', error.message)
            return false
        }

        console.log('✅ Step 1: Admin sign in successful!')
        console.log('   User ID:', data.user?.id)
        console.log('   User Email:', data.user?.email)

        // Step 2: Get user profile
        console.log('\n📝 Step 2: Getting user profile...')
        const { data: profile, error: profileError } = await supabase
            .from('users')
            .select('*')
            .eq('id', data.user.id)
            .single()

        if (profileError) {
            console.error('❌ Step 2: Profile fetch failed:', profileError.message)
            return false
        }

        console.log('✅ Step 2: Profile fetched successfully!')
        console.log('   Profile Role:', profile.role)
        console.log('   Profile Email:', profile.email)

        // Step 3: Simulate admin cookie setting
        console.log('\n📝 Step 3: Simulating admin cookie setting...')
        console.log('✅ Step 3: Admin cookie would be set in browser')
        console.log('   Cookie: adminAuthenticated=true; path=/; max-age=86400')

        // Step 4: Simulate redirect logic
        console.log('\n📝 Step 4: Simulating redirect logic...')
        const currentPath = '/admin-login'

        if (profile.role === 'admin' && !currentPath.startsWith('/admin')) {
            console.log('👑 Step 4: Should redirect admin to /admin/dashboard')
            console.log('   From:', currentPath)
            console.log('   To: /admin/dashboard')
        }

        // Step 5: Simulate admin layout check
        console.log('\n📝 Step 5: Simulating admin layout check...')
        console.log('✅ Step 5: Admin layout would check for adminAuthenticated cookie')
        console.log('✅ Step 5: Admin layout would verify session')
        console.log('✅ Step 5: Admin layout would verify admin role')
        console.log('✅ Step 5: Admin layout would allow access')

        // Step 6: Test session verification
        console.log('\n📝 Step 6: Testing session verification...')
        const { data: { session } } = await supabase.auth.getSession()

        if (session) {
            console.log('✅ Step 6: Session exists')
            console.log('   Session User ID:', session.user.id)
            console.log('   Session User Email:', session.user.email)
        } else {
            console.log('❌ Step 6: No session found')
        }

        // Sign out
        await supabase.auth.signOut()
        console.log('\n✅ Signed out successfully')

        return true
    } catch (err) {
        console.error('❌ Test error:', err.message)
        return false
    }
}

async function main() {
    console.log('🚀 Starting admin redirect flow test...\n')

    const success = await testAdminRedirectFlow()
    if (!success) {
        console.log('\n❌ Admin redirect flow test failed.')
        process.exit(1)
    }

    console.log('\n✅ Admin redirect flow test completed!')
    console.log('\n💡 If the frontend is still not redirecting, the issue might be:')
    console.log('   1. Browser caching of old authentication state')
    console.log('   2. React state update timing issues')
    console.log('   3. Next.js router not responding to redirects')
    console.log('   4. JavaScript errors preventing redirect execution')
}

main().catch(console.error) 