# How to Change the Website Logo - KriwDrive

This guide explains how to change the logo and branding of your KriwDrive website.

## Current Logo Implementation

The website currently uses a **Car icon** with the text **"KriwDrive"** as the logo. This appears in:

1. **Main Navigation Bar** (`components/layout/main-navbar.tsx`)
2. **Mobile Navigation** (`components/layout/mobile-nav.tsx`)
3. **Footer** (`components/layout/site-footer.tsx`)
4. **Admin Panel** (`components/admin/sidebar.tsx`)
5. **Page Headers** (various pages)

## Method 1: Replace with Image Logo

### Step 1: Add Your Logo Image
1. Place your logo image in the `public` folder (e.g., `public/logo.png`)
2. Recommended sizes:
   - **Main logo**: 200x60px (PNG with transparent background)
   - **Favicon**: 32x32px (ICO format)

### Step 2: Update Main Navigation
Edit `components/layout/main-navbar.tsx`:

```tsx
// Replace this section:
<Link className="flex items-center justify-center mr-6 group" href="/">
  <div className="bg-primary/10 p-2 rounded-full transition-all duration-300 group-hover:bg-primary/20">
    <Car className="h-6 w-6 text-primary" />
  </div>
  <span className="ml-2 text-xl font-bold">KriwDrive</span>
</Link>

// With this:
<Link className="flex items-center justify-center mr-6 group" href="/">
  <img 
    src="/logo.png" 
    alt="Your Company Logo" 
    className="h-10 w-auto transition-all duration-300 group-hover:opacity-80"
  />
</Link>
```

### Step 3: Update Mobile Navigation
Edit `components/layout/mobile-nav.tsx`:

```tsx
// Replace this section:
<SheetTitle className="flex items-center justify-center gap-2">
  <Car className="h-6 w-6 text-primary" />
  <span className="ml-2 text-xl font-bold">KriwDrive</span>
</SheetTitle>

// With this:
<SheetTitle className="flex items-center justify-center gap-2">
  <img 
    src="/logo.png" 
    alt="Your Company Logo" 
    className="h-8 w-auto"
  />
</SheetTitle>
```

### Step 4: Update Footer
Edit `components/layout/site-footer.tsx`:

```tsx
// Replace this section:
<div className="flex items-center gap-2">
  <Car className="h-6 w-6 text-primary" />
  <span className="text-xl font-bold">KriwDrive</span>
</div>

// With this:
<div className="flex items-center gap-2">
  <img 
    src="/logo.png" 
    alt="Your Company Logo" 
    className="h-8 w-auto brightness-0 invert"
  />
</div>
```

### Step 5: Update Admin Panel
Edit `components/admin/sidebar.tsx`:

```tsx
// Replace this section:
<div className="flex items-center gap-2 mb-8 px-2">
  <ShieldCheck className="h-8 w-8 text-primary" />
  <div>
    <h2 className="font-bold text-xl">Admin Panel</h2>
    <p className="text-xs text-muted-foreground">KriwDrive Management</p>
  </div>
</div>

// With this:
<div className="flex items-center gap-2 mb-8 px-2">
  <img 
    src="/logo.png" 
    alt="Your Company Logo" 
    className="h-8 w-auto"
  />
  <div>
    <h2 className="font-bold text-xl">Admin Panel</h2>
    <p className="text-xs text-muted-foreground">Your Company Management</p>
  </div>
</div>
```

## Method 2: Change Brand Name Only

If you want to keep the car icon but change the name:

### Step 1: Update Site Metadata
Edit `app/layout.tsx`:

```tsx
export const metadata: Metadata = {
  title: "YourBrand - Find a Rental Car in Morocco",
  description: "Discover Morocco on wheels with YourBrand car rental service",
}
```

### Step 2: Global Text Replacement
Replace "KriwDrive" with your brand name in these files:
- `components/layout/main-navbar.tsx`
- `components/layout/mobile-nav.tsx`
- `components/layout/site-footer.tsx`
- `components/admin/sidebar.tsx`
- `app/page-full.tsx`

## Method 3: Custom Logo Component

Create a reusable logo component:

### Step 1: Create Logo Component
Create `components/ui/logo.tsx`:

```tsx
import Image from 'next/image'
import Link from 'next/link'

interface LogoProps {
  className?: string
  showText?: boolean
  size?: 'sm' | 'md' | 'lg'
}

export function Logo({ className = '', showText = true, size = 'md' }: LogoProps) {
  const sizeClasses = {
    sm: 'h-6',
    md: 'h-8',
    lg: 'h-12'
  }

  return (
    <Link href="/" className={`flex items-center gap-2 ${className}`}>
      <Image
        src="/logo.png"
        alt="Your Company Logo"
        width={120}
        height={40}
        className={`w-auto ${sizeClasses[size]}`}
      />
      {showText && (
        <span className="text-xl font-bold">Your Brand</span>
      )}
    </Link>
  )
}
```

### Step 2: Use Logo Component
Replace logo implementations with:

```tsx
import { Logo } from '@/components/ui/logo'

// In navigation:
<Logo size="md" />

// In footer:
<Logo size="sm" showText={false} />
```

## Additional Branding Updates

### Favicon
Replace `public/favicon.ico` with your favicon

### Colors
Update brand colors in `tailwind.config.js`:

```js
theme: {
  extend: {
    colors: {
      primary: {
        DEFAULT: "#your-primary-color",
        // ... other shades
      }
    }
  }
}
```

### Contact Information
Update contact details in:
- `components/layout/site-footer.tsx`
- Admin login page
- Contact forms

## Testing Your Changes

1. **Development**: Run `npm run dev` and check all pages
2. **Build**: Run `npm run build` to ensure no errors
3. **Mobile**: Test on mobile devices
4. **Print**: Check how logo appears in print styles

## Troubleshooting

**Logo not showing?**
- Check file path is correct
- Ensure image is in `public` folder
- Verify image format (PNG, JPG, SVG supported)

**Logo too large/small?**
- Adjust `className` height values
- Use responsive classes: `h-6 md:h-8 lg:h-10`

**Logo not updating?**
- Clear browser cache
- Restart development server
- Check for typos in file paths

## Need Help?

If you need assistance with logo implementation, please provide:
1. Your logo file (PNG/SVG preferred)
2. Desired brand name
3. Any specific styling requirements
