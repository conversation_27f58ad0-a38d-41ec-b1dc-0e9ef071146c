"use client"

import type React from "react"
import Image from "next/image"

// Data for the steps
const customerSteps = [
    {
        title: "Search & Compare",
        description:
            "Easily find the perfect vehicle by searching our extensive inventory. Filter by location, date, and car type, then compare options side-by-side to find the best fit for your trip and budget.",
        image: "/placeholder.svg?height=400&width=600&text=Search+Illustration",
    },
    {
        title: "Book Your Car",
        description:
            "Once you've made your choice, the booking process is simple and secure. Select your preferred car, confirm the rental details, and complete the reservation in just a few clicks.",
        image: "/placeholder.svg?height=400&width=600&text=Booking+Illustration",
    },
    {
        title: "Confirm & Pay",
        description:
            "Review your booking details and make a secure payment through our trusted platform. You'll receive an instant confirmation with all the necessary information for your upcoming rental.",
        image: "/placeholder.svg?height=400&width=600&text=Payment+Illustration",
    },
    {
        title: "Enjoy Your Ride",
        description:
            "Pick up your car from the designated location and start your adventure. Enjoy the freedom and convenience of exploring Morocco at your own pace with a reliable vehicle from our trusted partners.",
        image: "/placeholder.svg?height=400&width=600&text=Driving+Illustration",
    },
]

const agencySteps = [
    {
        title: "List Your Vehicles",
        description:
            "Join our platform and easily list your fleet of vehicles. Upload high-quality photos, add detailed descriptions, and set your own pricing and availability to attract a wide range of customers.",
        image: "/placeholder.svg?height=400&width=600&text=Listing+Illustration",
    },
    {
        title: "Manage Bookings",
        description:
            "Our intuitive dashboard allows you to effortlessly manage all your bookings. Keep track of reservations, communicate with renters, and handle all the necessary details in one centralized place.",
        image: "/placeholder.svg?height=400&width=600&text=Dashboard+Illustration",
    },
    {
        title: "Grow Your Business",
        description:
            "Expand your reach and grow your rental business by connecting with a larger audience of travelers. Our platform provides the tools and exposure you need to increase your bookings and revenue.",
        image: "/placeholder.svg?height=400&width=600&text=Growth+Illustration",
    },
]

// Step component for alternating layout
const Step = ({
    title,
    description,
    image,
    index,
}: {
    title: string
    description: string
    image: string
    index: number
}) => {
    const isReversed = index % 2 !== 0
    return (
        <div className={`grid md:grid-cols-2 gap-12 items-center ${isReversed ? "md:grid-flow-col-dense" : ""}`}>
            <div className={`${isReversed ? "md:col-start-2" : ""}`}>
                <h3 className="text-3xl font-bold mb-4">{title}</h3>
                <p className="text-muted-foreground text-lg">{description}</p>
            </div>
            <div className="relative h-80 rounded-lg overflow-hidden">
                <Image src={image} alt={title} layout="fill" objectFit="cover" />
            </div>
        </div>
    )
}

export default function HowItWorksPage() {
    return (
        <div className="container mx-auto py-16 px-4">
            {/* For Customers Section */}
            <section className="mb-24">
                <div className="text-center mb-12">
                    <h2 className="text-4xl font-extrabold tracking-tight">How It Works for Customers</h2>
                    <p className="mt-4 text-xl text-muted-foreground">Renting a car has never been easier. Follow these simple steps.</p>
                </div>
                <div className="space-y-20">
                    {customerSteps.map((step, index) => (
                        <Step key={index} {...step} index={index} />
                    ))}
                </div>
            </section>

            {/* For Agencies Section */}
            <section>
                <div className="text-center mb-12">
                    <h2 className="text-4xl font-extrabold tracking-tight">How It Works for Agencies</h2>
                    <p className="mt-4 text-xl text-muted-foreground">Join our network and grow your rental business.</p>
                </div>
                <div className="space-y-20">
                    {agencySteps.map((step, index) => (
                        <Step key={index} {...step} index={index} />
                    ))}
                </div>
            </section>
        </div>
    )
} 