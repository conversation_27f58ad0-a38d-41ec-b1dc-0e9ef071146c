# Supabase Setup Checklist - Fix Authentication Issues

## 🚨 **CRITICAL: Complete Setup Required**

Since authentication is not working for any user type, we need to ensure <PERSON>pa<PERSON> is properly configured.

## 📋 **Step-by-Step Setup**

### 1. **Verify Supabase Project Setup**

**Check in Supabase Dashboard:**
- [ ] Project is created and active
- [ ] Database is running
- [ ] API keys are generated

**Get Your Credentials:**
```bash
# From Supabase Dashboard > Settings > API
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
```

### 2. **Environment Variables Setup**

Create/update `.env.local` file:
```bash
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
```

**Restart your development server after updating env vars!**

### 3. **Database Schema Setup**

**In Supabase Dashboard > SQL Editor, run these in order:**

#### A. Create Tables (from SUPABASE_SCHEMA.sql)
```sql
-- Run the entire SUPABASE_SCHEMA.sql file
-- This creates all tables, RLS policies, and functions
```

#### B. Fix RLS Policies (from SUPABASE_RLS_FIX.sql)
```sql
-- Run the entire SUPABASE_RLS_FIX.sql file
-- This fixes authentication issues
```

### 4. **Create Admin User**

**In Supabase Dashboard > Authentication > Users:**

**Option A: Via Dashboard**
1. Click "Add User"
2. Email: `<EMAIL>`
3. Password: `your-secure-password`
4. Email Confirmed: ✅ Yes

**Option B: Via SQL**
```sql
-- In SQL Editor
INSERT INTO auth.users (
    instance_id,
    id,
    aud,
    role,
    email,
    encrypted_password,
    email_confirmed_at,
    created_at,
    updated_at,
    confirmation_token,
    email_change,
    email_change_token_new,
    recovery_token
) VALUES (
    '00000000-0000-0000-0000-000000000000',
    gen_random_uuid(),
    'authenticated',
    'authenticated',
    '<EMAIL>',
    crypt('your-secure-password', gen_salt('bf')),
    NOW(),
    NOW(),
    NOW(),
    '',
    '',
    '',
    ''
);

-- Then create the profile
INSERT INTO public.users (id, email, first_name, last_name, role, is_verified)
SELECT id, email, 'Admin', 'User', 'admin', true
FROM auth.users
WHERE email = '<EMAIL>';
```

### 5. **Test Authentication**

**Run the debug script:**
1. Open your website
2. Open browser console (F12)
3. Copy and paste the contents of `debug-auth-issues.js`
4. Press Enter
5. Check the output for errors

### 6. **Common Issues & Solutions**

#### Issue: "relation 'users' does not exist"
**Solution:** Run `SUPABASE_SCHEMA.sql` in SQL Editor

#### Issue: "row-level security policy violation"
**Solution:** Run `SUPABASE_RLS_FIX.sql` in SQL Editor

#### Issue: "Invalid API key"
**Solution:** Check environment variables and restart dev server

#### Issue: "Failed to load user profile"
**Solution:** Ensure admin user exists in both `auth.users` and `public.users`

### 7. **Verification Steps**

After setup, verify:
- [ ] Can access Supabase dashboard
- [ ] Tables exist in Database > Tables
- [ ] RLS policies are enabled
- [ ] Admin user exists in Authentication > Users
- [ ] Environment variables are correct
- [ ] Development server restarted

## 🔧 **Manual Supabase Connection Test**

If you want to test the connection manually:

```javascript
// In browser console
const { createClient } = supabase;
const supabaseUrl = 'YOUR_SUPABASE_URL';
const supabaseKey = 'YOUR_ANON_KEY';
const client = createClient(supabaseUrl, supabaseKey);

// Test connection
client.auth.getSession().then(result => {
    console.log('Connection test:', result);
});
```

## 📞 **Need Help?**

If you're still having issues:

1. **Share the debug output** from `debug-auth-issues.js`
2. **Check Supabase Dashboard logs** (Dashboard > Logs)
3. **Verify your project URL and keys** are correct
4. **Ensure you've run both SQL files** in the correct order

## 🚀 **Quick Fix Commands**

```bash
# 1. Check if Supabase is accessible
curl -H "apikey: YOUR_ANON_KEY" "YOUR_SUPABASE_URL/rest/v1/"

# 2. Restart development server
npm run dev
# or
yarn dev

# 3. Clear browser cache and cookies
# Use browser dev tools > Application > Clear Storage
```

The authentication should work after completing these steps!
