// Shared authentication types
export interface User {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    phone?: string;
    avatar?: string;
    role: UserRole;
    isVerified: boolean;
    createdAt: string;
    updatedAt: string;
}

export interface Agency extends User {
    role: 'agency';
    agencyName: string;
    agencyDescription?: string;
    agencyAddress?: string;
    agencyPhone?: string;
    agencyEmail?: string;
    agencyWebsite?: string;
    agencyLogo?: string;
    isApproved: boolean;
    rating?: number;
    totalBookings?: number;
}

export interface Admin extends User {
    role: 'admin';
    permissions: AdminPermission[];
}

export type UserRole = 'user' | 'agency' | 'admin';

export type AdminPermission =
    | 'manage_users'
    | 'manage_agencies'
    | 'manage_cars'
    | 'manage_bookings'
    | 'view_analytics'
    | 'manage_payments'
    | 'manage_settings';

export interface AuthState {
    user: User | null;
    isAuthenticated: boolean;
    isLoading: boolean;
    error: string | null;
}

export interface LoginCredentials {
    email: string;
    password: string;
    rememberMe?: boolean;
}

export interface RegisterCredentials {
    email: string;
    password: string;
    confirmPassword: string;
    firstName: string;
    lastName: string;
    phone?: string;
    role?: 'user' | 'agency';
    agencyName?: string;
    agencyDescription?: string;
}

export interface AuthResponse {
    user: User;
    token: string;
    refreshToken: string;
    expiresIn: number;
}

export interface PasswordResetRequest {
    email: string;
}

export interface PasswordResetConfirm {
    token: string;
    newPassword: string;
    confirmPassword: string;
}

export interface EmailVerification {
    token: string;
}

export interface UpdateProfileData {
    firstName?: string;
    lastName?: string;
    phone?: string;
    avatar?: File;
}

export interface ChangePasswordData {
    currentPassword: string;
    newPassword: string;
    confirmPassword: string;
} 