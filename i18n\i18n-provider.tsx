"use client"

import type React from "react"

import { createContext, useContext, useState, useEffect } from "react"
import { useRouter } from "next/navigation"

// Define supported languages
export const languages = {
  en: { name: "English", nativeName: "English", flag: "🇬🇧" },
  ar: { name: "Arabic", nativeName: "العربية", flag: "🇲🇦" },
  fr: { name: "French", nativeName: "Français", flag: "🇫🇷" },
  es: { name: "Spanish", nativeName: "Español", flag: "🇪🇸" },
}

export type LanguageCode = keyof typeof languages

// Define the context type
type I18nContextType = {
  language: LanguageCode
  t: (key: string, params?: Record<string, string | number>) => string
  changeLanguage: (lang: LanguageCode) => void
  dir: "ltr" | "rtl"
}

// Create the context
const I18nContext = createContext<I18nContextType | undefined>(undefined)

// Translations
const translations = {
  en: {
    // Navbar
    "navbar.home": "Home",
    "navbar.browseCars": "Browse Cars",
    "navbar.agencies": "Agencies",
    "navbar.aboutUs": "About Us",
    "navbar.contactUs": "Contact Us",
    "navbar.agencyDashboard": "Agency Dashboard",
    "navbar.logIn": "Log In",
    "navbar.signUp": "Sign Up",
    "navbar.profile": "Profile",
    "navbar.settings": "Settings",
    "navbar.notifications": "Notifications",
    "navbar.bookings": "Bookings",
    "navbar.logout": "Log out",

    // Home Page
    "home.hero.title": "Discover Morocco on Wheels",
    "home.hero.subtitle": "Rent from trusted agencies across Morocco",
    "home.search.location": "Location",
    "home.search.locationPlaceholder": "Where in Morocco?",
    "home.search.carType": "Car Type",
    "home.search.dates": "Dates",
    "home.search.button": "Search",
    "home.featuredCars.title": "Featured Cars",
    "home.featuredCars.subtitle": "Discover our selection of premium cars from top agencies",
    "home.featuredCars.viewAll": "View All Cars",
    "home.popularDestinations.title": "Popular Destinations",
    "home.popularDestinations.subtitle": "Explore top car rental locations around Morocco",
    "home.topAgencies.title": "Top Rated Agencies",
    "home.topAgencies.subtitle": "Rent from trusted agencies with excellent customer reviews",
    "home.becomeHost.title": "Become a Car Host",
    "home.becomeHost.subtitle":
      "List your car on our platform and start earning. Get bookings from travelers around Morocco.",
    "home.becomeHost.learnMore": "Learn More",
    "home.becomeHost.register": "Register as Agency",

    // How It Works
    "howItWorks.title": "How It Works",
    "howItWorks.subtitle": "Simple steps to rent a car or list your vehicles",
    "howItWorks.forClients": "For Clients",
    "howItWorks.forAgencies": "For Agencies",
    "howItWorks.clients.step1.title": "Search & Compare",
    "howItWorks.clients.step1.description":
      "Search for available cars based on your location and dates, then compare options.",
    "howItWorks.clients.step2.title": "Book Your Car",
    "howItWorks.clients.step2.description": "Select your preferred car and complete the booking process securely.",
    "howItWorks.clients.step3.title": "Confirm & Pay",
    "howItWorks.clients.step3.description": "Confirm your reservation details and make a secure payment.",
    "howItWorks.clients.step4.title": "Enjoy Your Ride",
    "howItWorks.clients.step4.description": "Pick up your car and enjoy exploring Morocco with freedom.",
    "howItWorks.agencies.step1.title": "Register Your Agency",
    "howItWorks.agencies.step1.description":
      "Create an account and complete your agency profile with all necessary details.",
    "howItWorks.agencies.step2.title": "List Your Cars",
    "howItWorks.agencies.step2.description": "Add your vehicles with photos, specifications, and pricing options.",
    "howItWorks.agencies.step3.title": "Manage Bookings",
    "howItWorks.agencies.step3.description": "Receive and manage booking requests through your dashboard.",
    "howItWorks.agencies.step4.title": "Grow Your Business",
    "howItWorks.agencies.step4.description": "Increase your visibility and expand your customer base across Morocco.",
    "howItWorks.clients.benefits.title": "Benefits for Clients",
    "howItWorks.clients.benefits.item1": "Wide selection of vehicles",
    "howItWorks.clients.benefits.item2": "Transparent pricing",
    "howItWorks.clients.benefits.item3": "Verified agencies",
    "howItWorks.clients.benefits.item4": "Secure booking process",
    "howItWorks.agencies.benefits.title": "Benefits for Agencies",
    "howItWorks.agencies.benefits.item1": "Increased visibility",
    "howItWorks.agencies.benefits.item2": "Streamlined booking management",
    "howItWorks.agencies.benefits.item3": "Access to a wider customer base",
    "howItWorks.agencies.benefits.item4": "Detailed analytics and insights",

    // Listings Page
    "listings.title": "Available Cars",
    "listings.carsFound": "{{count}} cars found",
    "listings.filters.title": "Filters",
    "listings.filters.clearAll": "Clear all",
    "listings.filters.priceRange": "Price Range",
    "listings.filters.carType": "Car Type",
    "listings.filters.features": "Features",
    "listings.filters.rating": "Rating",
    "listings.search.placeholder": "Search cars...",
    "listings.sort.recommended": "Recommended",
    "listings.sort.priceLow": "Price: Low to High",
    "listings.sort.priceHigh": "Price: High to Low",
    "listings.sort.ratingHigh": "Highest Rated",
    "listings.noResults.title": "No cars found",
    "listings.noResults.subtitle": "Try adjusting your search criteria",
    "listings.loadMore": "Load More",

    // Car Types
    "carType.any": "Any type",
    "carType.sedan": "Sedan",
    "carType.suv": "SUV",
    "carType.coupe": "Coupe",
    "carType.convertible": "Convertible",
    "carType.luxury": "Luxury",
    "carType.electric": "Electric",
    "carType.hybrid": "Hybrid",
    "carType.compact": "Compact",
    "carType.minivan": "Minivan",
    "carType.pickup": "Pickup",
    "carType.sport": "Sport",
    "carType.economy": "Economy",

    // Reservation Page
    "reservation.title": "Confirm Your Reservation",
    "reservation.subtitle": "Please provide your details to complete your booking",
    "reservation.personalInfo.title": "Personal Information",
    "reservation.personalInfo.firstName": "First Name",
    "reservation.personalInfo.lastName": "Last Name",
    "reservation.personalInfo.phone": "Phone Number",
    "reservation.personalInfo.phonePlaceholder": "Enter your phone number",
    "reservation.personalInfo.idOrPassport": "ID Card or Passport (Upload)",
    "reservation.personalInfo.license": "Driving License (Upload)",
    "reservation.personalInfo.uploadHint": "Click to upload a single image",
    "reservation.personalInfo.uploadFormat": "PNG, JPG or PDF (max. 5MB)",
    "reservation.rentalInfo.title": "Rental Info",
    "reservation.rentalInfo.pickup": "Pick-Up",
    "reservation.rentalInfo.dropoff": "Drop-Off",
    "reservation.rentalInfo.location": "Location",
    "reservation.rentalInfo.locationPlaceholder": "Select your city",
    "reservation.rentalInfo.date": "Date",
    "reservation.rentalInfo.datePlaceholder": "Select your date",
    "reservation.rentalInfo.time": "Time",
    "reservation.rentalInfo.timePlaceholder": "Select your time",
    "reservation.terms.agree": "I agree on the agency",
    "reservation.terms.contract": "contract",
    "reservation.submit": "Submit Request",
    "reservation.submitting": "Submitting...",
    "reservation.summary.title": "Booking Summary",
    "reservation.summary.days": "days",
    "reservation.summary.day": "day",
    "reservation.summary.chauffeur": "With chauffeur",
    "reservation.summary.childSeat": "Child seat",
    "reservation.summary.paymentMethod": "Payment method",
    "reservation.summary.serviceFee": "Service fee",
    "reservation.summary.total": "Total",
    "reservation.delivery.title": "Delivery Options",
    "reservation.delivery.pickup": "Pick up at agency",
    "reservation.delivery.delivery": "Delivery to address",
    "reservation.delivery.address": "Delivery Address",
    "reservation.delivery.addressPlaceholder": "Enter delivery address",
    "reservation.extras.title": "Additional Services",
    "reservation.extras.chauffeur": "With Chauffeur",
    "reservation.extras.childSeat": "Child Seat",
    "reservation.payment.title": "Payment Method",
    "reservation.payment.bankTransfer": "Bank Transfer",
    "reservation.payment.cash": "Cash on Pickup",
    "reservation.coupon.title": "Coupon Code",
    "reservation.coupon.placeholder": "Enter coupon code",
    "reservation.coupon.apply": "Apply",
    "reservation.coupon.invalid": "Invalid or not applicable for this agency.",
    "reservation.fillAllFields": "Please fill in all required fields, upload documents, and agree to the terms.",
    "reservation.success": "Your reservation request has been sent to the agency.",

    // Dashboard
    "dashboard.welcome": "Welcome",
    "dashboard.activeRentals": "Active Rentals",
    "dashboard.upcomingBookings": "Upcoming Bookings",
    "dashboard.favoriteLocations": "Favorite Locations",
    "dashboard.notifications": "Notifications",
    "dashboard.myBookings": "My Bookings",
    "dashboard.favorites": "Favorites",
    "dashboard.rentalHistory": "Rental History",
    "dashboard.viewDetails": "View Details",

    // Agency Dashboard
    "agencyDashboard.totalCars": "Total Cars",
    "agencyDashboard.activeBookings": "Active Bookings",
    "agencyDashboard.monthlyRevenue": "Monthly Revenue",
    "agencyDashboard.averageRating": "Average Rating",

    // For Agencies Page
    "forAgencies.hero.badge": "For Car Rental Agencies",
    "forAgencies.hero.title": "Grow Your Car Rental Business",
    "forAgencies.hero.subtitle": "Join Morocco's leading car rental platform and access powerful tools to manage your fleet, increase bookings, and grow your revenue.",
    "forAgencies.hero.getStarted": "Get Started Today",
    "forAgencies.hero.viewDemo": "View Demo",
    "forAgencies.stats.activeAgencies": "Active Agencies",
    "forAgencies.stats.totalRevenue": "Total Revenue",
    "forAgencies.stats.customerSatisfaction": "Customer Satisfaction",
    "forAgencies.stats.averageRating": "Average Rating",
    "forAgencies.features.title": "Everything You Need to Succeed",
    "forAgencies.features.subtitle": "Our comprehensive platform provides all the tools and features you need to manage your car rental business efficiently and profitably.",
    "forAgencies.features.carManagement.title": "Comprehensive Car Management",
    "forAgencies.features.carManagement.description": "Easily manage your entire fleet with our intuitive dashboard. Add, edit, and remove vehicles with detailed specifications, pricing, and availability tracking. Our system supports all vehicle types from economy cars to luxury vehicles.",
    "forAgencies.features.carManagement.benefit1": "Unlimited vehicle listings",
    "forAgencies.features.carManagement.benefit2": "Real-time availability updates",
    "forAgencies.features.carManagement.benefit3": "Detailed vehicle specifications",
    "forAgencies.features.carManagement.benefit4": "Photo gallery management",
    "forAgencies.features.carManagement.benefit5": "Pricing flexibility",
    "forAgencies.features.bookingSystem.title": "Advanced Booking System",
    "forAgencies.features.bookingSystem.description": "Streamline your booking process with our intelligent reservation system. Receive instant booking requests, manage calendars, and handle customer communications all in one place. Never miss a booking opportunity again.",
    "forAgencies.features.bookingSystem.benefit1": "Instant booking notifications",
    "forAgencies.features.bookingSystem.benefit2": "Calendar integration",
    "forAgencies.features.bookingSystem.benefit3": "Customer communication tools",
    "forAgencies.features.bookingSystem.benefit4": "Booking status tracking",
    "forAgencies.features.bookingSystem.benefit5": "Automated confirmations",
    "forAgencies.features.reviews.title": "Customer Reviews & Ratings",
    "forAgencies.features.reviews.description": "Build trust and credibility with our comprehensive review system. Monitor customer feedback, respond to reviews, and maintain your agency's reputation. Positive reviews help attract more customers.",
    "forAgencies.features.reviews.benefit1": "Review management dashboard",
    "forAgencies.features.reviews.benefit2": "Response tools",
    "forAgencies.features.reviews.benefit3": "Rating analytics",
    "forAgencies.features.reviews.benefit4": "Reputation monitoring",
    "forAgencies.features.reviews.benefit5": "Customer feedback insights",
    "forAgencies.features.gps.title": "GPS Tracking & Fleet Management",
    "forAgencies.features.gps.description": "Keep track of your vehicles in real-time with our advanced GPS tracking system. Monitor vehicle locations, track usage patterns, and ensure the safety of your fleet. Perfect for large agencies with multiple vehicles.",
    "forAgencies.features.gps.benefit1": "Real-time vehicle tracking",
    "forAgencies.features.gps.benefit2": "Route optimization",
    "forAgencies.features.gps.benefit3": "Usage analytics",
    "forAgencies.features.gps.benefit4": "Geofencing alerts",
    "forAgencies.features.gps.benefit5": "Fleet performance insights",
    "forAgencies.features.coupons.title": "Coupon & Promotion Tools",
    "forAgencies.features.coupons.description": "Boost your business with our powerful marketing tools. Create custom coupons, seasonal promotions, and loyalty programs to attract and retain customers. Increase your bookings with targeted marketing campaigns.",
    "forAgencies.features.coupons.benefit1": "Custom coupon creation",
    "forAgencies.features.coupons.benefit2": "Seasonal promotions",
    "forAgencies.features.coupons.benefit3": "Loyalty programs",
    "forAgencies.features.coupons.benefit4": "Marketing analytics",
    "forAgencies.features.coupons.benefit5": "Customer retention tools",
    "forAgencies.features.analytics.title": "Advanced Analytics & Insights",
    "forAgencies.features.analytics.description": "Make data-driven decisions with our comprehensive analytics dashboard. Track revenue, monitor performance metrics, analyze customer behavior, and identify growth opportunities. Our insights help you optimize your business.",
    "forAgencies.features.analytics.benefit1": "Revenue tracking",
    "forAgencies.features.analytics.benefit2": "Performance metrics",
    "forAgencies.features.analytics.benefit3": "Customer analytics",
    "forAgencies.features.analytics.benefit4": "Growth insights",
    "forAgencies.features.analytics.benefit5": "Business intelligence",
    "forAgencies.cta.title": "Ready to Transform Your Business?",
    "forAgencies.cta.subtitle": "Join hundreds of successful car rental agencies across Morocco and start growing your business today.",
    "forAgencies.cta.register": "Register Your Agency",
    "forAgencies.cta.scheduleDemo": "Schedule a Demo",

    // Auth
    "auth.pleaseSignIn": "Please sign in to view your dashboard",
    "auth.signIn": "Sign In",
    "auth.accessDenied": "Access Denied",
    "auth.noPermission":
      "You don't have permission to access this page. This area is restricted to authorized users only.",
    "auth.goBack": "Go Back",
    "auth.goToHomepage": "Go to Homepage",

    // Auth Page
    "auth.welcomeBack": "Welcome back",
    "auth.loginSubtitle": "Log in to your morentcar account",
    "auth.email": "Email",
    "auth.emailPlaceholder": "<EMAIL>",
    "auth.password": "Password",
    "auth.passwordPlaceholder": "••••••••",
    "auth.rememberMe": "Remember me",
    "auth.forgotPassword": "Forgot password?",
    "auth.logIn": "Log In",
    "auth.loggingIn": "Logging in...",
    "auth.dontHaveAccount": "Don't have an account?",
    "auth.signUp": "Sign up",
    "auth.createAccount": "Create an account",
    "auth.signupSubtitle": "Sign up to get started with morentcar",
    "auth.firstName": "First name",
    "auth.firstNamePlaceholder": "John",
    "auth.lastName": "Last name",
    "auth.lastNamePlaceholder": "Doe",
    "auth.joinAs": "I want to join as",
    "auth.traveler": "Client",
    "auth.rentalAgency": "Rental Agency",
    "auth.agreeToTerms": "I agree to the",
    "auth.termsOfService": "terms of service",
    "auth.and": "and",
    "auth.privacyPolicy": "privacy policy",
    "auth.createAccountButton": "Create Account",
    "auth.creatingAccount": "Creating Account...",
    "auth.alreadyHaveAccount": "Already have an account?",
    "auth.logInLink": "Log in",
    "auth.fillAllFields": "Please fill in all required fields",
    "auth.agreeToTermsError": "You must agree to the terms of service",
    "auth.loginSuccess": "Login successful!",
    "auth.invalidCredentials": "Invalid email or password",
    "auth.loginError": "An error occurred during login",
    "auth.accountCreated": "Account created successfully!",
    "auth.failedToCreateAccount": "Failed to create account",
    "auth.signupError": "An error occurred during signup",
    "auth.completeRegistration": "Complete Registration",
    "auth.completeRegistrationMessage": "Please complete your registration by filling in your profile information.",

    // Common
    "common.loading": "Loading...",
    "common.price": "Price",
    "common.mileage": "Mileage",
    "common.insurance": "Insurance",
    "common.deposit": "Deposit",
    "common.edit": "Edit",
    "common.delete": "Delete",
    "common.days": "days",
    "common.day": "day",
    "common.viewReceipt": "View Receipt",

    // FAQ Page
    "faq.title": "Frequently Asked Questions",
    "faq.subtitle": "Find answers to common questions about our car and motorcycle rental services",
    "faq.contactMessage": "Still have questions? We're here to help!",
    "faq.contactButton": "Contact Us",
    "faq.categories.booking.title": "Booking & Reservations",
    "faq.categories.booking.q1.question": "How do I make a reservation?",
    "faq.categories.booking.q1.answer": "You can make a reservation through our website by selecting your desired location, dates, and vehicle type. Follow the booking process to confirm your reservation. You can also call our customer service for assistance.",
    "faq.categories.booking.q2.question": "Can I modify or cancel my reservation?",
    "faq.categories.booking.q2.answer": "Yes, you can modify or cancel your reservation up to 48 hours before the pickup time for a full refund. Modifications can be made through your account dashboard or by contacting our customer service.",
    "faq.categories.booking.q3.question": "What is the minimum rental period?",
    "faq.categories.booking.q3.answer": "The minimum rental period is 24 hours. However, some vehicles may have different minimum rental periods based on availability and location.",
    "faq.categories.requirements.title": "Requirements & Documents",
    "faq.categories.requirements.q1.question": "What documents do I need to rent a vehicle?",
    "faq.categories.requirements.q1.answer": "You'll need a valid driver's license, passport or ID card, and a credit card for the security deposit. International drivers will need an International Driving Permit (IDP) along with their national license.",
    "faq.categories.requirements.q2.question": "What are the age requirements?",
    "faq.categories.requirements.q2.answer": "The minimum age to rent a vehicle is 21 years old. Drivers under 25 may be subject to additional fees and restrictions. Some luxury vehicles may require drivers to be 25 or older.",
    "faq.categories.requirements.q3.question": "Do I need insurance?",
    "faq.categories.requirements.q3.answer": "Basic insurance is included in all our rentals. However, we recommend purchasing additional coverage for better protection. You can choose from various insurance options during the booking process.",
    "faq.categories.pricing.title": "Pricing & Payments",
    "faq.categories.pricing.q1.question": "What payment methods do you accept?",
    "faq.categories.pricing.q1.answer": "We accept all major credit cards (Visa, Mastercard, American Express), debit cards, and cash payments. Online bookings require a credit card for the reservation.",
    "faq.categories.pricing.q2.question": "What is included in the rental price?",
    "faq.categories.pricing.q2.answer": "The rental price includes the vehicle, basic insurance, and unlimited mileage. Additional services like GPS, child seats, or additional drivers may incur extra charges.",
    "faq.categories.pricing.q3.question": "How is the security deposit calculated?",
    "faq.categories.pricing.q3.answer": "The security deposit amount varies based on the vehicle type and rental duration. It's typically between 20-30% of the total rental cost. The deposit is refunded after the vehicle is returned in good condition.",
    "faq.categories.vehicle.title": "Vehicle & Services",
    "faq.categories.vehicle.q1.question": "What happens if the vehicle breaks down?",
    "faq.categories.vehicle.q1.answer": "We provide 24/7 roadside assistance. If your vehicle breaks down, call our emergency number and we'll arrange for assistance or a replacement vehicle if necessary.",
    "faq.categories.vehicle.q2.question": "Can I drive the vehicle to another country?",
    "faq.categories.vehicle.q2.answer": "Cross-border travel is allowed to certain countries. Please check with our customer service before your trip to confirm the specific requirements and restrictions.",
    "faq.categories.vehicle.q3.question": "What fuel policy do you have?",
    "faq.categories.vehicle.q3.answer": "Our standard policy is 'full-to-full'. You'll receive the vehicle with a full tank and should return it with a full tank. If you return it with less fuel, you'll be charged for the missing fuel plus a service fee.",

    // Enhanced Auth Messages
    "auth.confirmPassword": "Confirm Password",
    "auth.confirmPasswordPlaceholder": "Confirm your password",
    "auth.passwordsDoNotMatch": "Passwords do not match",
    "auth.passwordStrength": "Password must be at least 8 characters long",
    "auth.emailInvalid": "Please enter a valid email address",
    "auth.redirectingToDashboard": "Redirecting to dashboard...",

    // Password Reset Flow
    "auth.resetPassword": "Reset your password",
    "auth.resetPasswordDescription": "Enter your email address and we'll send you a reset code.",
    "auth.enterEmail": "Please enter your email address",
    "auth.enterResetCode": "Enter reset code",
    "auth.enterResetCodeDescription": "We've sent a 6-digit code to your email. Enter it below.",
    "auth.resetCode": "Reset Code",
    "auth.resetCodeSentTo": "Reset code sent to",
    "auth.invalidResetCode": "Invalid reset code. Please try again.",
    "auth.createNewPassword": "Create new password",
    "auth.createNewPasswordDescription": "Enter your new password below.",
    "auth.newPassword": "New Password",
    "auth.newPasswordPlaceholder": "Enter your new password",
    "auth.confirmNewPassword": "Confirm New Password",
    "auth.confirmNewPasswordPlaceholder": "Confirm your new password",
    "auth.passwordResetSuccess": "Password reset successful!",
    "auth.passwordResetSuccessDescription": "Your password has been successfully reset. You can now log in with your new password.",
    "auth.passwordResetError": "Failed to reset password. Please try again.",
    "auth.sendResetCode": "Send Reset Code",
    "auth.verifyCode": "Verify Code",
    "auth.processing": "Processing...",
    "auth.close": "Close",

    // Reservation Success Modal
    "reservation.successModal.title": "Booking Request Sent!",
    "reservation.successModal.message": "Your booking request has been sent to the agency. They will review your request and get back to you within 24 hours.",
    "reservation.successModal.bookingId": "Booking ID",
    "reservation.successModal.continue": "Continue",
  },
  ar: {
    // Navbar
    "navbar.home": "الرئيسية",
    "navbar.browseCars": "تصفح السيارات",
    "navbar.agencies": "الوكالات",
    "navbar.aboutUs": "من نحن",
    "navbar.contactUs": "اتصل بنا",
    "navbar.agencyDashboard": "لوحة تحكم الوكالة",
    "navbar.logIn": "تسجيل الدخول",
    "navbar.signUp": "إنشاء حساب",
    "navbar.profile": "الملف الشخصي",
    "navbar.settings": "الإعدادات",
    "navbar.notifications": "الإشعارات",
    "navbar.bookings": "الحجوزات",
    "navbar.logout": "تسجيل الخروج",

    // Home Page
    "home.hero.title": " اكتشف المغرب",
    "home.hero.subtitle": "استأجر من وكالات موثوقة في جميع مدن المغرب",
    "home.search.location": "الموقع",
    "home.search.locationPlaceholder": "أين في المغرب؟",
    "home.search.carType": "نوع السيارة",
    "home.search.dates": "التواريخ",
    "home.search.button": "بحث",
    "home.featuredCars.title": "سيارات مميزة",
    "home.featuredCars.subtitle": "اكتشف مجموعتنا من السيارات الفاخرة من أفضل الوكالات",
    "home.featuredCars.viewAll": "عرض جميع السيارات",
    "home.popularDestinations.title": "الوجهات الشائعة",
    "home.topAgencies.title": "أفضل الوكالات تقييماً",
    "home.topAgencies.subtitle": "استأجر من وكالات موثوقة ذات تقييمات ممتازة من العملاء",
    "home.becomeHost.title": "كن مضيفاً للسيارات",
    "home.becomeHost.subtitle":
      "أضف سيارتك على منصتنا وابدأ في الربح. احصل على حجوزات من المسافرين في جميع أنحاء المغرب.",
    "home.becomeHost.learnMore": "معرفة المزيد",
    "home.becomeHost.register": "التسجيل كوكالة",

    // How It Works
    "howItWorks.title": "كيف يعمل",
    "howItWorks.subtitle": "خطوات بسيطة لاستئجار سيارة أو إدراج مركباتك",
    "howItWorks.forClients": "للعملاء",
    "howItWorks.forAgencies": "للوكالات",
    "howItWorks.clients.step1.title": "البحث والمقارنة",
    "howItWorks.clients.step1.description": "ابحث عن السيارات المتاحة بناءً على موقعك وتواريخك، ثم قارن الخيارات.",
    "howItWorks.clients.step2.title": "احجز سيارتك",
    "howItWorks.clients.step2.description": "اختر السيارة المفضلة لديك وأكمل عملية الحجز بأمان.",
    "howItWorks.clients.step3.title": "التأكيد والدفع",
    "howItWorks.clients.step3.description": "قم بتأكيد تفاصيل حجزك وإجراء عملية دفع آمنة.",
    "howItWorks.clients.step4.title": "استمتع برحلتك",
    "howItWorks.clients.step4.description": "استلم سيارتك واستمتع باستكشاف المغرب بحرية.",
    "howItWorks.agencies.step1.title": "سجل وكالتك",
    "howItWorks.agencies.step1.description": "أنشئ حسابًا وأكمل ملف وكالتك بجميع التفاصيل اللازمة.",
    "howItWorks.agencies.step2.title": "أضف سياراتك",
    "howItWorks.agencies.step2.description": "أضف مركباتك مع الصور والمواصفات وخيارات التسعير.",
    "howItWorks.agencies.step3.title": "إدارة الحجوزات",
    "howItWorks.agencies.step3.description": "استلم وأدر طلبات الحجز من خلال لوحة التحكم الخاصة بك.",
    "howItWorks.agencies.step4.title": "نمي عملك",
    "howItWorks.agencies.step4.description": "زد من ظهورك ووسع قاعدة عملائك في جميع أنحاء المغرب.",
    "howItWorks.clients.benefits.title": "فوائد للعملاء",
    "howItWorks.clients.benefits.item1": "مجموعة واسعة من المركبات",
    "howItWorks.clients.benefits.item2": "أسعار شفافة",
    "howItWorks.clients.benefits.item3": "وكالات موثقة",
    "howItWorks.clients.benefits.item4": "عملية حجز آمنة",
    "howItWorks.agencies.benefits.title": "فوائد للوكالات",
    "howItWorks.agencies.benefits.item1": "زيادة الظهور",
    "howItWorks.agencies.benefits.item2": "إدارة حجز مبسطة",
    "howItWorks.agencies.benefits.item3": "الوصول إلى قاعدة عملاء أوسع",
    "howItWorks.agencies.benefits.item4": "تحليلات ورؤى مفصلة",

    // Listings Page
    "listings.title": "السيارات المتاحة",
    "listings.carsFound": "تم العثور على {{count}} سيارة",
    "listings.filters.title": "الفلاتر",
    "listings.filters.clearAll": "مسح الكل",
    "listings.filters.priceRange": "نطاق السعر",
    "listings.filters.carType": "نوع السيارة",
    "listings.filters.features": "المميزات",
    "listings.filters.rating": "التقييم",
    "listings.search.placeholder": "البحث عن سيارات...",
    "listings.sort.recommended": "موصى به",
    "listings.sort.priceLow": "السعر: من الأقل إلى الأعلى",
    "listings.sort.priceHigh": "السعر: من الأعلى إلى الأقل",
    "listings.sort.ratingHigh": "الأعلى تقييماً",
    "listings.noResults.title": "لم يتم العثور على سيارات",
    "listings.noResults.subtitle": "حاول تعديل معايير البحث الخاصة بك",
    "listings.loadMore": "تحميل المزيد",

    // Car Types
    "carType.any": "أي نوع",
    "carType.sedan": "سيدان",
    "carType.suv": "دفع رباعي",
    "carType.coupe": "كوبيه",
    "carType.convertible": "مكشوفة",
    "carType.luxury": "فاخرة",
    "carType.electric": "كهربائية",
    "carType.hybrid": "هجينة",
    "carType.compact": "مدمجة",
    "carType.minivan": "ميني فان",
    "carType.pickup": "بيك أب",
    "carType.sport": "رياضية",
    "carType.economy": "اقتصادية",

    // Reservation Page
    "reservation.title": "تأكيد الحجز",
    "reservation.subtitle": "يرجى إكمال تفاصيل حجزك",
    "reservation.personalInfo.title": "المعلومات الشخصية",
    "reservation.personalInfo.firstName": "الاسم الأول",
    "reservation.personalInfo.lastName": "اسم العائلة",
    "reservation.personalInfo.phone": "رقم الهاتف",
    "reservation.personalInfo.phonePlaceholder": "+212 XXXXXXXXX",
    "reservation.personalInfo.idOrPassport": "بطاقة الهوية أو جواز السفر (تحميل)",
    "reservation.personalInfo.license": "رخصة القيادة (تحميل)",
    "reservation.personalInfo.uploadHint": "انقر لتحميل صورة واحدة",
    "reservation.personalInfo.uploadFormat": "PNG أو JPG أو PDF (بحد أقصى 5 ميجابايت)",
    "reservation.rentalInfo.title": "معلومات الإيجار",
    "reservation.rentalInfo.pickup": "الاستلام",
    "reservation.rentalInfo.dropoff": "التسليم",
    "reservation.rentalInfo.location": "الموقع",
    "reservation.rentalInfo.locationPlaceholder": "اختر مدينتك",
    "reservation.rentalInfo.date": "التاريخ",
    "reservation.rentalInfo.datePlaceholder": "اختر تاريخك",
    "reservation.rentalInfo.time": "الوقت",
    "reservation.rentalInfo.timePlaceholder": "اختر وقتك",
    "reservation.terms.agree": "أوافق على",
    "reservation.terms.contract": "عقد الوكالة",
    "reservation.submit": "إرسال الطلب",
    "reservation.submitting": "جاري الإرسال...",
    "reservation.summary.title": "ملخص الحجز",
    "reservation.summary.days": "أيام",
    "reservation.summary.day": "يوم",
    "reservation.summary.chauffeur": "مع سائق",
    "reservation.summary.childSeat": "مقعد طفل",
    "reservation.summary.paymentMethod": "طريقة الدفع",
    "reservation.summary.serviceFee": "رسوم الخدمة",
    "reservation.summary.total": "المجموع",
    "reservation.delivery.title": "خيارات التوصيل",
    "reservation.delivery.pickup": "الاستلام من الوكالة",
    "reservation.delivery.delivery": "التسليم إلى العنوان",
    "reservation.delivery.address": "عنوان التسليم",
    "reservation.delivery.addressPlaceholder": "أدخل عنوان التسليم",
    "reservation.extras.title": "خدمات إضافية",
    "reservation.extras.chauffeur": "مع سائق",
    "reservation.extras.childSeat": "مقعد طفل",
    "reservation.payment.title": "طريقة الدفع",
    "reservation.payment.bankTransfer": "تحويل بنكي",
    "reservation.payment.cash": "الدفع نقدي عند الاستلام",
    "reservation.coupon.title": "رمز الكوبون",
    "reservation.coupon.placeholder": "أدخل رمز الكوبون",
    "reservation.coupon.apply": "تطبيق",
    "reservation.coupon.invalid": "غير صالح أو غير مطبق على هذه الوكالة.",
    "reservation.fillAllFields": "يرجى ملء جميع الحقول المطلوبة وتحميل المستندات والموافقة على الشروط.",
    "reservation.success": "تم إرسال طلب حجزك إلى الوكالة بنجاح.",

    // Dashboard
    "dashboard.welcome": "مرحبًا",
    "dashboard.activeRentals": "الإيجارات النشطة",
    "dashboard.upcomingBookings": "الحجوزات القادمة",
    "dashboard.favoriteLocations": "المواقع المفضلة",
    "dashboard.notifications": "الإشعارات",
    "dashboard.myBookings": "حجوزاتي",
    "dashboard.favorites": "المفضلة",
    "dashboard.rentalHistory": "سجل الإيجار",
    "dashboard.viewDetails": "عرض التفاصيل",

    // Agency Dashboard
    "agencyDashboard.totalCars": "إجمالي السيارات",
    "agencyDashboard.activeBookings": "الحجوزات النشطة",
    "agencyDashboard.monthlyRevenue": "الإيرادات الشهرية",
    "agencyDashboard.averageRating": "التقييم المتوسط",

    // For Agencies Page
    "forAgencies.hero.badge": "للوكالات",
    "forAgencies.hero.title": "نمي عملك",
    "forAgencies.hero.subtitle": "أضف سيارتك على منصتنا وابدأ في الربح. احصل على حجوزات من المسافرين في جميع أنحاء المغرب.",
    "forAgencies.hero.getStarted": "ابدأ اليوم",
    "forAgencies.hero.viewDemo": "عرض العرض",
    "forAgencies.stats.activeAgencies": "الوكالات النشطة",
    "forAgencies.stats.totalRevenue": "الإيرادات الكلية",
    "forAgencies.stats.customerSatisfaction": "رضا العملاء",
    "forAgencies.stats.averageRating": "التقييم المتوسط",
    "forAgencies.features.title": "كل ما تحتاجه للنجاح",
    "forAgencies.features.subtitle": "منصة كاملة لدينا توفر كل الأدوات والميزات التي تحتاجها لإدارة مشروع تأجير السيارات بكفاءة وربحية.",
    "forAgencies.features.carManagement.title": "إدارة السيارات الكاملة",
    "forAgencies.features.carManagement.description": "إدارة كاملة لكل سياراتك بواسطة لوحة التحكم المبسطة. أضف وحذف وتعديل السيارات بتفاصيل مفصلة وأسعار وتتبع التوفر.",
    "forAgencies.features.carManagement.benefit1": "إضافة عرض سيارات لا محدود",
    "forAgencies.features.carManagement.benefit2": "تحديث توفر السيارات بشكل حقيقي",
    "forAgencies.features.carManagement.benefit3": "تفاصيل سيارات مفصلة",
    "forAgencies.features.carManagement.benefit4": "إدارة معرض الصور",
    "forAgencies.features.carManagement.benefit5": "مرونة الأسعار",
    "forAgencies.features.bookingSystem.title": "نظام الحجز المتقدم",
    "forAgencies.features.bookingSystem.description": "تسهيل عملية الحجز بواسطة نظام الحجز الذكي. استلم طلبات الحجز الفورية وإدارة التقويم ومعالجة إتصالات العملاء في مكان واحد. لن تفوت أي فرصة حجز أبداً.",
    "forAgencies.features.bookingSystem.benefit1": "إشعارات حجز فورية",
    "forAgencies.features.bookingSystem.benefit2": "دمج التقويم",
    "forAgencies.features.bookingSystem.benefit3": "أدوات الاتصال",
    "forAgencies.features.bookingSystem.benefit4": "تتبع حالة الحجز",
    "forAgencies.features.bookingSystem.benefit5": "تأكيدات تلقائية",
    "forAgencies.features.reviews.title": "ملاحظات العملاء والتقييمات",
    "forAgencies.features.reviews.description": "إنشاء نظام تقييم موثوق به لدينا لبناء الثقة والمصداقية. تتبع تعليقات العملاء والرد عليها وحفظ سمعتك الشركة.",
    "forAgencies.features.reviews.benefit1": "لوحة مراقبة ملاحظات العملاء",
    "forAgencies.features.reviews.benefit2": "أدوات الرد",
    "forAgencies.features.reviews.benefit3": "تحليل التقييم",
    "forAgencies.features.reviews.benefit4": "مراقبة السمعة",
    "forAgencies.features.reviews.benefit5": "تلاحظات العملاء",
    "forAgencies.features.gps.title": "تتبع GPS وإدارة السيارات",
    "forAgencies.features.gps.description": "تتبع مواقع سياراتك بشكل مباشر باستخدام نظام تتبع GPS المتقدم. تتبع مواقع السيارات وتتبع أنماط الاستخدام وتأكد أمان سياراتك.",
    "forAgencies.features.gps.benefit1": "تتبع موقع السيارة بشكل مباشر",
    "forAgencies.features.gps.benefit2": "تحسين المسار",
    "forAgencies.features.gps.benefit3": "تحليل الاستخدام",
    "forAgencies.features.gps.benefit4": "تنبيهات الجيوفينسينغ",
    "forAgencies.features.gps.benefit5": "تلاحظات أداء السيارات",
    "forAgencies.features.coupons.title": "أدوات الكوبون والترويج",
    "forAgencies.features.coupons.description": "تعزيز عملك مع أدوات التسويق القوية. إنشاء كوبونات مخصصة وبرامج ترويجية وبرامج اللتحابة لجذب العملاء والاحتفاظ بهم. زيادة حجوزاتك من خلال حملات التسويق المستهدف.",
    "forAgencies.features.coupons.benefit1": "إنشاء كوبون مخصص",
    "forAgencies.features.coupons.benefit2": "برامج الترويج",
    "forAgencies.features.coupons.benefit3": "برامج اللتحابة",
    "forAgencies.features.coupons.benefit4": "تحليل التسويق",
    "forAgencies.features.coupons.benefit5": "أدوات الاحتفاظ",
    "forAgencies.features.analytics.title": "تحليل متقدم والرؤى",
    "forAgencies.features.analytics.description": "إتخاذ قرارات بيانية بناءً على تحليلنا الشامل. تتبع الإيرادات ومقاييس الأداء وتحليل سلوك العملاء وتحديد فرص النمو. تساعد رؤياتناك في تحسين عملك.",
    "forAgencies.features.analytics.benefit1": "تتبع الإيرادات",
    "forAgencies.features.analytics.benefit2": "مقاييس الأداء",
    "forAgencies.features.analytics.benefit3": "تحليل العملاء",
    "forAgencies.features.analytics.benefit4": "رؤى النمو",
    "forAgencies.features.analytics.benefit5": "الذكاء الأعمالي",
    "forAgencies.cta.title": "هل تريد تحويل عملك؟",
    "forAgencies.cta.subtitle": "انضم إلى مئات من وكالات تأجير السيارات الناجحة في المغرب وابدأ زيادة عملك اليوم.",
    "forAgencies.cta.register": "Register Your Agency",
    "forAgencies.cta.scheduleDemo": "Schedule a Demo",

    // Auth
    "auth.pleaseSignIn": "الرجاء تسجيل الدخول لعرض لوحة التحكم الخاصة بك",
    "auth.signIn": "تسجيل الدخول",
    "auth.accessDenied": "تم رفض الوصول",
    "auth.noPermission": "ليس لديك إذن للوصول إلى هذه الصفحة. هذه المنطقة مقيدة للمستخدمين المصرح لهم فقط.",
    "auth.goBack": "العودة",
    "auth.goToHomepage": "الذهاب إلى الصفحة الرئيسية",

    // Auth Page
    "auth.welcomeBack": "مرحبًا بعودتك",
    "auth.loginSubtitle": "تسجيل الدخول إلى حساب morentcar الخاص بك",
    "auth.email": "البريد الإلكتروني",
    "auth.emailPlaceholder": "<EMAIL>",
    "auth.password": "كلمة المرور",
    "auth.passwordPlaceholder": "••••••••",
    "auth.rememberMe": "تذكرني",
    "auth.forgotPassword": "هل نسيت كلمة المرور؟",
    "auth.logIn": "تسجيل الدخول",
    "auth.loggingIn": "جاري الدخول...",
    "auth.dontHaveAccount": "ليس لديك حساب؟",
    "auth.signUp": "إنشاء حساب",
    "auth.createAccount": "إنشاء حساب",
    "auth.signupSubtitle": "إنشاء حساب للبدء مع MoDrive",
    "auth.firstName": "الاسم الأول",
    "auth.firstNamePlaceholder": "جون",
    "auth.lastName": "الاسم الأخير",
    "auth.lastNamePlaceholder": "دو",
    "auth.joinAs": "أريد أن أنضم ك",
    "auth.traveler": "زبون",
    "auth.rentalAgency": "وكالة الإيجار",
    "auth.agreeToTerms": "أوافق على",
    "auth.termsOfService": "شروط الخدمة",
    "auth.and": "و",
    "auth.privacyPolicy": "سياسة الخصوصية",
    "auth.createAccountButton": "إنشاء حساب",
    "auth.creatingAccount": "جاري إنشاء الحساب...",
    "auth.alreadyHaveAccount": "هل لديك حساب؟",
    "auth.logInLink": "تسجيل الدخول",
    "auth.fillAllFields": "يرجى ملء جميع الحقول المطلوبة وتحميل المستندات والموافقة على الشروط.",
    "auth.agreeToTermsError": "يجب أن توافق على شروط الخدمة",
    "auth.loginSuccess": "تم تسجيل الدخول بنجاح!",
    "auth.invalidCredentials": "بريد إلكتروني أو كلمة مرور غير صحيحة",
    "auth.loginError": "حدث خطأ أثناء تسجيل الدخول",
    "auth.accountCreated": "تم إنشاء الحساب بنجاح!",
    "auth.failedToCreateAccount": "فشل إنشاء الحساب",
    "auth.signupError": "حدث خطأ أثناء إنشاء الحساب",
    "auth.completeRegistration": "إكمال التسجيل",
    "auth.completeRegistrationMessage": "يرجى إكمال تسجيلك بملء معلومات الملف الشخصي.",

    // Common
    "common.loading": "جاري التحميل...",
    "common.price": "السعر",
    "common.mileage": "المسافة المقطوعة",
    "common.insurance": "التأمين",
    "common.deposit": "التأمين",
    "common.edit": "تعديل",
    "common.delete": "حذف",
    "common.days": "أيام",
    "common.day": "يوم",
    "common.viewReceipt": "عرض الإيصال",

    // FAQ Page
    "faq.title": "أسئلة شائعة",
    "faq.subtitle": "إيجاد إجابات على أسئلة شائعة حول خدمات تأجير السيارات والدراجات النارية",
    "faq.contactMessage": "لديك أسئلة؟ نحن هنا لمساعدتك!",
    "faq.contactButton": "اتصل بنا",
    "faq.categories.booking.title": "الحجز والحجوزات",
    "faq.categories.booking.q1.question": "كيف أحجز سيارة؟",
    "faq.categories.booking.q1.answer": "يمكنك إجراء الحجز عبر موقعنا عن طريق اختيار موقعك وتواريخك ونوع السيارة. اتبع عملية الحجز لتأكيد الحجز. يمكنك أيضًا الاتصال بخدمة العملاء للمساعدة.",
    "faq.categories.booking.q2.question": "هل يمكنني تغيير أو إلغاء حجزي؟",
    "faq.categories.booking.q2.answer": "نعم، يمكنك تغيير أو إلغاء حجزك حتى 48 ساعة قبل وقت الاستلام لإسترداد المبلغ بالكامل. يمكن إجراء التغييرات عبر لوحة التحكم في حسابك أو من خلال الاتصال بخدمة العملاء.",
    "faq.categories.booking.q3.question": "ما هو المدة الأدنى للإيجار؟",
    "faq.categories.booking.q3.answer": "المدة الأدنى للإيجار هي 24 ساعة. ومع ذلك، قد تكون هناك مدة مدنية أطول للإيجار بناءً على توفر وموقع السيارة.",
    "faq.categories.requirements.title": "المتطلبات والمستندات",
    "faq.categories.requirements.q1.question": "ما المستندات التي أحتاجها لإيجار سيارة؟",
    "faq.categories.requirements.q1.answer": "ستحتاج إلى رخصة قيادة صالحة، جواز سفر أو بطاقة صرف، وبطاقة صرف لضمان الضمان النقدي. سيحتاج سائقو الدول الخارجية إلى موجه القيادة الدولي (موجه القيادة الدولي) مع رخصتهم المحلية.",
    "faq.categories.requirements.q2.question": "ما هي متطلبات العمر؟",
    "faq.categories.requirements.q2.answer": "يجب أن يكون سن السيارة 21 عامًا على الأقل. قد تكون سائقي الأقل من 25 عامًا مُعرضين لرسوم إضافية وقيود. قد تتطلب بعض السيارات الفاخرة السائقين أن يكونوا أكبر من 25 عامًا.",
    "faq.categories.requirements.q3.question": "هل أحتاج إلى تأمين؟",
    "faq.categories.requirements.q3.answer": "يشمل تأمين الإيجار الأساسي في جميع عقود الإيجار. ومع ذلك، نوصي بشراء تغطية إضافية للحماية الأفضل. يمكنك اختيار من بين خيارات التأمين المتاحة خلال عملية الحجز.",
    "faq.categories.pricing.title": "السعر والدفع",
    "faq.categories.pricing.q1.question": "ما الطرق الدفع التي تقبلها؟",
    "faq.categories.pricing.q1.answer": "نحن نقبل جميع بطاقات الائتمان الرئيسية (فيزا وماستركارد وإكسبريس) وبطاقات الصرف والدفع نقدي. تتطلب الحجوزات المباشرة بطاقة ائتمان للحجز.",
    "faq.categories.pricing.q2.question": "ما الذي يشمل سعر الإيجار؟",
    "faq.categories.pricing.q2.answer": "يشمل سعر الإيجار السيارة، تأمين الأساسي، والميلاجي اللامتناهي. قد تتكبد خدمات إضافية مثل الجي بي آي، مقعد طفل، أو سائق إضافي إضافية.",
    "faq.categories.pricing.q3.question": "كيف يتم حساب الضمان النقدي؟",
    "faq.categories.pricing.q3.answer": "يتراوح مبلغ الضمان النقدي بناءً على نوع السيارة ومدة الإيجار. إنه عادة ما يكون بين 20-30% من تكلفة الإيجار الإجمالية. سيتم إرجاع الضمان النقدي بعد إرجاع السيارة في حالة جيدة.",
    "faq.categories.vehicle.title": "السيارة والخدمات",
    "faq.categories.vehicle.q1.question": "ماذا يحدث إذا تعطلت السيارة؟",
    "faq.categories.vehicle.q1.answer": "نوفر مساعدة طرفية 24/7. إذا تعطلت سيارتك، يرجى الاتصال برقم الطوارئ وسننظر في توفير مساعدة أو سيارة عودة مكانية إذا كان ذلك ضروريًا.",
    "faq.categories.vehicle.q2.question": "هل يمكنني إرسال السيارة إلى بلد آخر؟",
    "faq.categories.vehicle.q2.answer": "يسمح السفر الدولي لبلدان معينة. يرجى التحقق من المتطلبات والقيود المحلية قبل الرحلة باستخدام خدمة العملاء.",
    "faq.categories.vehicle.q3.question": "ما سياسة الوقود؟",
    "faq.categories.vehicle.q3.answer": "سياسة الوقود القياسية لدينا هي 'full-to-full'. ستحصل على السيارة مع خزان ممتلئ ويجب إرجاعها مع خزان ممتلئ. إذا عادتها بمبلغ أقل من الوقود، سيتم خصم مبلغ الوقود المفقود بالإضافة إلى رسوم الخدمة.",

    // Enhanced Auth Messages
    "auth.confirmPassword": "تأكيد كلمة المرور",
    "auth.confirmPasswordPlaceholder": "تأكيد كلمة المرور",
    "auth.passwordsDoNotMatch": "كلمات المرور غير متطابقة",
    "auth.passwordStrength": "يجب أن تكون كلمة المرور 8 أحرف على الأقل",
    "auth.emailInvalid": "يرجى إدخال عنوان بريد إلكتروني صحيح",
    "auth.redirectingToDashboard": "إعادة التوجيه إلى لوحة التحكم...",

    // Password Reset Flow
    "auth.resetPassword": "إعادة تعيين كلمة المرور",
    "auth.resetPasswordDescription": "أدخل عنوان بريدك الإلكتروني وسنرسل لك رمز إعادة التعيين.",
    "auth.enterEmail": "يرجى إدخال عنوان بريدك الإلكتروني",
    "auth.enterResetCode": "أدخل رمز إعادة التعيين",
    "auth.enterResetCodeDescription": "لقد أرسلنا رمز مكون من 6 أرقام إلى بريدك الإلكتروني. أدخله أدناه.",
    "auth.resetCode": "رمز إعادة التعيين",
    "auth.resetCodeSentTo": "تم إرسال رمز إعادة التعيين إلى",
    "auth.invalidResetCode": "رمز إعادة التعيين غير صحيح. يرجى المحاولة مرة أخرى.",
    "auth.createNewPassword": "إنشاء كلمة مرور جديدة",
    "auth.createNewPasswordDescription": "أدخل كلمة المرور الجديدة أدناه.",
    "auth.newPassword": "كلمة المرور الجديدة",
    "auth.newPasswordPlaceholder": "أدخل كلمة المرور الجديدة",
    "auth.confirmNewPassword": "تأكيد كلمة المرور الجديدة",
    "auth.confirmNewPasswordPlaceholder": "تأكيد كلمة المرور الجديدة",
    "auth.passwordResetSuccess": "تم إعادة تعيين كلمة المرور بنجاح!",
    "auth.passwordResetSuccessDescription": "تم إعادة تعيين كلمة المرور بنجاح. يمكنك الآن تسجيل الدخول بكلمة المرور الجديدة.",
    "auth.passwordResetError": "فشل في إعادة تعيين كلمة المرور. يرجى المحاولة مرة أخرى.",
    "auth.sendResetCode": "إرسال رمز إعادة التعيين",
    "auth.verifyCode": "التحقق من الرمز",
    "auth.processing": "جاري المعالجة...",
    "auth.close": "إغلاق",

    // Reservation Success Modal
    "reservation.successModal.title": "تم إرسال طلب الحجز!",
    "reservation.successModal.message": "تم إرسال طلب الحجز إلى الوكالة. سيراجعون طلبك ويعودون إليك خلال 24 ساعة.",
    "reservation.successModal.bookingId": "رقم الحجز",
    "reservation.successModal.continue": "متابعة",
  },
  fr: {
    // Navbar
    "navbar.home": "Accueil",
    "navbar.browseCars": "Parcourir les voitures",
    "navbar.agencies": "Agences",
    "navbar.aboutUs": "À propos de nous",
    "navbar.contactUs": "Contactez-nous",
    "navbar.agencyDashboard": "Tableau de bord de l'agence",
    "navbar.logIn": "Se connecter",
    "navbar.signUp": "S'inscrire",
    "navbar.profile": "Profil",
    "navbar.settings": "Paramètres",
    "navbar.notifications": "Notifications",
    "navbar.bookings": "Réservations",
    "navbar.logout": "Se déconnecter",

    // Home Page
    "home.hero.title": "Découvrez le Maroc sur roues",
    "home.hero.subtitle": "Louez auprès d'agences de confiance au Maroc",
    "home.search.location": "Lieu",
    "home.search.locationPlaceholder": "Où au Maroc ?",
    "home.search.carType": "Type de voiture",
    "home.search.dates": "Dates",
    "home.search.button": "Rechercher",
    "home.featuredCars.title": "Voitures en vedette",
    "home.featuredCars.subtitle": "Découvrez notre sélection de voitures premium des meilleures agences",
    "home.featuredCars.viewAll": "Voir toutes les voitures",
    "home.popularDestinations.title": "Destinations populaires",
    "home.popularDestinations.subtitle": "Explorez les meilleurs endroits de location de voitures au Maroc",
    "home.topAgencies.title": "Agences les mieux notées",
    "home.topAgencies.subtitle": "Louez auprès d'agences de confiance avec d'excellentes évaluations clients",
    "home.becomeHost.title": "Devenez hôte de voiture",
    "home.becomeHost.subtitle":
      "Listez votre voiture sur notre plateforme et commencez à gagner. Obtenez des réservations de voyageurs du Maroc.",
    "home.becomeHost.learnMore": "En savoir plus",
    "home.becomeHost.register": "S'inscrire en tant qu'agence",

    // How It Works
    "howItWorks.title": "Comment ça marche",
    "howItWorks.subtitle": "Étapes simples pour louer une voiture ou lister vos véhicules",
    "howItWorks.forClients": "Pour les clients",
    "howItWorks.forAgencies": "Pour les agences",
    "howItWorks.clients.step1.title": "Rechercher et comparer",
    "howItWorks.clients.step1.description":
      "Recherchez les voitures disponibles en fonction de votre lieu et de vos dates, puis comparez les options.",
    "howItWorks.clients.step2.title": "Réservez votre voiture",
    "howItWorks.clients.step2.description":
      "Sélectionnez votre voiture préférée et complétez le processus de réservation en toute sécurité.",
    "howItWorks.clients.step3.title": "Confirmer et payer",
    "howItWorks.clients.step3.description": "Confirmez les détails de votre réservation et effectuez un paiement sécurisé.",
    "howItWorks.clients.step4.title": "Profitez de votre trajet",
    "howItWorks.clients.step4.description": "Récupérez votre voiture et profitez d'explorer le Maroc en toute liberté.",
    "howItWorks.agencies.step1.title": "Enregistrez votre agence",
    "howItWorks.agencies.step1.description":
      "Créez un compte et complétez le profil de votre agence avec tous les détails nécessaires.",
    "howItWorks.agencies.step2.title": "Listez vos voitures",
    "howItWorks.agencies.step2.description": "Ajoutez vos véhicules avec photos, spécifications et options de tarification.",
    "howItWorks.agencies.step3.title": "Gérez les réservations",
    "howItWorks.agencies.step3.description": "Recevez et gérez les demandes de réservation via votre tableau de bord.",
    "howItWorks.agencies.step4.title": "Développez votre entreprise",
    "howItWorks.agencies.step4.description":
      "Augmentez votre visibilité et élargissez votre clientèle à travers le Maroc.",
    "howItWorks.clients.benefits.title": "Avantages pour les clients",
    "howItWorks.clients.benefits.item1": "Large sélection de véhicules",
    "howItWorks.clients.benefits.item2": "Prix transparents",
    "howItWorks.clients.benefits.item3": "Agences vérifiées",
    "howItWorks.clients.benefits.item4": "Processus de réservation sécurisé",
    "howItWorks.agencies.benefits.title": "Avantages pour les agences",
    "howItWorks.agencies.benefits.item1": "Visibilité accrue",
    "howItWorks.agencies.benefits.item2": "Gestion simplifiée des réservations",
    "howItWorks.agencies.benefits.item3": "Accès à une clientèle plus large",
    "howItWorks.agencies.benefits.item4": "Analyses et insights détaillés",

    // Listings Page
    "listings.title": "Voitures disponibles",
    "listings.carsFound": "{{count}} voitures trouvées",
    "listings.filters.title": "Filtres",
    "listings.filters.clearAll": "Tout effacer",
    "listings.filters.priceRange": "Fourchette de prix",
    "listings.filters.carType": "Type de voiture",
    "listings.filters.features": "Caractéristiques",
    "listings.filters.rating": "Évaluation",
    "listings.search.placeholder": "Rechercher des voitures...",
    "listings.sort.recommended": "Recommandé",
    "listings.sort.priceLow": "Prix: croissant",
    "listings.sort.priceHigh": "Prix: décroissant",
    "listings.sort.ratingHigh": "Mieux notées",
    "listings.noResults.title": "Aucune voiture trouvée",
    "listings.noResults.subtitle": "Essayez d'ajuster vos critères de recherche",
    "listings.loadMore": "Charger plus",

    // Car Types
    "carType.any": "Tout type",
    "carType.sedan": "Berline",
    "carType.suv": "SUV",
    "carType.coupe": "Coupé",
    "carType.convertible": "Cabriolet",
    "carType.luxury": "Luxe",
    "carType.electric": "Électrique",
    "carType.hybrid": "Hybride",
    "carType.compact": "Compacte",
    "carType.minivan": "Monospace",
    "carType.pickup": "Pick-up",
    "carType.sport": "Sport",
    "carType.economy": "Économique",

    // Reservation Page
    "reservation.title": "Confirmer la réservation",
    "reservation.subtitle": "Veuillez compléter les détails de votre réservation",
    "reservation.personalInfo.title": "Informations personnelles",
    "reservation.personalInfo.firstName": "Prénom",
    "reservation.personalInfo.lastName": "Nom",
    "reservation.personalInfo.phone": "Numéro de téléphone",
    "reservation.personalInfo.phonePlaceholder": "+212 XXXXXXXXX",
    "reservation.personalInfo.idOrPassport": "Carte d'identité ou Passeport (Télécharger)",
    "reservation.personalInfo.license": "Permis de conduire (Télécharger)",
    "reservation.personalInfo.uploadHint": "Cliquez pour télécharger une image",
    "reservation.personalInfo.uploadFormat": "PNG, JPG ou PDF (max. 5MB)",
    "reservation.rentalInfo.title": "Informations de location",
    "reservation.rentalInfo.pickup": "Prise en charge",
    "reservation.rentalInfo.dropoff": "Restitution",
    "reservation.rentalInfo.location": "Lieu",
    "reservation.rentalInfo.locationPlaceholder": "Sélectionnez votre ville",
    "reservation.rentalInfo.date": "Date",
    "reservation.rentalInfo.datePlaceholder": "Sélectionnez votre date",
    "reservation.rentalInfo.time": "Heure",
    "reservation.rentalInfo.timePlaceholder": "Sélectionnez votre heure",
    "reservation.terms.agree": "J'accepte le",
    "reservation.terms.contract": "contrat de l'agence",
    "reservation.submit": "Soumettre la demande",
    "reservation.submitting": "Soumission en cours...",
    "reservation.summary.title": "Résumé de la réservation",
    "reservation.summary.days": "jours",
    "reservation.summary.day": "jour",
    "reservation.summary.chauffeur": "Avec chauffeur",
    "reservation.summary.childSeat": "Siège enfant",
    "reservation.summary.paymentMethod": "Méthode de paiement",
    "reservation.summary.serviceFee": "Frais de service",
    "reservation.summary.total": "Total",
    "reservation.delivery.title": "Options de livraison",
    "reservation.delivery.pickup": "Prise en charge à l'agence",
    "reservation.delivery.delivery": "Livraison à l'adresse",
    "reservation.delivery.address": "Adresse de livraison",
    "reservation.delivery.addressPlaceholder": "Entrez l'adresse de livraison",
    "reservation.extras.title": "Services supplémentaires",
    "reservation.extras.chauffeur": "Avec chauffeur",
    "reservation.extras.childSeat": "Siège enfant",
    "reservation.payment.title": "Méthode de paiement",
    "reservation.payment.bankTransfer": "Virement bancaire",
    "reservation.payment.cash": "Paiement en espèces à la réception",
    "reservation.coupon.title": "Code coupon",
    "reservation.coupon.placeholder": "Entrez le code coupon",
    "reservation.coupon.apply": "Appliquer",
    "reservation.coupon.invalid": "Inválido ou no aplicable para esta agencia.",
    "reservation.fillAllFields": "Veuillez remplir tous les champs requis, télécharger les documents et accepter les términos.",
    "reservation.success": "Votre demande de réservation a été envoyée à l'agence avec succès.",

    // Dashboard
    "dashboard.welcome": "Bienvenue",
    "dashboard.activeRentals": "Locations actives",
    "dashboard.upcomingBookings": "Réservations à venir",
    "dashboard.favoriteLocations": "Lieux favoris",
    "dashboard.notifications": "Notifications",
    "dashboard.myBookings": "Mes réservations",
    "dashboard.favorites": "Favoris",
    "dashboard.rentalHistory": "Historique de location",
    "dashboard.viewDetails": "Voir les détails",

    // Agency Dashboard
    "agencyDashboard.totalCars": "Total des voitures",
    "agencyDashboard.activeBookings": "Réservations actives",
    "agencyDashboard.monthlyRevenue": "Revenu mensuel",
    "agencyDashboard.averageRating": "Évaluation moyenne",

    // For Agencies Page
    "forAgencies.hero.badge": "Pour les agences de location",
    "forAgencies.hero.title": "Développez votre entreprise",
    "forAgencies.hero.subtitle": "Rejoignez la plateforme de location de voitures leader du Maroc et accédez aux outils puissants pour gérer votre flotte, augmenter les réservations et augmenter vos revenus.",
    "forAgencies.hero.getStarted": "Commencez aujourd'hui",
    "forAgencies.hero.viewDemo": "Voir démo",
    "forAgencies.stats.activeAgencies": "Agences actives",
    "forAgencies.stats.totalRevenue": "Revenu total",
    "forAgencies.stats.customerSatisfaction": "Satisfaction des clients",
    "forAgencies.stats.averageRating": "Évaluation moyenne",
    "forAgencies.features.title": "Tout ce dont vous avez besoin pour réussir",
    "forAgencies.features.subtitle": "Notre plateforme complète vous offre tous les outils et fonctionnalités dont vous avez besoin pour gérer efficacement votre entreprise de location de voitures et réaliser des bénéfices.",
    "forAgencies.features.carManagement.title": "Gestion des véhicules complète",
    "forAgencies.features.carManagement.description": "Gérez facilement votre flotte entière avec notre tableau de bord intuitif. Ajoutez, modifiez et supprimez des véhicules avec des spécifications détaillées, des prix et un suivi de la disponibilité. Notre système prend en charge tous les types de véhicules, de voitures économiques à luxueuses.",
    "forAgencies.features.carManagement.benefit1": "Listings illimités de véhicules",
    "forAgencies.features.carManagement.benefit2": "Mise à jour de la disponibilité en temps réel",
    "forAgencies.features.carManagement.benefit3": "Spécifications détaillées des véhicules",
    "forAgencies.features.carManagement.benefit4": "Gestion de la galerie photo",
    "forAgencies.features.carManagement.benefit5": "Flexibilité des prix",
    "forAgencies.features.bookingSystem.title": "Système de réservation avancé",
    "forAgencies.features.bookingSystem.description": "Simplifiez votre processus de réservation avec notre système de réservation intelligent. Recevez des demandes de réservation instantanées, gérez les calendriers et gérez les communications avec les clients en un seul endroit. Jamais manquer une opportunité de réservation.",
    "forAgencies.features.bookingSystem.benefit1": "Notifications de réservation instantanées",
    "forAgencies.features.bookingSystem.benefit2": "Intégration du calendrier",
    "forAgencies.features.bookingSystem.benefit3": "Outils de communication",
    "forAgencies.features.bookingSystem.benefit4": "Suivi des statuts de réservation",
    "forAgencies.features.bookingSystem.benefit5": "Confirmations automatisées",
    "forAgencies.features.reviews.title": "Avis des clients et évaluations",
    "forAgencies.features.reviews.description": "Construisez la confiance et la crédibilité avec notre système de révision complet. Surveillez les commentaires des clients, répondez aux évaluations et maintenez la réputation de votre agence. Les bonnes évaluations aident à attirer plus de clients.",
    "forAgencies.features.reviews.benefit1": "Tableau de gestion des avis",
    "forAgencies.features.reviews.benefit2": "Outils de réponse",
    "forAgencies.features.reviews.benefit3": "Analyse des évaluations",
    "forAgencies.features.reviews.benefit4": "Surveillance de la réputation",
    "forAgencies.features.reviews.benefit5": "Insights des commentaires des clients",
    "forAgencies.features.gps.title": "Suivi GPS et gestion de flotte",
    "forAgencies.features.gps.description": "Suivez en temps réel vos véhicules avec notre système d'écoute avancé. Surveillez les emplacements des véhicules, suivez les motifs d'utilisation et assurer la sécurité de votre flotte. Parfait pour les grandes agences avec plusieurs véhicules.",
    "forAgencies.features.gps.benefit1": "Suivi en temps réel des véhicules",
    "forAgencies.features.gps.benefit2": "Optimisation des itinéraires",
    "forAgencies.features.gps.benefit3": "Analyse de l'utilisation",
    "forAgencies.features.gps.benefit4": "Alertes de géofencing",
    "forAgencies.features.gps.benefit5": "Insights sur les performances",
    "forAgencies.features.coupons.title": "Outils de coupon et de promotion",
    "forAgencies.features.coupons.description": "Faites monter votre entreprise avec nos outils de marketing puissants. Créez des coupons personnalisés, des promotions saisonnières et des programmes de fidélité pour attirer et retenir les clients. Augmentez vos réservations grâce à des campagnes de marketing ciblées.",
    "forAgencies.features.coupons.benefit1": "Création de coupons personnalisés",
    "forAgencies.features.coupons.benefit2": "Promotions saisonnières",
    "forAgencies.features.coupons.benefit3": "Programmes de fidélité",
    "forAgencies.features.coupons.benefit4": "Analyse du marketing",
    "forAgencies.features.coupons.benefit5": "Outils de rétention",
    "forAgencies.features.analytics.title": "Analyse avancée et insights",
    "forAgencies.features.analytics.description": "Prenez des décisions basées sur les données avec notre tableau de bord d'analyse complet. Suivez les revenus, surveillez les métriques de performance, analysez le comportement des clients et identifiez les opportunités de croissance. Nos insights vous aident à optimiser votre entreprise.",
    "forAgencies.features.analytics.benefit1": "Suivi des revenus",
    "forAgencies.features.analytics.benefit2": "Métriques de performance",
    "forAgencies.features.analytics.benefit3": "Analyse des clients",
    "forAgencies.features.analytics.benefit4": "Insights de croissance",
    "forAgencies.features.analytics.benefit5": "Inteligence d'affaires",
    "forAgencies.cta.title": "Prêt à transformer votre entreprise ?",
    "forAgencies.cta.subtitle": "Rejoignez des centaines d'agences de location de voitures réussies au Maroc et commencez à grandir votre entreprise aujourd'hui.",
    "forAgencies.cta.register": "Inscrivez votre agence",
    "forAgencies.cta.scheduleDemo": "Planifier une démo",

    // Auth
    "auth.pleaseSignIn": "Veuillez vous connecter pour voir votre tableau de bord",
    "auth.signIn": "Se connecter",
    "auth.accessDenied": "Accès refusé",
    "auth.noPermission":
      "Vous n'avez pas la permission d'accéder à cette page. Cette área est réservée aux utilisateurs autorisés uniquement.",
    "auth.goBack": "Retour",
    "auth.goToHomepage": "Aller à la page d'accueil",

    // Auth Page
    "auth.welcomeBack": "Bienvenue",
    "auth.loginSubtitle": "Se connecter à votre compte MoDrive",
    "auth.email": "Courriel",
    "auth.emailPlaceholder": "<EMAIL>",
    "auth.password": "Mot de passe",
    "auth.passwordPlaceholder": "••••••••",
    "auth.rememberMe": "Se souvenir de moi",
    "auth.forgotPassword": "¿Olvidó su contraseña?",
    "auth.logIn": "Se connecter",
    "auth.loggingIn": "Connexion en cours...",
    "auth.dontHaveAccount": "Vous n'avez pas de compte ?",
    "auth.signUp": "S'inscrire",
    "auth.createAccount": "Créer un compte",
    "auth.signupSubtitle": "S'inscrire pour commencer avec MoDrive",
    "auth.firstName": "Prénom",
    "auth.firstNamePlaceholder": "John",
    "auth.lastName": "Nom",
    "auth.lastNamePlaceholder": "Doe",
    "auth.joinAs": "Je veux me joindre comme",
    "auth.traveler": "Voyageur",
    "auth.rentalAgency": "Agence de location",
    "auth.agreeToTerms": "J'accepte les",
    "auth.termsOfService": "términos de servicio",
    "auth.and": "et",
    "auth.privacyPolicy": "política de privacidad",
    "auth.createAccountButton": "Créer un compte",
    "auth.creatingAccount": "Creando cuenta...",
    "auth.alreadyHaveAccount": "¿Ya tienes cuenta?",
    "auth.logInLink": "Se connecter",
    "auth.fillAllFields": "Veuillez remplir tous les champs requis et télécharger les documents et accepter les términos.",
    "auth.agreeToTermsError": "Vous devez accepter les conditions d'utilisation",
    "auth.loginSuccess": "Connexion réussie !",
    "auth.invalidCredentials": "Courriel ou mot de passe invalide",
    "auth.loginError": "Se produjo un error al iniciar sesión",
    "auth.accountCreated": "¡Cuenta creada con éxito!",
    "auth.failedToCreateAccount": "Échec de la création du compte",
    "auth.signupError": "Erreur lors de la création du compte",
    "auth.completeRegistration": "Compléter l'inscription",
    "auth.completeRegistrationMessage": "Veuillez compléter votre inscription en remplissant les informations du profil.",

    // Common
    "common.loading": "Chargement...",
    "common.price": "Prix",
    "common.mileage": "Kilométrage",
    "common.insurance": "Assurance",
    "common.deposit": "Caution",
    "common.edit": "Modifier",
    "common.delete": "Supprimer",
    "common.days": "días",
    "common.day": "día",
    "common.viewReceipt": "Voir le récépissé",

    // FAQ Page
    "faq.title": "Questions fréquemment posées",
    "faq.subtitle": "Trouvez des réponses aux questions courantes sur nos services de location de voitures et motos",
    "faq.contactMessage": "Vous avez encore des questions ? Nous sommes là pour vous aider !",
    "faq.contactButton": "Contactez-nous",
    "faq.categories.booking.title": "Réservation et réservations",
    "faq.categories.booking.q1.question": "Comment faire une réservation ?",
    "faq.categories.booking.q1.answer": "Vous pouvez faire une réservation via notre site web en sélectionnant votre lieu, dates et type de véhicule souhaités. Suivez le processus de réservation pour confirmer votre réservation. Vous pouvez également appeler notre service client pour obtenir de l'aide.",
    "faq.categories.booking.q2.question": "Puis-je modifier ou annuler ma réservation ?",
    "faq.categories.booking.q2.answer": "Oui, vous pouvez modifier ou annuler votre réservation jusqu'à 48 heures avant l'heure de prise en charge pour un remboursement complet. Les modifications peuvent être apportées via le tableau de bord de votre compte ou en contactant notre service client.",
    "faq.categories.booking.q3.question": "Quelle est la période de location minimale ?",
    "faq.categories.booking.q3.answer": "La période de location minimale est de 24 heures. Cependant, certains véhicules peuvent avoir des périodes de location minimales différentes selon la disponibilité et l'emplacement.",
    "faq.categories.requirements.title": "Exigences et documents",
    "faq.categories.requirements.q1.question": "Quels documents ai-je besoin pour louer un véhicule ?",
    "faq.categories.requirements.q1.answer": "Vous aurez besoin d'un permis de conduire valide, d'un passeport ou d'une carte d'identité, et d'une carte de crédit pour la caution. Les conducteurs internationaux auront besoin d'un permis de conduire international (IDP) avec leur permis national.",
    "faq.categories.requirements.q2.question": "Quelles sont les exigences d'âge ?",
    "faq.categories.requirements.q2.answer": "L'âge minimum pour louer un véhicule est de 21 ans. Les conducteurs de moins de 25 ans peuvent être soumis à des frais et restrictions supplémentaires. Certains véhicules de luxe peuvent exiger que les conducteurs aient 25 ans ou plus.",
    "faq.categories.requirements.q3.question": "Ai-je besoin d'une assurance ?",
    "faq.categories.requirements.q3.answer": "L'assurance de base est incluse dans toutes nos locations. Cependant, nous recommandons d'acheter une couverture supplémentaire pour une meilleure protection. Vous pouvez choisir parmi diverses options d'assurance pendant le processus de réservation.",
    "faq.categories.pricing.title": "Prix et paiements",
    "faq.categories.pricing.q1.question": "Quelles méthodes de paiement acceptez-vous ?",
    "faq.categories.pricing.q1.answer": "Nous acceptons toutes les principales tarjetas de crédito (Visa, Mastercard, American Express), tarjetas de débit et les paiements en espèces. Les réservations en ligne nécessitent une carte de crédit pour la réservation.",
    "faq.categories.pricing.q2.question": "Qu'est-ce qui est inclus dans le prix de location ?",
    "faq.categories.pricing.q2.answer": "Le prix de location comprend le véhicule, l'assurance de base et le kilométrage illimité. Des services supplémentaires comme le GPS, les sièges d'enfant ou les conducteurs supplémentaires peuvent entraîner des frais supplémentaires.",
    "faq.categories.pricing.q3.question": "Comment la caution est-elle calculée ?",
    "faq.categories.pricing.q3.answer": "Le montant de la caution varie selon le type de véhicule et la durée de location. Il est généralement entre 20 et 30% du coût total de location. La caution est remboursée après que le véhicule soit retourné en bon état.",
    "faq.categories.vehicle.title": "Véhicule et services",
    "faq.categories.vehicle.q1.question": "Que se passe-t-il si le véhicule tombe en panne ?",
    "faq.categories.vehicle.q1.answer": "Nous fournissons une assistance routière 24h/24 et 7j/7. Si votre véhicule tombe en panne, appelez notre numéro d'urgence et nous organiserons une assistance ou un véhicule de remplacement si nécessaire.",
    "faq.categories.vehicle.q2.question": "Puis-je conduire le véhicule vers un autre pays ?",
    "faq.categories.vehicle.q2.answer": "Les voyages transfrontaliers sont autorisés vers certains pays. Veuillez vérifier avec notre service client avant votre voyage pour confirmer les exigences et restrictions spécifiques.",
    "faq.categories.vehicle.q3.question": "Quelle est votre politique de carburant ?",
    "faq.categories.vehicle.q3.answer": "Notre politique standard est 'full-to-full'. Vous recevrez le véhicule avec un réservoir plein et devriez le retourner avec un réservoir plein. Si vous le retournez avec moins de carburant, vous serez facturé pour le carburant manquant plus des frais de service.",

    // Enhanced Auth Messages
    "auth.accountNotFound": "Aucun compte trouvé avec cet email",
    "auth.emailAlreadyExists": "Un compte avec cet email existe déjà",
    "auth.passwordMismatch": "Les mots de passe ne correspondent pas",
    "auth.weakPassword": "Le mot de passe doit contenir au moins 8 caractères",
    "auth.invalidEmailFormat": "Veuillez entrer une adresse email valide",
    "auth.networkError": "Erreur réseau. Veuillez réessayer",
    "auth.confirmPassword": "Confirmer le mot de passe",
    "auth.confirmPasswordPlaceholder": "Confirmez votre mot de passe",
    "auth.passwordsDoNotMatch": "Les mots de passe ne correspondent pas",
    "auth.passwordStrength": "Le mot de passe doit contenir au moins 8 caractères",
    "auth.emailInvalid": "Veuillez entrer une adresse email valide",
    "auth.redirectingToDashboard": "Redirection vers le tableau de bord...",

    // Password Reset Flow
    "auth.resetPassword": "Réinitialiser votre mot de passe",
    "auth.resetPasswordDescription": "Entrez votre adresse email et nous vous enverrons un code de réinitialisation.",
    "auth.enterEmail": "Veuillez entrer votre adresse email",
    "auth.enterResetCode": "Entrez le code de réinitialisation",
    "auth.enterResetCodeDescription": "Nous avons envoyé un code à 6 chiffres à votre email. Entrez-le ci-dessous.",
    "auth.resetCode": "Code de réinitialisation",
    "auth.resetCodeSentTo": "Code de réinitialisation envoyé à",
    "auth.invalidResetCode": "Code de réinitialisation invalide. Veuillez réessayer.",
    "auth.createNewPassword": "Créer un nouveau mot de passe",
    "auth.createNewPasswordDescription": "Entrez votre nouveau mot de passe ci-dessous.",
    "auth.newPassword": "Nouveau mot de passe",
    "auth.newPasswordPlaceholder": "Entrez votre nouveau mot de passe",
    "auth.confirmNewPassword": "Confirmer le nouveau mot de passe",
    "auth.confirmNewPasswordPlaceholder": "Confirmez votre nouveau mot de passe",
    "auth.passwordResetSuccess": "Mot de passe réinitialisé avec succès !",
    "auth.passwordResetSuccessDescription": "Votre mot de passe a été réinitialisé avec succès. Vous pouvez maintenant vous connecter avec votre nouveau mot de passe.",
    "auth.passwordResetError": "Échec de la réinitialisation du mot de passe. Veuillez réessayer.",
    "auth.sendResetCode": "Envoyer le code de réinitialisation",
    "auth.verifyCode": "Vérifier le code",
    "auth.processing": "Traitement...",
    "auth.close": "Fermer",

    // Reservation Success Modal
    "reservation.successModal.title": "Demande de réservation envoyée !",
    "reservation.successModal.message": "Votre demande de réservation a été envoyée à l'agence. Ils examineront votre demande et vous répondront dans les 24 heures.",
    "reservation.successModal.bookingId": "ID de réservation",
    "reservation.successModal.continue": "Continuer",
  },
  es: {
    // Navbar
    "navbar.home": "Inicio",
    "navbar.browseCars": "Explorar coches",
    "navbar.agencies": "Agencias",
    "navbar.aboutUs": "Sobre nosotros",
    "navbar.contactUs": "Contáctenos",
    "navbar.agencyDashboard": "Panel de agencia",
    "navbar.logIn": "Iniciar sesión",
    "navbar.signUp": "Registrarse",
    "navbar.profile": "Perfil",
    "navbar.settings": "Configuración",
    "navbar.notifications": "Notificaciones",
    "navbar.bookings": "Reservas",
    "navbar.logout": "Cerrar sesión",

    // Home Page
    "home.hero.title": "Descubre Marruecos sobre ruedas",
    "home.hero.subtitle": "Alquila de agencias confiables en las ciudades de Marruecos",
    "home.search.location": "Ubicación",
    "home.search.locationPlaceholder": "¿Dónde en Marruecos?",
    "home.search.carType": "Tipo de coche",
    "home.search.dates": "Fechas",
    "home.search.button": "Buscar",
    "home.featuredCars.title": "Coches destacados",
    "home.featuredCars.subtitle": "Descubre nuestra selección de coches premium de las mejores agencias",
    "home.featuredCars.viewAll": "Ver todos los coches",
    "home.popularDestinations.title": "Destinos populares",
    "home.popularDestinations.subtitle": "Explora las mejores ubicaciones de alquiler de coches en Marruecos",
    "home.topAgencies.title": "Agencias mejor valoradas",
    "home.topAgencies.subtitle": "Alquila de agencias confiables con excelentes reseñas de clientes",
    "home.becomeHost.title": "Conviértete en anfitrión de coches",
    "home.becomeHost.subtitle":
      "Lista tu coche en nuestra plataforma y comienza a ganar. Obtén reservas de viajeros por todo Marruecos.",
    "home.becomeHost.learnMore": "Saber más",
    "home.becomeHost.register": "Registrarse como agencia",

    // How It Works
    "howItWorks.title": "Cómo funciona",
    "howItWorks.subtitle": "Pasos simples para alquilar un coche o listar tus vehículos",
    "howItWorks.forClients": "Para clientes",
    "howItWorks.forAgencies": "Para agencias",
    "howItWorks.clients.step1.title": "Buscar y comparar",
    "howItWorks.clients.step1.description":
      "Busca coches disponibles según tu ubicación y fechas, luego compara opciones.",
    "howItWorks.clients.step2.title": "Reserva tu coche",
    "howItWorks.clients.step2.description": "Selecciona tu coche preferido y completa el proceso de reserva de forma segura.",
    "howItWorks.clients.step3.title": "Confirmar y pagar",
    "howItWorks.clients.step3.description": "Confirma los detalles de tu reserva y realiza un pago seguro.",
    "howItWorks.clients.step4.title": "Disfruta tu viaje",
    "howItWorks.clients.step4.description": "Recoge tu coche y disfruta explorando Marruecos con libertad.",
    "howItWorks.agencies.step1.title": "Registra tu agencia",
    "howItWorks.agencies.step1.description":
      "Crea una cuenta y completa el perfil de tu agencia con todos los detalles necesarios.",
    "howItWorks.agencies.step2.title": "Lista tus coches",
    "howItWorks.agencies.step2.description": "Añade tus vehículos con fotos, especificaciones y opciones de precios.",
    "howItWorks.agencies.step3.title": "Gestiona reservas",
    "howItWorks.agencies.step3.description": "Recibe y gestiona solicitudes de reserva a través de tu panel.",
    "howItWorks.agencies.step4.title": "Haz crecer tu negocio",
    "howItWorks.agencies.step4.description": "Aumenta tu visibilidad y expande tu base de clientes por todo Marruecos.",
    "howItWorks.clients.benefits.title": "Beneficios para clientes",
    "howItWorks.clients.benefits.item1": "Amplia selección de vehículos",
    "howItWorks.clients.benefits.item2": "Precios transparentes",
    "howItWorks.clients.benefits.item3": "Agencias verificadas",
    "howItWorks.clients.benefits.item4": "Proceso de reserva seguro",
    "howItWorks.agencies.benefits.title": "Beneficios para agencias",
    "howItWorks.agencies.benefits.item1": "Mayor visibilidad",
    "howItWorks.agencies.benefits.item2": "Gestión de reservas simplificada",
    "howItWorks.agencies.benefits.item3": "Acceso a una base de clientes más amplia",
    "howItWorks.agencies.benefits.item4": "Análisis e información detallados",

    // Listings Page
    "listings.title": "Coches disponibles",
    "listings.carsFound": "{{count}} coches encontrados",
    "listings.filters.title": "Filtros",
    "listings.filters.clearAll": "Limpiar todo",
    "listings.filters.priceRange": "Rango de precio",
    "listings.filters.carType": "Tipo de coche",
    "listings.filters.features": "Características",
    "listings.filters.rating": "Valoración",
    "listings.search.placeholder": "Buscar coches...",
    "listings.sort.recommended": "Recomendado",
    "listings.sort.priceLow": "Precio: de menor a mayor",
    "listings.sort.priceHigh": "Precio: de mayor a menor",
    "listings.sort.ratingHigh": "Mejor valorados",
    "listings.noResults.title": "No se encontraron coches",
    "listings.noResults.subtitle": "Intenta ajustar tus criterios de búsqueda",
    "listings.loadMore": "Cargar más",

    // Car Types
    "carType.any": "Cualquier tipo",
    "carType.sedan": "Sedán",
    "carType.suv": "SUV",
    "carType.coupe": "Coupé",
    "carType.convertible": "Convertible",
    "carType.luxury": "Lujo",
    "carType.electric": "Eléctrico",
    "carType.hybrid": "Híbrido",
    "carType.compact": "Compacto",
    "carType.minivan": "Monovolumen",
    "carType.pickup": "Pickup",
    "carType.sport": "Deportivo",
    "carType.economy": "Económico",

    // Reservation Page
    "reservation.title": "Confirmar reserva",
    "reservation.subtitle": "Por favor, completa los detalles de tu reserva",
    "reservation.personalInfo.title": "Información personal",
    "reservation.personalInfo.firstName": "Nombre",
    "reservation.personalInfo.lastName": "Apellido",
    "reservation.personalInfo.phone": "Número de teléfono",
    "reservation.personalInfo.phonePlaceholder": "Enter your phone number",
    "reservation.personalInfo.idOrPassport": "DNI o Pasaporte (Subir)",
    "reservation.personalInfo.license": "Permiso de conducir (Subir)",
    "reservation.personalInfo.uploadHint": "Haz clic para subir una imagen",
    "reservation.personalInfo.uploadFormat": "PNG, JPG o PDF (máx. 5MB)",
    "reservation.rentalInfo.title": "Información de alquiler",
    "reservation.rentalInfo.pickup": "Recogida",
    "reservation.rentalInfo.dropoff": "Devolución",
    "reservation.rentalInfo.location": "Ubicación",
    "reservation.rentalInfo.locationPlaceholder": "Selecciona tu ciudad",
    "reservation.rentalInfo.date": "Fecha",
    "reservation.rentalInfo.datePlaceholder": "Selecciona tu fecha",
    "reservation.rentalInfo.time": "Hora",
    "reservation.rentalInfo.timePlaceholder": "Selecciona tu hora",
    "reservation.terms.agree": "Acepto el",
    "reservation.terms.contract": "contrato de la agencia",
    "reservation.submit": "Enviar solicitud",
    "reservation.submitting": "Enviando...",
    "reservation.summary.title": "Resumen de reserva",
    "reservation.summary.days": "días",
    "reservation.summary.day": "día",
    "reservation.summary.chauffeur": "Con chófer",
    "reservation.summary.childSeat": "Asiento infantil",
    "reservation.summary.paymentMethod": "Método de pago",
    "reservation.summary.serviceFee": "Tarifa de servicio",
    "reservation.summary.total": "Total",
    "reservation.delivery.title": "Opciones de entrega",
    "reservation.delivery.pickup": "Recoger en la agencia",
    "reservation.delivery.delivery": "Entrega a la dirección",
    "reservation.delivery.address": "Dirección de entrega",
    "reservation.delivery.addressPlaceholder": "Enter delivery address",
    "reservation.extras.title": "Servicios adicionales",
    "reservation.extras.chauffeur": "Con chófer",
    "reservation.extras.childSeat": "Asiento infantil",
    "reservation.payment.title": "Método de pago",
    "reservation.payment.bankTransfer": "Transferencia bancaria",
    "reservation.payment.cash": "Pago en efectivo al recoger",
    "reservation.coupon.title": "Código de cupón",
    "reservation.coupon.placeholder": "Enter coupon code",
    "reservation.coupon.apply": "Aplicar",
    "reservation.coupon.invalid": "Inválido o no aplicable para esta agencia.",
    "reservation.fillAllFields": "Por favor, complete todos los campos requeridos, suba los documentos y acepte los términos.",
    "reservation.success": "Su solicitud de reserva se ha enviado a la agencia con éxito.",

    // Dashboard
    "dashboard.welcome": "Bienvenido",
    "dashboard.activeRentals": "Alquileres activos",
    "dashboard.upcomingBookings": "Próximas reservas",
    "dashboard.favoriteLocations": "Ubicaciones favoritas",
    "dashboard.notifications": "Notificaciones",
    "dashboard.myBookings": "Mis reservas",
    "dashboard.favorites": "Favoritos",
    "dashboard.rentalHistory": "Historial de alquiler",
    "dashboard.viewDetails": "Ver detalles",

    // Agency Dashboard
    "agencyDashboard.totalCars": "Total de coches",
    "agencyDashboard.activeBookings": "Reservas activas",
    "agencyDashboard.monthlyRevenue": "Ingresos mensuales",
    "agencyDashboard.averageRating": "Valoración media",

    // For Agencies Page
    "forAgencies.hero.badge": "Para agencias de alquiler",
    "forAgencies.hero.title": "Desarrolla tu negocio",
    "forAgencies.hero.subtitle": "Únete a la plataforma líder de alquiler de coches de Marruecos y accede a herramientas poderosas para gestionar tu flota, aumentar reservas y aumentar tus ingresos.",
    "forAgencies.hero.getStarted": "Comienza hoy",
    "forAgencies.hero.viewDemo": "Ver demo",
    "forAgencies.stats.activeAgencies": "Agencias activas",
    "forAgencies.stats.totalRevenue": "Ingresos totales",
    "forAgencies.stats.customerSatisfaction": "Satisfacción del cliente",
    "forAgencies.stats.averageRating": "Valoración media",
    "forAgencies.features.title": "Todo lo que necesitas para tener éxito",
    "forAgencies.features.subtitle": "Nuestra plataforma completa te ofrece todos los herramientas y características que necesitas para gestionar eficientemente tu negocio de alquiler de coches y obtener beneficios.",
    "forAgencies.features.carManagement.title": "Gestión de vehículos completa",
    "forAgencies.features.carManagement.description": "Gestiona fácilmente tu flota completa con nuestro panel intuitivo. Agrega, edita y elimina vehículos con especificaciones detalladas, precios y seguimiento de disponibilidad. Nuestro sistema admite todos los tipos de vehículos, desde coches económicos hasta vehículos de lujo.",
    "forAgencies.features.carManagement.benefit1": "Listas ilimitadas de vehículos",
    "forAgencies.features.carManagement.benefit2": "Actualizaciones de disponibilidad en tiempo real",
    "forAgencies.features.carManagement.benefit3": "Especificaciones detalladas de vehículos",
    "forAgencies.features.carManagement.benefit4": "Gestión de galería de fotos",
    "forAgencies.features.carManagement.benefit5": "Flexibilidad de precios",
    "forAgencies.features.bookingSystem.title": "Sistema de reserva avanzado",
    "forAgencies.features.bookingSystem.description": "Simplifica tu proceso de reserva con nuestro sistema de reserva inteligente. Recibe solicitudes de reserva instantáneas, gestiona calendarios y maneja las comunicaciones con los clientes en un solo lugar. Nunca te perderás una oportunidad de reserva.",
    "forAgencies.features.bookingSystem.benefit1": "Notificaciones de reserva instantáneas",
    "forAgencies.features.bookingSystem.benefit2": "Integración de calendario",
    "forAgencies.features.bookingSystem.benefit3": "Herramientas de comunicación",
    "forAgencies.features.bookingSystem.benefit4": "Seguimiento de estado de reserva",
    "forAgencies.features.bookingSystem.benefit5": "Confirmaciones automatizadas",
    "forAgencies.features.reviews.title": "Reseñas de clientes y valoraciones",
    "forAgencies.features.reviews.description": "Construye confianza y credibilidad con nuestro sistema de reseñas completo. Monitorea comentarios de clientes, responde a reseñas y mantén la reputación de tu agencia. Las buenas reseñas ayudan a atraer más clientes.",
    "forAgencies.features.reviews.benefit1": "Panel de gestión de reseñas",
    "forAgencies.features.reviews.benefit2": "Herramientas de respuesta",
    "forAgencies.features.reviews.benefit3": "Análisis de valoraciones",
    "forAgencies.features.reviews.benefit4": "Monitoreo de reputación",
    "forAgencies.features.reviews.benefit5": "Insights de comentarios de clientes",
    "forAgencies.features.gps.title": "Seguimiento GPS y gestión de flota",
    "forAgencies.features.gps.description": "Mantén un seguimiento en tiempo real de tus vehículos con nuestro sistema de seguimiento avanzado. Supervisa los lugares de los vehículos, sigue los patrones de uso y asegúrate de la seguridad de tu flota. Perfecto para grandes agencias con varios vehículos.",
    "forAgencies.features.gps.benefit1": "Seguimiento en tiempo real de vehículos",
    "forAgencies.features.gps.benefit2": "Optimización de rutas",
    "forAgencies.features.gps.benefit3": "Análisis de uso",
    "forAgencies.features.gps.benefit4": "Alertas de geofencing",
    "forAgencies.features.gps.benefit5": "Insights sobre rendimiento",
    "forAgencies.features.coupons.title": "Herramientas de cupón y promoción",
    "forAgencies.features.coupons.description": "Incrementa tus ventas con nuestras herramientas de marketing poderosas. Crea cupones personalizados, promociones estacionales y programas de fidelidad para atraer y retener clientes. Aumenta tus reservas con campañas de marketing dirigidas.",
    "forAgencies.features.coupons.benefit1": "Creación de cupones personalizados",
    "forAgencies.features.coupons.benefit2": "Promociones estacionales",
    "forAgencies.features.coupons.benefit3": "Programas de fidelidad",
    "forAgencies.features.coupons.benefit4": "Análisis de marketing",
    "forAgencies.features.coupons.benefit5": "Herramientas de retención",
    "forAgencies.features.analytics.title": "Análisis avanzado e insights",
    "forAgencies.features.analytics.description": "Toma decisiones basadas en datos con nuestro panel de análisis completo. Rastrea ingresos, monitorea métricas de rendimiento, analiza comportamiento de clientes e identifica oportunidades de crecimiento. Nuestros insights te ayudan a optimizar tu negocio.",
    "forAgencies.features.analytics.benefit1": "Seguimiento de ingresos",
    "forAgencies.features.analytics.benefit2": "Métricas de rendimiento",
    "forAgencies.features.analytics.benefit3": "Análisis de clientes",
    "forAgencies.features.analytics.benefit4": "Insights de crecimiento",
    "forAgencies.features.analytics.benefit5": "Inteligencia de negocio",
    "forAgencies.cta.title": "¿Listo para transformar tu negocio?",
    "forAgencies.cta.subtitle": "Únete a cientos de agencias de alquiler de coches exitosas en Marruecos y comienza a hacer crecer tu negocio hoy.",
    "forAgencies.cta.register": "Regístrate como agencia",
    "forAgencies.cta.scheduleDemo": "Programar una demo",

    // Auth
    "auth.pleaseSignIn": "Por favor, inicie sesión para ver su panel",
    "auth.signIn": "Iniciar sesión",
    "auth.accessDenied": "Acceso denegado",
    "auth.noPermission":
      "No tiene permiso para acceder a esta página. Esta área está restringida a usuarios autorizados solamente.",
    "auth.goBack": "Volver",
    "auth.goToHomepage": "Ir a la página de inicio",

    // Auth Page
    "auth.welcomeBack": "Bienvenido de retor",
    "auth.loginSubtitle": "Se conectar a su cuenta MoDrive",
    "auth.email": "Correo electrónico",
    "auth.emailPlaceholder": "<EMAIL>",
    "auth.password": "Contraseña",
    "auth.passwordPlaceholder": "••••••••",
    "auth.rememberMe": "Recordarme",
    "auth.forgotPassword": "¿Olvidó su contraseña?",
    "auth.logIn": "Iniciar sesión",
    "auth.loggingIn": "Conectando...",
    "auth.dontHaveAccount": "¿No tienes cuenta?",
    "auth.signUp": "Registrarse",
    "auth.createAccount": "Crear cuenta",
    "auth.signupSubtitle": "Regístrate para empezar con MoDrive",
    "auth.firstName": "Nombre",
    "auth.firstNamePlaceholder": "John",
    "auth.lastName": "Apellido",
    "auth.lastNamePlaceholder": "Doe",
    "auth.joinAs": "Quiero unirme como",
    "auth.traveler": "Viajero",
    "auth.rentalAgency": "Agencia de alquiler",
    "auth.agreeToTerms": "Acepto los",
    "auth.termsOfService": "términos de servicio",
    "auth.and": "y",
    "auth.privacyPolicy": "política de privacidad",
    "auth.createAccountButton": "Crear cuenta",
    "auth.creatingAccount": "Creando cuenta...",
    "auth.alreadyHaveAccount": "¿Ya tienes cuenta?",
    "auth.logInLink": "Iniciar sesión",
    "auth.fillAllFields": "Por favor, complete todos los campos requeridos y suba los documentos y acepte los términos.",
    "auth.agreeToTermsError": "Debe aceptar los términos de servicio",
    "auth.loginSuccess": "¡Inicio de sesión exitoso!",
    "auth.invalidCredentials": "Correo electrónico o contraseña incorrectos",
    "auth.loginError": "Se produjo un error al iniciar sesión",
    "auth.accountCreated": "¡Cuenta creada con éxito!",
    "auth.failedToCreateAccount": "Error al crear la cuenta",
    "auth.signupError": "Error al crear la cuenta",
    "auth.completeRegistration": "Completar registro",
    "auth.completeRegistrationMessage": "Por favor, complete su registro completando la información del perfil.",

    // Common
    "common.loading": "Cargando...",
    "common.price": "Precio",
    "common.mileage": "Kilometraje",
    "common.insurance": "Seguro",
    "common.deposit": "Depósito",
    "common.edit": "Editar",
    "common.delete": "Eliminar",
    "common.days": "días",
    "common.day": "día",
    "common.viewReceipt": "Ver recibo",

    // FAQ Page
    "faq.title": "Preguntas frecuentes",
    "faq.subtitle": "Encuentre respuestas a preguntas comunes sobre nuestros servicios de alquiler de coches y motos",
    "faq.contactMessage": "¿Todavía tiene preguntas? ¡Estamos aquí para ayudarlo!",
    "faq.contactButton": "Contáctenos",
    "faq.categories.booking.title": "Reserva y reservas",
    "faq.categories.booking.q1.question": "¿Cómo hago una reserva?",
    "faq.categories.booking.q1.answer": "Puedes hacer una reserva a través de nuestro sitio web seleccionando tu ubicación, fechas y tipo de vehículo. Sigue el proceso de reserva para confirmar tu reserva. También puedes llamar a nuestro servicio al cliente para obtener ayuda.",
    "faq.categories.booking.q2.question": "¿Puedo modificar o cancelar mi reserva?",
    "faq.categories.booking.q2.answer": "Sí, puedes modificar o cancelar tu reserva hasta 48 horas antes de la recogida para un reembolso completo. Las modificaciones se pueden hacer a través del panel de tu cuenta o contactando nuestro servicio al cliente.",
    "faq.categories.booking.q3.question": "¿Cuál es el período mínimo de alquiler?",
    "faq.categories.booking.q3.answer": "El período mínimo de alquiler es de 24 horas. Sin embargo, algunos vehículos pueden tener diferentes períodos mínimos de alquiler según la disponibilidad y ubicación.",
    "faq.categories.requirements.title": "Requisitos y documentos",
    "faq.categories.requirements.q1.question": "¿Qué documentos necesito para alquilar un vehículo?",
    "faq.categories.requirements.q1.answer": "Necesitarás una licencia de conducir válida, pasaporte o tarjeta de identidad, y una tarjeta de crédito para el depósito de seguridad. Los conductores internacionales necesitarán un Permiso de Conducir Internacional (IDP) junto con su licencia nacional.",
    "faq.categories.requirements.q2.question": "¿Cuáles son los requisitos de edad?",
    "faq.categories.requirements.q2.answer": "La edad mínima para alquilar un vehículo es de 21 años. Los conductores menores de 25 años pueden estar sujetos a tarifas y restricciones adicionales. Algunos vehículos de lujo pueden requerir que los conductores tengan 25 años o más.",
    "faq.categories.requirements.q3.question": "¿Necesito seguro?",
    "faq.categories.requirements.q3.answer": "El seguro básico está incluido en todos nuestros alquileres. Sin embargo, recomendamos comprar cobertura adicional para mejor protección. Puedes elegir entre varias opciones de seguro durante el proceso de reserva.",
    "faq.categories.pricing.title": "Precios y pagos",
    "faq.categories.pricing.q1.question": "¿Qué métodos de pago aceptan?",
    "faq.categories.pricing.q1.answer": "Aceptamos todas las principales tarjetas de crédito (Visa, Mastercard, American Express), tarjetas de débito y pagos en efectivo. Las reservas en línea requieren una tarjeta de crédito para la reserva.",
    "faq.categories.pricing.q2.question": "¿Qué está incluido en el precio de alquiler?",
    "faq.categories.pricing.q2.answer": "El precio de alquiler incluye el vehículo, seguro básico y kilometraje ilimitado. Servicios adicionales como GPS, sillas de niño o conductores adicionales pueden incurrir en cargos adicionales.",
    "faq.categories.pricing.q3.question": "¿Cómo se calcula el depósito de seguridad?",
    "faq.categories.pricing.q3.answer": "El monto del depósito de seguridad varía según el tipo de vehículo y la duración del alquiler. Típicamente está entre 20-30% del costo total de alquiler. El depósito se reembolsa después de que el vehículo se devuelve en buenas condiciones.",
    "faq.categories.vehicle.title": "Vehículo y servicios",
    "faq.categories.vehicle.q1.question": "¿Qué pasa si el vehículo se avería?",
    "faq.categories.vehicle.q1.answer": "Proporcionamos asistencia en carretera 24/7. Si tu vehículo se avería, llama a nuestro número de emergencia y organizaremos asistencia o un vehículo de remplazo si es necesario.",
    "faq.categories.vehicle.q2.question": "¿Puedo conducir el vehículo a otro país?",
    "faq.categories.vehicle.q2.answer": "Los viajes transfronterizos están permitidos a ciertos países. Por favor verifica con nuestro servicio al cliente antes de tu viaje para confirmar los requisitos y restricciones específicos.",
    "faq.categories.vehicle.q3.question": "Qué política de combustible tienen?",
    "faq.categories.vehicle.q3.answer": "Nuestra política estándar es 'full-to-full'. Vous recevrez le véhicule avec un réservoir plein et devriez le retourner avec un réservoir plein. Si vous le retournez avec moins de carburant, vous serez facturé pour le carburant manquant plus des frais de service.",

    // Enhanced Auth Messages
    "auth.accountNotFound": "No se encontró cuenta con este email",
    "auth.emailAlreadyExists": "Ya existe una cuenta con este email",
    "auth.passwordMismatch": "Las contraseñas no coinciden",
    "auth.weakPassword": "La contraseña debe tener al menos 8 caracteres",
    "auth.invalidEmailFormat": "Por favor ingrese un email válido",
    "auth.networkError": "Error de red. Por favor intente de nuevo",
    "auth.confirmPassword": "Confirmar contraseña",
    "auth.confirmPasswordPlaceholder": "Confirme su contraseña",
    "auth.passwordsDoNotMatch": "Las contraseñas no coinciden",
    "auth.passwordStrength": "La contraseña debe tener al menos 8 caracteres",
    "auth.emailInvalid": "Por favor ingrese un email válido",
    "auth.redirectingToDashboard": "Redirigiendo al panel de control...",

    // Password Reset Flow
    "auth.resetPassword": "Restablecer tu contraseña",
    "auth.resetPasswordDescription": "Ingresa tu dirección de email y te enviaremos un código de restablecimiento.",
    "auth.enterEmail": "Por favor ingresa tu dirección de email",
    "auth.enterResetCode": "Ingresa el código de restablecimiento",
    "auth.enterResetCodeDescription": "Hemos enviado un código de 6 dígitos a tu email. Ingrésalo abajo.",
    "auth.resetCode": "Código de restablecimiento",
    "auth.resetCodeSentTo": "Código de restablecimiento enviado a",
    "auth.invalidResetCode": "Código de restablecimiento inválido. Por favor inténtalo de nuevo.",
    "auth.createNewPassword": "Crear nueva contraseña",
    "auth.createNewPasswordDescription": "Ingresa tu nueva contraseña abajo.",
    "auth.newPassword": "Nueva contraseña",
    "auth.newPasswordPlaceholder": "Ingresa tu nueva contraseña",
    "auth.confirmNewPassword": "Confirmar nueva contraseña",
    "auth.confirmNewPasswordPlaceholder": "Confirma tu nueva contraseña",
    "auth.passwordResetSuccess": "¡Contraseña restablecida exitosamente!",
    "auth.passwordResetSuccessDescription": "Tu contraseña ha sido restablecida exitosamente. Ahora puedes iniciar sesión con tu nueva contraseña.",
    "auth.passwordResetError": "Error al restablecer la contraseña. Por favor inténtalo de nuevo.",
    "auth.sendResetCode": "Enviar código de restablecimiento",
    "auth.verifyCode": "Verificar código",
    "auth.processing": "Procesando...",
    "auth.close": "Cerrar",

    // Reservation Success Modal
    "reservation.successModal.title": "¡Solicitud de reserva enviada!",
    "reservation.successModal.message": "Tu solicitud de reserva ha sido enviada a la agencia. Revisarán tu solicitud y te responderán dentro de 24 horas.",
    "reservation.successModal.bookingId": "ID de reserva",
    "reservation.successModal.continue": "Continuar",
  },
}

// Replace parameters in translated strings like {{paramName}}
function replaceParams(text: string, params?: Record<string, string | number>): string {
  if (!params) return text

  return Object.entries(params).reduce((acc, [key, value]) => {
    return acc.replace(new RegExp(`{{${key}}}`, "g"), String(value))
  }, text)
}

export function I18nProvider({ children }: { children: React.ReactNode }) {
  const [language, setLanguage] = useState<LanguageCode>("en")
  const router = useRouter()

  // Translaton function
  const t = (key: string, params?: Record<string, string | number>): string => {
    const langTranslations = translations[language] || translations.en
    const translation = langTranslations[key as keyof typeof langTranslations]

    if (!translation) {
      // If translation not found, return the key
      console.warn(`Translation missing for key: ${key}`)
      return key
    }

    return replaceParams(translation, params)
  }

  // Change language function
  const changeLanguage = (lang: LanguageCode) => {
    setLanguage(lang)
    localStorage.setItem("preferredLanguage", lang)

    // Reload page to apply RTL if needed
    if ((lang === "ar" && document.dir !== "rtl") || (lang !== "ar" && document.dir === "rtl")) {
      window.location.reload()
    }
  }

  // Set initial language from localStorage on mount
  useEffect(() => {
    const storedLang = localStorage.getItem("preferredLanguage") as LanguageCode
    if (storedLang && languages[storedLang]) {
      setLanguage(storedLang)
    }
  }, [])

  // Set document direction based on language
  useEffect(() => {
    document.documentElement.lang = language
    document.dir = language === "ar" ? "rtl" : "ltr"

    // Add RTL class to body for easier styling
    if (language === "ar") {
      document.body.classList.add("rtl")
    } else {
      document.body.classList.remove("rtl")
    }
  }, [language])

  const dir = language === "ar" ? "rtl" : "ltr"

  return <I18nContext.Provider value={{ language, t, changeLanguage, dir }}>{children}</I18nContext.Provider>
}

export function useI18n() {
  const context = useContext(I18nContext)
  if (!context) {
    throw new Error("useI18n must be used within an I18nProvider")
  }
  return context
}
