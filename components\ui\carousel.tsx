import React from "react"
import { use<PERSON><PERSON><PERSON>lide<PERSON>, KeenSliderOptions } from "keen-slider/react"
import "keen-slider/keen-slider.min.css"

interface CarouselProps {
    children: React.ReactNode
    slidesPerView?: number
    breakpoints?: KeenSliderOptions["breakpoints"]
    className?: string
}

const Carousel: React.FC<CarouselProps> = ({
    children,
    slidesPerView = 1,
    breakpoints = {
        "(min-width: 640px)": { slides: { perView: 2, spacing: 16 } },
        "(min-width: 1024px)": { slides: { perView: 3, spacing: 16 } },
        "(min-width: 1280px)": { slides: { perView: 4, spacing: 16 } },
    },
    className = "",
}) => {
    const [sliderRef, instanceRef] = useKeenSlider<HTMLDivElement>({
        slides: { perView: slidesPerView, spacing: 16 },
        breakpoints,
        loop: true,
    })

    return (
        <div className={`relative ${className}`}>
            <div ref={sliderRef} className="keen-slider">
                {React.Children.map(children, (child) => (
                    <div className="keen-slider__slide flex justify-center">{child}</div>
                ))}
            </div>
            <button
                type="button"
                aria-label="Previous"
                className="absolute left-2 top-1/2 -translate-y-1/2 z-10 bg-white/80 hover:bg-white shadow rounded-full p-2"
                onClick={() => instanceRef.current?.prev()}
            >
                <svg width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path d="M15 19l-7-7 7-7" /></svg>
            </button>
            <button
                type="button"
                aria-label="Next"
                className="absolute right-2 top-1/2 -translate-y-1/2 z-10 bg-white/80 hover:bg-white shadow rounded-full p-2"
                onClick={() => instanceRef.current?.next()}
            >
                <svg width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path d="M9 5l7 7-7 7" /></svg>
            </button>
        </div>
    )
}

export default Carousel; 