import { Badge } from "@/components/ui/badge"
import { CheckCircle } from "lucide-react"
import { cn } from "@/lib/utils"

interface VerificationBadgeProps {
  isVerified: boolean
  size?: "sm" | "md" | "lg"
  variant?: "default" | "outline" | "secondary"
  className?: string
}

export function VerificationBadge({ 
  isVerified, 
  size = "md", 
  variant = "secondary",
  className 
}: VerificationBadgeProps) {
  if (!isVerified) return null

  const sizeClasses = {
    sm: "text-xs px-2 py-0.5",
    md: "text-sm px-2.5 py-0.5", 
    lg: "text-base px-3 py-1"
  }

  const iconSizes = {
    sm: "h-3 w-3",
    md: "h-4 w-4",
    lg: "h-5 w-5"
  }

  return (
    <Badge 
      variant={variant} 
      className={cn(
        "inline-flex items-center gap-1 bg-green-100 text-green-800 border-green-200 hover:bg-green-200",
        sizeClasses[size],
        className
      )}
    >
      <CheckCircle className={cn("fill-current", iconSizes[size])} />
      <span className="font-medium">Verified</span>
    </Badge>
  )
}
