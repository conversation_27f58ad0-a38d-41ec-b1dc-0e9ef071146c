// Application configuration
export const APP_CONFIG = {
  // App Information
  name: 'KriwDrive',
  version: '1.0.0',
  description: 'Car rental platform in Morocco',
  
  // Contact Information
  contact: {
    email: '<EMAIL>',
    adminEmail: '<EMAIL>',
    phone: '+*********** 456',
    address: '123, Rue Mohammed V, Marrakech, Morocco',
  },
  
  // Social Media
  social: {
    facebook: 'https://facebook.com/kriwdrive',
    instagram: 'https://instagram.com/kriwdrive',
    twitter: 'https://twitter.com/kriwdrive',
    linkedin: 'https://linkedin.com/company/kriwdrive',
  },
  
  // Features
  features: {
    enableBlogManagement: true,
    enablePaymentRequests: true,
    enableGPSTracking: true,
    enableEmailVerification: true,
    enableAgencyVerification: true,
  },
  
  // File Upload Settings
  upload: {
    maxFileSize: 5242880, // 5MB in bytes
    allowedImageTypes: ['image/jpeg', 'image/png', 'image/webp'],
    allowedDocumentTypes: ['application/pdf', 'image/jpeg', 'image/png'],
  },
  
  // Admin Settings
  admin: {
    maxLoginAttempts: 5,
    lockoutDuration: 15 * 60 * 1000, // 15 minutes in milliseconds
    secretKey: process.env.ADMIN_SECRET_KEY || 'admin-secret-123',
  },
} as const;

// Environment-specific configurations
export const ENV_CONFIG = {
  development: {
    apiUrl: 'http://localhost:3000/api',
    appUrl: 'http://localhost:3000',
  },
  production: {
    apiUrl: process.env.NEXT_PUBLIC_API_URL || 'https://kriwdrive.com/api',
    appUrl: process.env.NEXT_PUBLIC_APP_URL || 'https://kriwdrive.com',
  },
} as const;

// Get current environment configuration
export const getCurrentConfig = () => {
  const env = process.env.NODE_ENV || 'development';
  return ENV_CONFIG[env as keyof typeof ENV_CONFIG] || ENV_CONFIG.development;
};
