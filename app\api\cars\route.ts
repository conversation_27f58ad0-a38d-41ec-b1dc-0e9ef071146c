import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { NextResponse } from 'next/server'

export async function GET(request: Request) {
    const supabase = createRouteHandlerClient({ cookies })

    try {
        const { data, error } = await supabase
            .from('cars')
            .select('*, agencies(*)')

        if (error) throw error

        return NextResponse.json({ data, success: true })
    } catch (error) {
        return NextResponse.json(
            { error: 'Failed to fetch cars', success: false },
            { status: 500 }
        )
    }
}

export async function POST(request: Request) {
    const supabase = createRouteHandlerClient({ cookies })

    try {
        const body = await request.json()

        const { data, error } = await supabase
            .from('cars')
            .insert(body)
            .select()
            .single()

        if (error) throw error

        return NextResponse.json({ data, success: true })
    } catch (error) {
        return NextResponse.json(
            { error: 'Failed to create car', success: false },
            { status: 500 }
        )
    }
} 