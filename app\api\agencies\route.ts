import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { NextResponse } from 'next/server'

export async function GET(request: Request) {
    const supabase = createRouteHandlerClient({ cookies })

    try {
        const { data, error } = await supabase
            .from('agencies')
            .select('*')

        if (error) throw error

        return NextResponse.json({ data, success: true })
    } catch (error) {
        return NextResponse.json(
            { error: 'Failed to fetch agencies', success: false },
            { status: 500 }
        )
    }
}

export async function POST(request: Request) {
    const supabase = createRouteHandlerClient({ cookies })

    try {
        const { data: { user } } = await supabase.auth.getUser()

        if (!user) {
            return NextResponse.json(
                { error: 'Unauthorized', success: false },
                { status: 401 }
            )
        }

        const body = await request.json()

        const { data, error } = await supabase
            .from('agencies')
            .insert(body)
            .select()
            .single()

        if (error) throw error

        return NextResponse.json({ data, success: true })
    } catch (error) {
        return NextResponse.json(
            { error: 'Failed to create agency', success: false },
            { status: 500 }
        )
    }
} 