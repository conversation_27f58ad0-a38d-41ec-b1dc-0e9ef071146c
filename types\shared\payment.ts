// Shared payment types
export interface Payment {
    id: string;
    bookingId: string;
    amount: number;
    currency: string;
    status: PaymentStatus;
    method: PaymentMethod;
    transactionId?: string;
    gatewayResponse?: any;
    createdAt: string;
    updatedAt: string;
}

export type PaymentStatus =
    | 'pending'
    | 'processing'
    | 'completed'
    | 'failed'
    | 'cancelled'
    | 'refunded'
    | 'partially_refunded';

export type PaymentMethod =
    | 'credit_card'
    | 'debit_card'
    | 'bank_transfer'
    | 'paypal'
    | 'stripe'
    | 'cash'
    | 'mobile_payment';

export interface PaymentCreateData {
    bookingId: string;
    amount: number;
    currency: string;
    method: PaymentMethod;
    paymentDetails?: any;
}

export interface PaymentUpdateData {
    status?: PaymentStatus;
    transactionId?: string;
    gatewayResponse?: any;
}

export interface PaymentRequest {
    id: string;
    agencyId: string;
    amount: number;
    currency: string;
    description: string;
    status: PaymentRequestStatus;
    createdAt: string;
    updatedAt: string;
}

export type PaymentRequestStatus =
    | 'pending'
    | 'approved'
    | 'rejected'
    | 'paid'; 