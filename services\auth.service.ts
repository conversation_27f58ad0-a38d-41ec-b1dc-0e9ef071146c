// Authentication service
import apiClient from '../lib/api/client';
import { API_ENDPOINTS } from '../lib/constants/app';
import type {
    LoginCredentials,
    RegisterCredentials,
    AuthResponse,
    User,
    PasswordResetRequest,
    PasswordResetConfirm,
    EmailVerification,
    UpdateProfileData,
    ChangePasswordData,
} from '../types/shared/auth';

export class AuthService {
    // Login user
    async login(credentials: LoginCredentials): Promise<AuthResponse> {
        const response = await apiClient.post<AuthResponse>(
            API_ENDPOINTS.auth.login,
            credentials
        );
        return response.data;
    }

    // Register user
    async register(credentials: RegisterCredentials): Promise<AuthResponse> {
        const response = await apiClient.post<AuthResponse>(
            API_ENDPOINTS.auth.register,
            credentials
        );
        return response.data;
    }

    // Logout user
    async logout(): Promise<void> {
        await apiClient.post(API_ENDPOINTS.auth.logout);
        // Clear local storage
        if (typeof window !== 'undefined') {
            localStorage.removeItem('auth_token');
            localStorage.removeItem('refresh_token');
            localStorage.removeItem('user');
        }
    }

    // Verify email
    async verifyEmail(token: string): Promise<{ message: string }> {
        const response = await apiClient.post<{ message: string }>(
            API_ENDPOINTS.auth.verify,
            { token }
        );
        return response.data;
    }

    // Request password reset
    async requestPasswordReset(data: PasswordResetRequest): Promise<{ message: string }> {
        const response = await apiClient.post<{ message: string }>(
            '/api/auth/forgot-password',
            data
        );
        return response.data;
    }

    // Confirm password reset
    async confirmPasswordReset(data: PasswordResetConfirm): Promise<{ message: string }> {
        const response = await apiClient.post<{ message: string }>(
            '/api/auth/reset-password',
            data
        );
        return response.data;
    }

    // Get current user profile
    async getProfile(): Promise<User> {
        const response = await apiClient.get<User>(API_ENDPOINTS.users.profile);
        return response.data;
    }

    // Update user profile
    async updateProfile(data: UpdateProfileData): Promise<User> {
        const response = await apiClient.put<User>(API_ENDPOINTS.users.update, data);
        return response.data;
    }

    // Change password
    async changePassword(data: ChangePasswordData): Promise<{ message: string }> {
        const response = await apiClient.post<{ message: string }>(
            '/api/auth/change-password',
            data
        );
        return response.data;
    }

    // Refresh token
    async refreshToken(): Promise<AuthResponse> {
        const refreshToken = localStorage.getItem('refresh_token');
        if (!refreshToken) {
            throw new Error('No refresh token available');
        }

        const response = await apiClient.post<AuthResponse>(
            '/api/auth/refresh',
            { refreshToken }
        );
        return response.data;
    }

    // Check if user is authenticated
    isAuthenticated(): boolean {
        if (typeof window === 'undefined') return false;
        const token = localStorage.getItem('auth_token');
        return !!token;
    }

    // Get stored user data
    getStoredUser(): User | null {
        if (typeof window === 'undefined') return null;
        const userStr = localStorage.getItem('user');
        return userStr ? JSON.parse(userStr) : null;
    }

    // Store user data
    storeUserData(user: User, token: string, refreshToken: string): void {
        if (typeof window === 'undefined') return;
        localStorage.setItem('user', JSON.stringify(user));
        localStorage.setItem('auth_token', token);
        localStorage.setItem('refresh_token', refreshToken);
    }
}

// Export singleton instance
export const authService = new AuthService();
export default authService; 