"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { Search, MoreVertical, CheckCircle, XCircle, AlertTriangle, Eye, Loader2 } from "lucide-react"
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { toast } from "sonner"

export default function AgenciesPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [viewAgencyDialog, setViewAgencyDialog] = useState(false)
  const [selectedAgency, setSelectedAgency] = useState<any>(null)
  const [agencies, setAgencies] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const supabase = createClientComponentClient()

  // Load agencies from database
  useEffect(() => {
    loadAgencies()
  }, [])

  const loadAgencies = async () => {
    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('agencies')
        .select(`
          *,
          users (
            first_name,
            last_name,
            email,
            phone
          )
        `)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error loading agencies:', error)
        toast.error('Failed to load agencies')
        return
      }

      // Transform data to match the expected format
      const transformedAgencies = data.map(agency => ({
        id: agency.id,
        name: agency.agency_name || 'Unnamed Agency',
        email: agency.agency_email || agency.users?.email || 'No email',
        status: agency.is_approved ? 'verified' : 'pending',
        cars: 0, // We'll need to count cars separately
        bookings: 0, // We'll need to count bookings separately
        joinDate: new Date(agency.created_at).toLocaleDateString(),
        address: agency.agency_address || 'No address',
        phone: agency.agency_phone || agency.users?.phone || 'No phone',
        owner: `${agency.users?.first_name || ''} ${agency.users?.last_name || ''}`.trim() || 'Unknown',
        logo: agency.agency_logo,
        description: agency.agency_description,
      }))

      setAgencies(transformedAgencies)
    } catch (error) {
      console.error('Error loading agencies:', error)
      toast.error('Failed to load agencies')
    } finally {
      setLoading(false)
    }
  }

  const handleApproveAgency = async (agencyId: string) => {
    try {
      const { error } = await supabase
        .from('agencies')
        .update({ is_approved: true })
        .eq('id', agencyId)

      if (error) {
        toast.error('Failed to approve agency: ' + error.message)
        return
      }

      toast.success('Agency approved successfully!')
      loadAgencies() // Reload the list
    } catch (error) {
      console.error('Error approving agency:', error)
      toast.error('Failed to approve agency')
    }
  }

  const handleRejectAgency = async (agencyId: string) => {
    try {
      const { error } = await supabase
        .from('agencies')
        .update({ is_approved: false })
        .eq('id', agencyId)

      if (error) {
        toast.error('Failed to reject agency: ' + error.message)
        return
      }

      toast.success('Agency rejected successfully!')
      loadAgencies() // Reload the list
    } catch (error) {
      console.error('Error rejecting agency:', error)
      toast.error('Failed to reject agency')
    }
  }

  const filteredAgencies = agencies.filter(
    (agency) =>
      agency.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      agency.email.toLowerCase().includes(searchQuery.toLowerCase()),
  )

  const handleViewAgency = (agency: any) => {
    setSelectedAgency(agency)
    setViewAgencyDialog(true)
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "verified":
        return <Badge className="bg-green-500">Verified</Badge>
      case "pending":
        return <Badge className="bg-amber-500">Pending</Badge>
      case "rejected":
        return <Badge className="bg-red-500">Rejected</Badge>
      default:
        return <Badge>Unknown</Badge>
    }
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">Agencies Management</h1>
          <p className="text-muted-foreground">Manage and verify rental agencies</p>
        </div>
      </div>

      <Card className="mb-8">
        <CardHeader>
          <CardTitle>All Agencies</CardTitle>
          <CardDescription>View, verify, and manage all rental agencies</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex justify-between mb-4">
            <div className="relative w-64">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search agencies..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <div className="flex gap-2">
              <Button variant="outline" size="sm">
                Export
              </Button>
              <Button size="sm">Add Agency</Button>
            </div>
          </div>

          <div className="rounded-md border">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b bg-muted/50">
                  <th className="py-3 px-4 text-left font-medium">Agency Name</th>
                  <th className="py-3 px-4 text-left font-medium">Email</th>
                  <th className="py-3 px-4 text-left font-medium">Status</th>
                  <th className="py-3 px-4 text-left font-medium">Cars</th>
                  <th className="py-3 px-4 text-left font-medium">Bookings</th>
                  <th className="py-3 px-4 text-left font-medium">Join Date</th>
                  <th className="py-3 px-4 text-left font-medium">Actions</th>
                </tr>
              </thead>
              <tbody>
                {loading ? (
                  <tr>
                    <td colSpan={7} className="py-8 text-center">
                      <Loader2 className="h-6 w-6 animate-spin mx-auto mb-2" />
                      <p>Loading agencies...</p>
                    </td>
                  </tr>
                ) : filteredAgencies.length === 0 ? (
                  <tr>
                    <td colSpan={7} className="py-8 text-center text-gray-500">
                      No agencies found
                    </td>
                  </tr>
                ) : filteredAgencies.map((agency) => (
                  <tr key={agency.id} className="border-b">
                    <td className="py-3 px-4 font-medium">{agency.name}</td>
                    <td className="py-3 px-4">{agency.email}</td>
                    <td className="py-3 px-4">{getStatusBadge(agency.status)}</td>
                    <td className="py-3 px-4">{agency.cars}</td>
                    <td className="py-3 px-4">{agency.bookings}</td>
                    <td className="py-3 px-4">{agency.joinDate}</td>
                    <td className="py-3 px-4">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleViewAgency(agency)}>
                            <Eye className="h-4 w-4 mr-2" />
                            View Details
                          </DropdownMenuItem>
                          {agency.status === "pending" && (
                            <>
                              <DropdownMenuItem onClick={() => handleApproveAgency(agency.id)}>
                                <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
                                Verify Agency
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleRejectAgency(agency.id)}>
                                <XCircle className="h-4 w-4 mr-2 text-red-500" />
                                Reject Agency
                              </DropdownMenuItem>
                            </>
                          )}
                          {agency.status === "verified" && (
                            <DropdownMenuItem>
                              <AlertTriangle className="h-4 w-4 mr-2 text-amber-500" />
                              Suspend Agency
                            </DropdownMenuItem>
                          )}
                          {agency.status === "rejected" && (
                            <DropdownMenuItem>
                              <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
                              Reconsider Agency
                            </DropdownMenuItem>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Agency Details Dialog */}
      {selectedAgency && (
        <Dialog open={viewAgencyDialog} onOpenChange={setViewAgencyDialog}>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>Agency Details</DialogTitle>
              <DialogDescription>Detailed information about {selectedAgency.name}</DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <p className="text-sm font-medium col-span-1">Status:</p>
                <div className="col-span-3">{getStatusBadge(selectedAgency.status)}</div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <p className="text-sm font-medium col-span-1">Name:</p>
                <p className="col-span-3">{selectedAgency.name}</p>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <p className="text-sm font-medium col-span-1">Email:</p>
                <p className="col-span-3">{selectedAgency.email}</p>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <p className="text-sm font-medium col-span-1">Owner:</p>
                <p className="col-span-3">{selectedAgency.owner}</p>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <p className="text-sm font-medium col-span-1">Phone:</p>
                <p className="col-span-3">{selectedAgency.phone}</p>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <p className="text-sm font-medium col-span-1">Address:</p>
                <p className="col-span-3">{selectedAgency.address}</p>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <p className="text-sm font-medium col-span-1">Joined:</p>
                <p className="col-span-3">{selectedAgency.joinDate}</p>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <p className="text-sm font-medium col-span-1">Cars:</p>
                <p className="col-span-3">{selectedAgency.cars}</p>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <p className="text-sm font-medium col-span-1">Bookings:</p>
                <p className="col-span-3">{selectedAgency.bookings}</p>
              </div>
            </div>
            <DialogFooter>
              {selectedAgency.status === "pending" && (
                <>
                  <Button
                    variant="destructive"
                    onClick={() => {
                      handleRejectAgency(selectedAgency.id)
                      setViewAgencyDialog(false)
                    }}
                  >
                    <XCircle className="h-4 w-4 mr-2" />
                    Reject
                  </Button>
                  <Button
                    onClick={() => {
                      handleApproveAgency(selectedAgency.id)
                      setViewAgencyDialog(false)
                    }}
                  >
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Verify
                  </Button>
                </>
              )}
              {selectedAgency.status === "verified" && (
                <Button variant="destructive">
                  <AlertTriangle className="h-4 w-4 mr-2" />
                  Suspend Agency
                </Button>
              )}
              {selectedAgency.status === "rejected" && (
                <Button>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Reconsider Agency
                </Button>
              )}
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  )
}
