// Test script to check admin access issues
require('dotenv').config({ path: '.env.local' })

const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://sagtwjbwgfgvzulnsmhc.supabase.co'
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseKey) {
    console.error('❌ NEXT_PUBLIC_SUPABASE_ANON_KEY not found')
    process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseKey)

async function testAdminAccess() {
    console.log('🔍 Testing admin access issues...\n')

    try {
        // Step 1: Sign in as admin
        console.log('📝 Step 1: Signing in as admin...')
        const { data, error } = await supabase.auth.signInWithPassword({
            email: '<EMAIL>',
            password: 'Admin123!'
        })

        if (error) {
            console.error('❌ Admin sign in failed:', error.message)
            return false
        }

        console.log('✅ Step 1: Admin sign in successful!')
        console.log('   User ID:', data.user?.id)
        console.log('   User Email:', data.user?.email)

        // Step 2: Get user profile
        console.log('\n📝 Step 2: Getting user profile...')
        const { data: profile, error: profileError } = await supabase
            .from('users')
            .select('*')
            .eq('id', data.user.id)
            .single()

        if (profileError) {
            console.error('❌ Step 2: Profile fetch failed:', profileError.message)
            return false
        }

        console.log('✅ Step 2: Profile fetched successfully!')
        console.log('   Profile Role:', profile.role)
        console.log('   Profile Email:', profile.email)
        console.log('   Is Verified:', profile.is_verified)

        // Step 3: Check if role is actually admin
        if (profile.role !== 'admin') {
            console.error('❌ Step 3: User role is not admin!')
            console.log('   Expected: admin')
            console.log('   Got:', profile.role)
            return false
        }

        console.log('✅ Step 3: User role is admin')

        // Step 4: Test session verification
        console.log('\n📝 Step 4: Testing session verification...')
        const { data: { session } } = await supabase.auth.getSession()

        if (!session) {
            console.error('❌ Step 4: No session found')
            return false
        }

        console.log('✅ Step 4: Session exists')
        console.log('   Session User ID:', session.user.id)
        console.log('   Session User Email:', session.user.email)

        // Step 5: Simulate middleware check
        console.log('\n📝 Step 5: Simulating middleware check...')
        const { data: middlewareUser } = await supabase
            .from('users')
            .select('role')
            .eq('id', session.user.id)
            .single()

        if (middlewareUser?.role === 'admin') {
            console.log('✅ Step 5: Middleware would allow admin access')
        } else {
            console.error('❌ Step 5: Middleware would deny admin access')
            console.log('   User role:', middlewareUser?.role)
            return false
        }

        // Step 6: Test admin layout check
        console.log('\n📝 Step 6: Simulating admin layout check...')
        const { data: layoutUser } = await supabase
            .from('users')
            .select('*')
            .eq('id', session.user.id)
            .single()

        if (layoutUser?.role === 'admin') {
            console.log('✅ Step 6: Admin layout would allow access')
        } else {
            console.error('❌ Step 6: Admin layout would deny access')
            console.log('   User role:', layoutUser?.role)
            return false
        }

        // Sign out
        await supabase.auth.signOut()
        console.log('\n✅ Signed out successfully')

        return true
    } catch (err) {
        console.error('❌ Test error:', err.message)
        return false
    }
}

async function checkUserRoles() {
    console.log('\n🔍 Checking all user roles...\n')

    try {
        const { data: users, error } = await supabase
            .from('users')
            .select('email, role, is_verified')
            .order('created_at', { ascending: false })
            .limit(10)

        if (error) {
            console.error('❌ Failed to fetch users:', error.message)
            return false
        }

        console.log('📊 Recent users:')
        users.forEach((user, index) => {
            console.log(`${index + 1}. ${user.email} - Role: ${user.role} - Verified: ${user.is_verified}`)
        })

        // Check admin user specifically
        const adminUser = users.find(u => u.email === '<EMAIL>')
        if (adminUser) {
            console.log(`\n👑 Admin user details:`)
            console.log(`   Email: ${adminUser.email}`)
            console.log(`   Role: ${adminUser.role}`)
            console.log(`   Verified: ${adminUser.is_verified}`)
        } else {
            console.log('\n❌ Admin user not found in recent users')
        }

        return true
    } catch (err) {
        console.error('❌ Error checking user roles:', err.message)
        return false
    }
}

async function main() {
    console.log('🚀 Starting admin access test...\n')

    const accessSuccess = await testAdminAccess()
    if (!accessSuccess) {
        console.log('\n❌ Admin access test failed.')
        process.exit(1)
    }

    await checkUserRoles()

    console.log('\n✅ Admin access test completed!')
    console.log('\n💡 If the frontend still can\'t access dashboards:')
    console.log('   1. The issue might be with cookie timing')
    console.log('   2. The middleware might be blocking access')
    console.log('   3. The admin layout might be failing authentication check')
    console.log('   4. There might be a React state issue')
}

main().catch(console.error) 