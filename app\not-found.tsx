'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Car, Home } from 'lucide-react'

export default function NotFound() {
  const router = useRouter()

  useEffect(() => {
    // Auto-redirect to home after 5 seconds
    const timer = setTimeout(() => {
      router.push('/')
    }, 5000)

    return () => clearTimeout(timer)
  }, [router])

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="text-center p-8 max-w-md mx-auto">
        <div className="mb-8">
          <Car className="h-24 w-24 mx-auto text-primary mb-4" />
          <h1 className="text-6xl font-bold text-gray-900 mb-4">404</h1>
          <h2 className="text-2xl font-semibold text-gray-700 mb-2">Page Not Found</h2>
          <p className="text-gray-600 mb-6">
            The page you're looking for doesn't exist. You'll be redirected to the home page in a few seconds.
          </p>
        </div>
        
        <div className="space-y-4">
          <Button 
            onClick={() => router.push('/')}
            className="w-full"
            size="lg"
          >
            <Home className="mr-2 h-5 w-5" />
            Go to Home Page
          </Button>
          
          <Button 
            onClick={() => router.back()}
            variant="outline"
            className="w-full"
            size="lg"
          >
            Go Back
          </Button>
        </div>
        
        <div className="mt-8 text-sm text-gray-500">
          <p>Redirecting automatically in 5 seconds...</p>
        </div>
      </div>
    </div>
  )
}
