"use client"

import { useAuth } from "@/contexts/auth-context"
import { useEffect, useState } from "react"

export default function AdminTestPage() {
    const { user, isAuthenticated, isLoading } = useAuth()
    const [cookieInfo, setCookieInfo] = useState<string>('')

    useEffect(() => {
        // Check cookies
        const cookies = document.cookie.split(';').map(c => c.trim())
        const adminCookie = cookies.find(c => c.startsWith('adminAuthenticated='))
        setCookieInfo(adminCookie || 'No admin cookie found')
    }, [])

    if (isLoading) {
        return (
            <div className="min-h-screen flex items-center justify-center">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                    <p className="mt-4 text-gray-600">Loading...</p>
                </div>
            </div>
        )
    }

    return (
        <div className="min-h-screen bg-gray-50 p-8">
            <div className="max-w-4xl mx-auto">
                <h1 className="text-3xl font-bold text-gray-900 mb-8">Admin Test Page</h1>

                <div className="bg-white rounded-lg shadow p-6 space-y-4">
                    <h2 className="text-xl font-semibold">Authentication Status</h2>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="bg-gray-50 p-4 rounded">
                            <h3 className="font-medium text-gray-700">Auth Context</h3>
                            <p>Is Authenticated: {isAuthenticated ? '✅ Yes' : '❌ No'}</p>
                            <p>Is Loading: {isLoading ? '✅ Yes' : '❌ No'}</p>
                            <p>User Email: {user?.email || 'None'}</p>
                            <p>User Role: {user?.role || 'None'}</p>
                        </div>

                        <div className="bg-gray-50 p-4 rounded">
                            <h3 className="font-medium text-gray-700">Cookie Info</h3>
                            <p>Admin Cookie: {cookieInfo}</p>
                            <p>All Cookies: {document.cookie || 'No cookies'}</p>
                        </div>
                    </div>

                    <div className="bg-gray-50 p-4 rounded">
                        <h3 className="font-medium text-gray-700">Current URL</h3>
                        <p>{window.location.href}</p>
                    </div>

                    <div className="bg-gray-50 p-4 rounded">
                        <h3 className="font-medium text-gray-700">User Object</h3>
                        <pre className="text-sm bg-gray-100 p-2 rounded overflow-auto">
                            {JSON.stringify(user, null, 2)}
                        </pre>
                    </div>
                </div>

                <div className="mt-8 bg-white rounded-lg shadow p-6">
                    <h2 className="text-xl font-semibold mb-4">Navigation Links</h2>
                    <div className="space-y-2">
                        <a href="/admin/dashboard" className="block text-blue-600 hover:underline">
                            → Admin Dashboard
                        </a>
                        <a href="/admin-login" className="block text-blue-600 hover:underline">
                            → Admin Login
                        </a>
                        <a href="/auth" className="block text-blue-600 hover:underline">
                            → Regular Auth
                        </a>
                    </div>
                </div>
            </div>
        </div>
    )
} 