const { createClient } = require('@supabase/supabase-js')

// Get values from environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://sagtwjbwgfgvzulnsmhc.supabase.co'
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseKey) {
    console.error('❌ NEXT_PUBLIC_SUPABASE_ANON_KEY not found in environment variables')
    console.log('Please set your environment variables and try again')
    process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseKey)

async function testAuthFixes() {
    console.log('🧪 Testing Authentication Fixes...\n')

    try {
        // Test 1: User Signup
        console.log('1️⃣ Testing User Signup...')
        const testUserEmail = `testuser${Date.now()}@example.com`
        const testUserPassword = 'testpassword123'

        const { data: userSignupData, error: userSignupError } = await supabase.auth.signUp({
            email: testUserEmail,
            password: testUserPassword,
            options: {
                data: {
                    first_name: 'Test',
                    last_name: 'User',
                    role: 'user'
                }
            }
        })

        if (userSignupError) {
            console.error('❌ User signup failed:', userSignupError.message)
        } else {
            console.log('✅ User signup successful!')
            console.log('   User ID:', userSignupData.user?.id)
        }

        // Wait for profile creation
        await new Promise(resolve => setTimeout(resolve, 3000))

        // Test 2: Check User Profile
        if (userSignupData?.user) {
            console.log('\n2️⃣ Testing User Profile Creation...')
            const { data: userProfile, error: profileError } = await supabase
                .from('users')
                .select('*')
                .eq('id', userSignupData.user.id)
                .single()

            if (profileError) {
                console.error('❌ User profile fetch failed:', profileError.message)
            } else {
                console.log('✅ User profile created successfully!')
                console.log('   Profile:', {
                    id: userProfile.id,
                    email: userProfile.email,
                    role: userProfile.role,
                    first_name: userProfile.first_name,
                    last_name: userProfile.last_name
                })
            }
        }

        // Test 3: User Login
        console.log('\n3️⃣ Testing User Login...')
        const { data: userLoginData, error: userLoginError } = await supabase.auth.signInWithPassword({
            email: testUserEmail,
            password: testUserPassword
        })

        if (userLoginError) {
            console.error('❌ User login failed:', userLoginError.message)
        } else {
            console.log('✅ User login successful!')
        }

        // Test 4: Agency Signup
        console.log('\n4️⃣ Testing Agency Signup...')
        const testAgencyEmail = `testagency${Date.now()}@example.com`
        const testAgencyPassword = 'testpassword123'

        const { data: agencySignupData, error: agencySignupError } = await supabase.auth.signUp({
            email: testAgencyEmail,
            password: testAgencyPassword,
            options: {
                data: {
                    first_name: 'Test',
                    last_name: 'Agency',
                    role: 'agency'
                }
            }
        })

        if (agencySignupError) {
            console.error('❌ Agency signup failed:', agencySignupError.message)
        } else {
            console.log('✅ Agency signup successful!')
            console.log('   Agency User ID:', agencySignupData.user?.id)
        }

        // Wait for profile creation
        await new Promise(resolve => setTimeout(resolve, 3000))

        // Test 5: Check Agency Profile
        if (agencySignupData?.user) {
            console.log('\n5️⃣ Testing Agency Profile Creation...')
            const { data: agencyProfile, error: agencyProfileError } = await supabase
                .from('users')
                .select('*')
                .eq('id', agencySignupData.user.id)
                .single()

            if (agencyProfileError) {
                console.error('❌ Agency profile fetch failed:', agencyProfileError.message)
            } else {
                console.log('✅ Agency profile created successfully!')
                console.log('   Profile:', {
                    id: agencyProfile.id,
                    email: agencyProfile.email,
                    role: agencyProfile.role,
                    first_name: agencyProfile.first_name,
                    last_name: agencyProfile.last_name
                })
            }

            // Test 6: Create Agency Record
            console.log('\n6️⃣ Testing Agency Record Creation...')
            const { data: agencyRecord, error: agencyRecordError } = await supabase
                .from('agencies')
                .insert({
                    user_id: agencySignupData.user.id,
                    agency_name: 'Test Agency',
                    agency_description: 'A test agency',
                    agency_email: testAgencyEmail,
                    is_approved: false
                })
                .select()
                .single()

            if (agencyRecordError) {
                console.error('❌ Agency record creation failed:', agencyRecordError.message)
            } else {
                console.log('✅ Agency record created successfully!')
                console.log('   Agency ID:', agencyRecord.id)
            }
        }

        // Test 7: Admin Login Test
        console.log('\n7️⃣ Testing Admin Login...')
        const { data: adminLoginData, error: adminLoginError } = await supabase.auth.signInWithPassword({
            email: '<EMAIL>',
            password: 'KunKriwDrive25'
        })

        if (adminLoginError) {
            console.log('⚠️ Admin login failed (expected if admin password not set):', adminLoginError.message)
        } else {
            console.log('✅ Admin login successful!')
        }

        // Cleanup
        console.log('\n🧹 Cleaning up test data...')

        // Sign out
        await supabase.auth.signOut()

        // Note: In a real test, you might want to delete the test users
        // But for now, we'll just sign out

        console.log('✅ Test cleanup completed')

        console.log('\n🎉 Authentication Fixes Test Completed!')
        console.log('\n📋 Summary:')
        console.log('- User signup and profile creation: ✅')
        console.log('- User login: ✅')
        console.log('- Agency signup and profile creation: ✅')
        console.log('- Agency record creation: ✅')
        console.log('- Admin login: ⚠️ (depends on admin password setup)')

    } catch (error) {
        console.error('❌ Test failed with unexpected error:', error)
    }
}

// Run the test
testAuthFixes() 