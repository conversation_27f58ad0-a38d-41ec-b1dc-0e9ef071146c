// Test script to verify redirect functionality
// Run with: node test-redirects.js

// Load environment variables from .env.local
require('dotenv').config({ path: '.env.local' })

const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://sagtwjbwgfgvzulnsmhc.supabase.co'
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseKey) {
    console.error('❌ NEXT_PUBLIC_SUPABASE_ANON_KEY not found')
    process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseKey)

async function testUserLogin() {
    console.log('👤 Testing user login and role check...')

    try {
        const testEmail = '<EMAIL>'
        const testPassword = 'testpassword123'

        console.log(`📝 Signing in as user: ${testEmail}`)

        const { data, error } = await supabase.auth.signInWithPassword({
            email: testEmail,
            password: testPassword
        })

        if (error) {
            console.error('❌ User login failed:', error.message)
            return false
        }

        console.log('✅ User login successful!')
        console.log('User ID:', data.user?.id)

        // Check user profile
        const { data: profile, error: profileError } = await supabase
            .from('users')
            .select('*')
            .eq('id', data.user.id)
            .single()

        if (profileError) {
            console.error('❌ Profile fetch failed:', profileError.message)
            return false
        }

        console.log('✅ User profile loaded!')
        console.log('Role:', profile.role)
        console.log('Email:', profile.email)
        console.log('Name:', profile.first_name, profile.last_name)

        // Sign out
        await supabase.auth.signOut()
        console.log('✅ Signed out successfully')

        return true
    } catch (err) {
        console.error('❌ User login test error:', err.message)
        return false
    }
}

async function testAgencyLogin() {
    console.log('\n🏢 Testing agency login and role check...')

    try {
        const testEmail = '<EMAIL>'
        const testPassword = 'testpassword123'

        console.log(`📝 Signing in as agency: ${testEmail}`)

        const { data, error } = await supabase.auth.signInWithPassword({
            email: testEmail,
            password: testPassword
        })

        if (error) {
            console.error('❌ Agency login failed:', error.message)
            return false
        }

        console.log('✅ Agency login successful!')
        console.log('User ID:', data.user?.id)

        // Check user profile
        const { data: profile, error: profileError } = await supabase
            .from('users')
            .select('*')
            .eq('id', data.user.id)
            .single()

        if (profileError) {
            console.error('❌ Profile fetch failed:', profileError.message)
            return false
        }

        console.log('✅ Agency profile loaded!')
        console.log('Role:', profile.role)
        console.log('Email:', profile.email)
        console.log('Name:', profile.first_name, profile.last_name)

        // Check agency profile
        const { data: agencyProfile, error: agencyError } = await supabase
            .from('agencies')
            .select('*')
            .eq('user_id', data.user.id)
            .single()

        if (agencyError) {
            console.error('❌ Agency profile fetch failed:', agencyError.message)
        } else {
            console.log('✅ Agency record found!')
            console.log('Agency Name:', agencyProfile.agency_name)
        }

        // Sign out
        await supabase.auth.signOut()
        console.log('✅ Signed out successfully')

        return true
    } catch (err) {
        console.error('❌ Agency login test error:', err.message)
        return false
    }
}

async function testAdminLogin() {
    console.log('\n👑 Testing admin login and role check...')

    try {
        const testEmail = '<EMAIL>'
        const testPassword = 'Admin123!'

        console.log(`📝 Signing in as admin: ${testEmail}`)

        const { data, error } = await supabase.auth.signInWithPassword({
            email: testEmail,
            password: testPassword
        })

        if (error) {
            console.error('❌ Admin login failed:', error.message)
            return false
        }

        console.log('✅ Admin login successful!')
        console.log('User ID:', data.user?.id)

        // Check user profile
        const { data: profile, error: profileError } = await supabase
            .from('users')
            .select('*')
            .eq('id', data.user.id)
            .single()

        if (profileError) {
            console.error('❌ Profile fetch failed:', profileError.message)
            return false
        }

        console.log('✅ Admin profile loaded!')
        console.log('Role:', profile.role)
        console.log('Email:', profile.email)
        console.log('Name:', profile.first_name, profile.last_name)

        // Sign out
        await supabase.auth.signOut()
        console.log('✅ Signed out successfully')

        return true
    } catch (err) {
        console.error('❌ Admin login test error:', err.message)
        return false
    }
}

async function main() {
    console.log('🚀 Testing login and role verification...\n')

    const userLoginOk = await testUserLogin()
    if (!userLoginOk) {
        console.log('\n❌ User login test failed.')
        process.exit(1)
    }

    const agencyLoginOk = await testAgencyLogin()
    if (!agencyLoginOk) {
        console.log('\n❌ Agency login test failed.')
        process.exit(1)
    }

    const adminLoginOk = await testAdminLogin()
    if (!adminLoginOk) {
        console.log('\n❌ Admin login test failed.')
        process.exit(1)
    }

    console.log('\n🎉 All login tests passed!')
    console.log('\n📋 Expected redirects:')
    console.log('- User login → /user/dashboard')
    console.log('- Agency login → /agency/dashboard')
    console.log('- Admin login → /admin/dashboard')
    console.log('\n💡 If redirects are not working in the web app, check the browser console for debug messages.')
}

main().catch(console.error) 