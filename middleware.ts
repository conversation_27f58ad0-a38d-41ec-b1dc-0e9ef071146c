import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs'
import { NextResponse } from "next/server"
import type { NextRequest } from "next/server"
import { Database } from '@/types/supabase'

export async function middleware(request: NextRequest) {
  const res = NextResponse.next()
  const supabase = createMiddlewareClient<Database>({ req: request, res })

  // Get the pathname
  const path = request.nextUrl.pathname

  // Refresh session if expired - required for Server Components
  const { data: { session } } = await supabase.auth.getSession()

  // NOTE: Admin route protection moved to layout.tsx for better session handling
  // Middleware has limitations with Supabase sessions in Edge Runtime

  // Restrict admin-login access to specific route only
  if (path === "/admin-login") {
    // Only allow access from the exact /admin-login path
    // This prevents access from other routes or direct navigation
    const referer = request.headers.get('referer')
    const origin = request.nextUrl.origin

    // Allow direct access to /admin-login or from same origin
    if (!referer || referer.startsWith(origin)) {
      // Check if already authenticated admin
      if (session) {
        const { data: user } = await supabase
          .from('users')
          .select('role')
          .eq('id', session.user.id)
          .single()

        if (user?.role === 'admin') {
          return NextResponse.redirect(new URL("/admin/dashboard", request.url))
        }
      }
    }
  }

  // NOTE: Agency and user route protection moved to layout/component level
  // Middleware has limitations with Supabase sessions in Edge Runtime

  // Keep agency registration open (no auth required)
  // All other protection handled by AuthGuard components and layout checks

  // Add admin-login to valid routes
  const validRoutes = [
    "/",
    "/admin-login",
    "/admin",  // Allow /admin for backward compatibility
    "/agency",
    "/user",
    "/auth",
    "/verify-email",
    "/payment",
    "/pricing",
    "/listings",
    "/about",
    "/contact",
    "/faqs",
    "/privacy-policy",
    "/terms",
    "/how-it-works",
    "/for-agencies",
    "/become-host",
    "/learn-more",
    "/blogs",
    "/confirm-reservation",
    "/access-denied",
    "/test-env",
    "/not-found"
  ]

  // Check if the path is a valid route or starts with a valid route prefix
  const isValidRoute = validRoutes.some(route =>
    path === route || path.startsWith(route + "/")
  )

  // Check if it's a Next.js internal route or static file
  const isNextJsRoute = path.startsWith("/_next") ||
    path.startsWith("/api") ||
    path.includes(".")

  // If it's not a valid route and not a Next.js internal route, redirect to home
  if (!isValidRoute && !isNextJsRoute) {
    return NextResponse.redirect(new URL("/", request.url))
  }

  return res
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    "/((?!api|_next/static|_next/image|favicon.ico).*)",
  ],
}

