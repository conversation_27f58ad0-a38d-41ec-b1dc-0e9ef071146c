# Supabase MCP (Model Context Protocol) Setup Guide

This guide will help you set up and use Supabase MCP to keep your local development environment synchronized with your Supabase project.

## 🚀 Quick Start

### 1. Environment Setup

First, create your `.env.local` file:

```bash
# Copy the example file
cp env.example .env.local

# Edit with your actual Supabase credentials
nano .env.local
```

**Required Environment Variables:**
```bash
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

### 2. Get Your Supabase Credentials

1. Go to your [Supabase Dashboard](https://supabase.com/dashboard)
2. Select your project
3. Go to Settings → API
4. Copy:
   - Project URL
   - Anon public key
   - Service role key (keep this secret!)

### 3. Initialize Local Development

```bash
# Start local Supabase services
npm run supabase:start

# This will start:
# - Database on port 54322
# - API on port 54321
# - Studio on port 54323
# - Inbucket (email testing) on port 54324
```

### 4. Apply Schema and Seed Data

```bash
# Reset database and apply migrations + seed data
npm run supabase:reset

# Generate TypeScript types from local schema
npm run supabase:gen-types
```

## 🔄 MCP Workflow

### Local Development Workflow

1. **Start local services:**
   ```bash
   npm run supabase:start
   ```

2. **Make schema changes:**
   - Edit files in `supabase/migrations/`
   - Or use Supabase Studio at http://localhost:54323

3. **Apply changes:**
   ```bash
   npm run supabase:push
   ```

4. **Generate updated types:**
   ```bash
   npm run supabase:gen-types
   ```

### Remote Development Workflow

1. **Pull latest schema from remote:**
   ```bash
   npm run supabase:pull
   ```

2. **Generate types from remote:**
   ```bash
   # Replace YOUR_PROJECT_ID with your actual project ID
   npm run supabase:gen-types-remote
   ```

3. **Apply local changes to remote:**
   ```bash
   npm run supabase:push
   ```

## 📁 Project Structure

```
supabase/
├── config.toml          # Supabase configuration
├── migrations/          # Database migrations
│   └── 20250101000000_initial_schema.sql
├── seed/               # Seed data
│   └── seed.sql
└── .temp/              # Temporary files (auto-generated)
```

## 🛠️ Available Commands

### Local Development
```bash
npm run supabase:start      # Start local Supabase services
npm run supabase:stop       # Stop local services
npm run supabase:status     # Check service status
npm run supabase:reset      # Reset database and apply migrations
```

### Database Operations
```bash
npm run supabase:push       # Push local schema to remote
npm run supabase:pull       # Pull remote schema to local
npm run supabase:gen-types  # Generate types from local schema
```

### Remote Operations
```bash
# Generate types from remote (replace YOUR_PROJECT_ID)
npm run supabase:gen-types-remote
```

## 🔧 Configuration

### Supabase Config (`supabase/config.toml`)

Key settings:
- **Project ID**: `modrivet`
- **API Port**: 54321
- **Database Port**: 54322
- **Studio Port**: 54323
- **Email Testing**: 54324

### Environment Variables

Create `.env.local` with:
```bash
# Required for authentication
NEXT_PUBLIC_SUPABASE_URL=http://127.0.0.1:54321  # Local
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_local_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_local_service_key

# For production, use your remote Supabase URLs
# NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
# NEXT_PUBLIC_SUPABASE_ANON_KEY=your_remote_anon_key
# SUPABASE_SERVICE_ROLE_KEY=your_remote_service_key
```

## 🚨 Troubleshooting

### Common Issues

1. **Port conflicts:**
   ```bash
   # Check what's using the ports
   netstat -ano | findstr :54321
   netstat -ano | findstr :54322
   ```

2. **Database connection issues:**
   ```bash
   # Reset everything
   npm run supabase:stop
   npm run supabase:start
   npm run supabase:reset
   ```

3. **Type generation fails:**
   ```bash
   # Make sure local services are running
   npm run supabase:status
   
   # Then generate types
   npm run supabase:gen-types
   ```

### Reset Everything

```bash
# Complete reset
npm run supabase:stop
rm -rf supabase/.temp
npm run supabase:start
npm run supabase:reset
```

## 🔐 Authentication Setup

### 1. Enable Auth Providers

In Supabase Studio (http://localhost:54323):
1. Go to Authentication → Providers
2. Enable Email provider
3. Configure Google/Facebook if needed

### 2. Configure Email Settings

```bash
# In supabase/config.toml
[auth.email]
enable_signup = true
enable_confirmations = false  # Set to true for production
```

### 3. Test Authentication

```bash
# Check auth status
curl http://127.0.0.1:54321/auth/v1/health
```

## 📊 Database Schema

Your schema includes:
- **Users**: Extended auth.users with profiles
- **Agencies**: Car rental agencies
- **Cars**: Available vehicles
- **Bookings**: Rental reservations
- **Payments**: Transaction records
- **Reviews**: Customer feedback
- **Blogs**: Content management
- **Messages**: Contact/support system

## 🔄 Migration Workflow

### Creating New Migrations

1. **Make schema changes:**
   ```sql
   -- Add to new migration file
   ALTER TABLE cars ADD COLUMN new_feature TEXT;
   ```

2. **Create migration file:**
   ```bash
   # Create new migration
   touch supabase/migrations/$(date +%Y%m%d%H%M%S)_add_new_feature.sql
   ```

3. **Apply migration:**
   ```bash
   npm run supabase:push
   ```

### Best Practices

- Always create migrations for schema changes
- Test migrations locally before pushing to remote
- Use descriptive migration names
- Include rollback scripts if needed

## 🚀 Production Deployment

### 1. Push to Production

```bash
# Push local schema to production
npm run supabase:push

# Generate production types
npm run supabase:gen-types-remote
```

### 2. Environment Variables

Update your production environment:
```bash
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_production_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_production_service_key
```

### 3. Enable RLS Policies

Make sure Row Level Security is properly configured for production.

## 📚 Additional Resources

- [Supabase CLI Documentation](https://supabase.com/docs/guides/cli)
- [Supabase MCP Documentation](https://supabase.com/docs/guides/cli/model-context-protocol)
- [Database Schema Reference](https://supabase.com/docs/guides/database/schema)
- [Authentication Guide](https://supabase.com/docs/guides/auth)

## 🆘 Support

If you encounter issues:
1. Check the troubleshooting section above
2. Review Supabase logs: `npx supabase logs`
3. Check service status: `npm run supabase:status`
4. Reset if needed: `npm run supabase:reset` 