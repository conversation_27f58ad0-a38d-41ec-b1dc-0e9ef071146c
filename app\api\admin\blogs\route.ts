import { NextRequest, NextResponse } from "next/server"
import { writeFile, mkdir, readFile, writeFile as writeJson } from "fs/promises"
import path from "path"

const BLOGS_PATH = path.join(process.cwd(), "public", "images", "blogs")
const BLOGS_DB = path.join(process.cwd(), "app", "blogs", "blogs.json")

export async function POST(req: NextRequest) {
    const formData = await req.formData()
    const title = formData.get("title") as string
    const content = formData.get("content") as string
    const image = formData.get("image") as File | null

    if (!title || !content || !image) {
        return NextResponse.json({ error: "Missing required fields" }, { status: 400 })
    }

    // Ensure blogs image directory exists
    await mkdir(BLOGS_PATH, { recursive: true })

    // Save image
    const ext = image.name.split(".").pop()
    const fileName = `${Date.now()}_${title.replace(/\s+/g, "_")}.${ext}`
    const filePath = path.join(BLOGS_PATH, fileName)
    const arrayBuffer = await image.arrayBuffer()
    await writeFile(filePath, Buffer.from(arrayBuffer))
    const imageUrl = `/images/blogs/${fileName}`

    // Save blog post to JSON file (mock DB)
    let blogs = []
    try {
        const data = await readFile(BLOGS_DB, "utf-8")
        blogs = JSON.parse(data)
    } catch { }
    const newBlog = { id: Date.now(), title, content, image: imageUrl, createdAt: new Date().toISOString() }
    blogs.unshift(newBlog)
    await writeJson(BLOGS_DB, JSON.stringify(blogs, null, 2), "utf-8")

    return NextResponse.json({ success: true, blog: newBlog })
} 