import { Skeleton } from "@/components/ui/skeleton"

export default function UserDashboardLoading() {
    return (
        <div className="w-full py-8">
            <div className="container px-4 md:px-6">
                <Skeleton className="h-10 w-64 mb-8" />
                <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                    {[1, 2, 3].map((i) => (
                        <Skeleton key={i} className="h-32 w-full rounded-lg" />
                    ))}
                </div>
                <div className="mt-12">
                    <Skeleton className="h-8 w-48 mb-4" />
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                        {[1, 2, 3, 4].map((i) => (
                            <Skeleton key={i} className="h-24 w-full rounded" />
                        ))}
                    </div>
                </div>
            </div>
        </div>
    )
} 