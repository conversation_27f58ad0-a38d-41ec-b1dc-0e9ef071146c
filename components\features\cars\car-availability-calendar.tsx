"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { supabase } from '@/lib/supabase/client'
import ReactCalendar from 'react-calendar'
import 'react-calendar/dist/Calendar.css'

interface Booking {
  id: string
  start_date: string
  end_date: string
  status: 'pending' | 'confirmed' | 'active' | 'completed' | 'cancelled'
}

interface CarAvailabilityCalendarProps {
  carId: string
  className?: string
}

export function CarAvailabilityCalendar({ carId, className }: CarAvailabilityCalendarProps) {
  const [bookings, setBookings] = useState<Booking[]>([])
  const [bookedDates, setBookedDates] = useState<Date[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    fetchBookings()
  }, [carId])

  const fetchBookings = async () => {
    try {
      setIsLoading(true)
      const { data, error } = await supabase
        .from('bookings')
        .select('id, start_date, end_date, status')
        .eq('car_id', carId)
        .in('status', ['confirmed', 'active'])

      if (error) {
        console.error('Error fetching bookings:', error)
        return
      }

      setBookings(data || [])
      
      // Generate booked dates from bookings
      const dates: Date[] = []
      data?.forEach(booking => {
        const startDate = new Date(booking.start_date)
        const endDate = new Date(booking.end_date)
        
        // Add all dates between start and end (inclusive)
        const currentDate = new Date(startDate)
        while (currentDate <= endDate) {
          dates.push(new Date(currentDate))
          currentDate.setDate(currentDate.getDate() + 1)
        }
      })
      
      setBookedDates(dates)
    } catch (error) {
      console.error('Error fetching bookings:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const tileClassName = ({ date }: { date: Date }) => {
    const isBooked = bookedDates.some(bookedDate => 
      bookedDate.toDateString() === date.toDateString()
    )
    
    if (isBooked) {
      return 'rc-booked'
    }
    
    return 'rc-available'
  }

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Availability Calendar</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>Availability Calendar</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="relative">
          <ReactCalendar
            tileClassName={tileClassName}
            className="w-full text-base"
            prev2Label={null}
            next2Label={null}
            locale="en-US"
            minDate={new Date()}
          />
        </div>
        <div className="flex gap-4 mt-4 text-xs">
          <div className="flex items-center gap-2">
            <span className="inline-block w-3 h-3 bg-red-300 rounded"></span>
            <span>Booked</span>
          </div>
          <div className="flex items-center gap-2">
            <span className="inline-block w-3 h-3 border border-blue-500 rounded"></span>
            <span>Today</span>
          </div>
          <div className="flex items-center gap-2">
            <span className="inline-block w-3 h-3 bg-gray-100 rounded"></span>
            <span>Available</span>
          </div>
        </div>
        
        {/* Booking summary */}
        <div className="mt-4 p-3 bg-gray-50 rounded-lg">
          <h4 className="font-semibold text-sm mb-2">Booking Summary</h4>
          <div className="text-xs text-gray-600">
            <p>Total bookings: {bookings.length}</p>
            <p>Booked days: {bookedDates.length}</p>
          </div>
        </div>

        <style jsx global>{`
          .rc-booked {
            background: #fecaca !important;
            color: #b91c1c !important;
            font-weight: bold;
            border-radius: 6px;
          }
          .rc-available {
            background: #f3f4f6;
            border-radius: 6px;
          }
          .react-calendar__tile:enabled:hover,
          .react-calendar__tile:enabled:focus {
            background-color: #e5e7eb;
          }
          .react-calendar__tile--now {
            background: #3b82f6 !important;
            color: white !important;
            border-radius: 6px;
          }
          .react-calendar__tile--active {
            background: #1d4ed8 !important;
            color: white !important;
          }
        `}</style>
      </CardContent>
    </Card>
  )
}
