import { supabase } from '@/lib/supabase/client'
import { Database } from '@/types/supabase'
import { RealtimeChannel, RealtimePostgresChangesPayload } from '@supabase/supabase-js'

type Tables = Database['public']['Tables']
type Booking = Tables['bookings']['Row']
type Notification = Tables['notifications']['Row']
type Car = Tables['cars']['Row']
type Payment = Tables['payments']['Row']

export class RealtimeService {
  private static channels: Map<string, RealtimeChannel> = new Map()

  // Generic subscription method
  static subscribe<T extends { [key: string]: any } = any>(
    table: string,
    callback: (payload: RealtimePostgresChangesPayload<T>) => void,
    filter?: string,
    event?: 'INSERT' | 'UPDATE' | 'DELETE' | '*'
  ): RealtimeChannel {
    const channelName = `${table}_${filter || 'all'}_${event || 'all'}`
    
    // Remove existing channel if it exists
    if (this.channels.has(channelName)) {
      this.unsubscribe(channelName)
    }

    let channel = supabase.channel(channelName)

    if (filter) {
      channel = channel.on(
        'postgres_changes' as any,
        {
          event: event || '*',
          schema: 'public',
          table,
          filter
        },
        callback as any
      )
    } else {
      channel = channel.on(
        'postgres_changes' as any,
        {
          event: event || '*',
          schema: 'public',
          table
        },
        callback as any
      )
    }

    channel.subscribe()
    this.channels.set(channelName, channel)
    
    return channel
  }

  // Unsubscribe from a channel
  static unsubscribe(channelName: string) {
    const channel = this.channels.get(channelName)
    if (channel) {
      supabase.removeChannel(channel)
      this.channels.delete(channelName)
    }
  }

  // Unsubscribe from all channels
  static unsubscribeAll() {
    this.channels.forEach((channel, channelName) => {
      supabase.removeChannel(channel)
    })
    this.channels.clear()
  }

  // Booking-specific subscriptions
  static subscribeToUserBookings(
    userId: string,
    callback: (payload: RealtimePostgresChangesPayload<Booking>) => void
  ) {
    return this.subscribe<Booking>(
      'bookings',
      callback,
      `user_id=eq.${userId}`
    )
  }

  static subscribeToAgencyBookings(
    agencyId: string,
    callback: (payload: RealtimePostgresChangesPayload<Booking>) => void
  ) {
    return this.subscribe<Booking>(
      'bookings',
      callback,
      `agency_id=eq.${agencyId}`
    )
  }

  static subscribeToBookingUpdates(
    bookingId: string,
    callback: (payload: RealtimePostgresChangesPayload<Booking>) => void
  ) {
    return this.subscribe<Booking>(
      'bookings',
      callback,
      `id=eq.${bookingId}`,
      'UPDATE'
    )
  }

  // Notification subscriptions
  static subscribeToUserNotifications(
    userId: string,
    callback: (payload: RealtimePostgresChangesPayload<Notification>) => void
  ) {
    return this.subscribe<Notification>(
      'notifications',
      callback,
      `user_id=eq.${userId}`,
      'INSERT'
    )
  }

  // Car status subscriptions
  static subscribeToCarUpdates(
    carId: string,
    callback: (payload: RealtimePostgresChangesPayload<Car>) => void
  ) {
    return this.subscribe<Car>(
      'cars',
      callback,
      `id=eq.${carId}`,
      'UPDATE'
    )
  }

  static subscribeToAgencyCars(
    agencyId: string,
    callback: (payload: RealtimePostgresChangesPayload<Car>) => void
  ) {
    return this.subscribe<Car>(
      'cars',
      callback,
      `agency_id=eq.${agencyId}`
    )
  }

  // Payment subscriptions
  static subscribeToPaymentUpdates(
    bookingId: string,
    callback: (payload: RealtimePostgresChangesPayload<Payment>) => void
  ) {
    return this.subscribe<Payment>(
      'payments',
      callback,
      `booking_id=eq.${bookingId}`,
      'UPDATE'
    )
  }

  // GPS tracking subscriptions (if implemented)
  static subscribeToCarLocation(
    carId: string,
    callback: (payload: any) => void
  ) {
    return this.subscribe(
      'gps_tracking',
      callback,
      `car_id=eq.${carId}`,
      'INSERT'
    )
  }

  // Presence (online users) - for chat or support features
  static subscribeToPresence(
    roomId: string,
    onJoin: (key: string, currentPresences: any, newPresences: any) => void,
    onLeave: (key: string, currentPresences: any, leftPresences: any) => void
  ) {
    const channelName = `presence_${roomId}`
    
    // Remove existing channel if it exists
    if (this.channels.has(channelName)) {
      this.unsubscribe(channelName)
    }

    const channel = supabase.channel(channelName, {
      config: {
        presence: {
          key: roomId,
        },
      },
    })

    channel
      .on('presence', { event: 'sync' }, () => {
        const newState = channel.presenceState()
        console.log('sync', newState)
      })
      .on('presence', { event: 'join' }, ({ key, newPresences }) => {
        onJoin(key, channel.presenceState(), newPresences)
      })
      .on('presence', { event: 'leave' }, ({ key, leftPresences }) => {
        onLeave(key, channel.presenceState(), leftPresences)
      })
      .subscribe()

    this.channels.set(channelName, channel)
    return channel
  }

  // Track user presence
  static async trackPresence(roomId: string, userInfo: any) {
    const channelName = `presence_${roomId}`
    const channel = this.channels.get(channelName)
    
    if (channel) {
      await channel.track(userInfo)
    }
  }

  // Stop tracking presence
  static async untrackPresence(roomId: string) {
    const channelName = `presence_${roomId}`
    const channel = this.channels.get(channelName)
    
    if (channel) {
      await channel.untrack()
    }
  }

  // Broadcast messages (for real-time chat or notifications)
  static subscribeToBroadcast(
    roomId: string,
    event: string,
    callback: (payload: any) => void
  ) {
    const channelName = `broadcast_${roomId}_${event}`
    
    // Remove existing channel if it exists
    if (this.channels.has(channelName)) {
      this.unsubscribe(channelName)
    }

    const channel = supabase.channel(channelName)
      .on('broadcast', { event }, callback)
      .subscribe()

    this.channels.set(channelName, channel)
    return channel
  }

  // Send broadcast message
  static async sendBroadcast(roomId: string, event: string, payload: any) {
    const channelName = `broadcast_${roomId}_${event}`
    const channel = this.channels.get(channelName)
    
    if (channel) {
      await channel.send({
        type: 'broadcast',
        event,
        payload
      })
    }
  }

  // Utility methods
  static getActiveChannels(): string[] {
    return Array.from(this.channels.keys())
  }

  static getChannelStatus(channelName: string): string | null {
    const channel = this.channels.get(channelName)
    return channel ? channel.state : null
  }

  // Helper method to handle common real-time patterns
  static createBookingStatusTracker(
    bookingId: string,
    onStatusChange: (newStatus: Booking['status'], booking: Booking) => void
  ) {
    return this.subscribeToBookingUpdates(bookingId, (payload) => {
      if (payload.eventType === 'UPDATE' && payload.new && payload.old) {
        const newBooking = payload.new as Booking
        const oldBooking = payload.old as Booking
        
        if (newBooking.status !== oldBooking.status) {
          onStatusChange(newBooking.status, newBooking)
        }
      }
    })
  }

  static createNotificationHandler(
    userId: string,
    onNewNotification: (notification: Notification) => void
  ) {
    return this.subscribeToUserNotifications(userId, (payload) => {
      if (payload.eventType === 'INSERT' && payload.new) {
        onNewNotification(payload.new as Notification)
      }
    })
  }
}
