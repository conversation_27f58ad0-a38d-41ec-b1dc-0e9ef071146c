import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { NextResponse } from 'next/server'

export async function GET(request: Request) {
    const supabase = createRouteHandlerClient({ cookies })

    try {
        const { data: { user } } = await supabase.auth.getUser()

        if (!user) {
            return NextResponse.json(
                { error: 'Unauthorized', success: false },
                { status: 401 }
            )
        }

        const { data, error } = await supabase
            .from('bookings')
            .select('*, cars(*), users(*)')
            .eq('user_id', user.id)

        if (error) throw error

        return NextResponse.json({ data, success: true })
    } catch (error) {
        return NextResponse.json(
            { error: 'Failed to fetch bookings', success: false },
            { status: 500 }
        )
    }
}

export async function POST(request: Request) {
    const supabase = createRouteHandlerClient({ cookies })

    try {
        const { data: { user } } = await supabase.auth.getUser()

        if (!user) {
            return NextResponse.json(
                { error: 'Unauthorized', success: false },
                { status: 401 }
            )
        }

        const body = await request.json()

        const { data, error } = await supabase
            .from('bookings')
            .insert({
                ...body,
                user_id: user.id
            })
            .select()
            .single()

        if (error) throw error

        return NextResponse.json({ data, success: true })
    } catch (error) {
        return NextResponse.json(
            { error: 'Failed to create booking', success: false },
            { status: 500 }
        )
    }
} 