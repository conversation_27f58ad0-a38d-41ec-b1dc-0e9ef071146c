-- Fix infinite recursion in RLS policies
-- The issue is that admin policies check for admin role, but users might not have profiles yet

-- Drop problematic admin policies that cause recursion
DROP POLICY IF EXISTS "Admins can view all profiles" ON public.users;
DROP POLICY IF EXISTS "Admins can update all profiles" ON public.users;
DROP POLICY IF EXISTS "Ad<PERSON> can insert profiles" ON public.users;
DROP POLICY IF EXISTS "Admins can view all agencies" ON public.agencies;
DROP POLICY IF EXISTS "Admins can update all agencies" ON public.agencies;
DROP POLICY IF EXISTS "Admins can insert agencies" ON public.agencies;

-- Create simpler policies that don't cause recursion
-- Allow service role to do everything (this includes admin operations)
-- The service role can be used for admin operations without RLS restrictions

-- For now, let's keep the basic policies that work:
-- - Users can view/update their own profile
-- - Users can insert their own profile
-- - Service role can do everything
-- - Anyone can view approved agencies
-- - Agencies can manage their own agency

-- The admin functionality will be handled through the service role
-- or through direct database access for admin operations 