"use client"

import { useState, useEffect } from "react"
import { usePaymentRequests } from "@/contexts/payment-requests-context"
import { DatabaseService } from "@/services/database.service"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { toast } from "sonner"

export default function AdminPaymentRequestsPage() {
    const { paymentRequests } = usePaymentRequests()
    const [dbPaymentRequests, setDbPaymentRequests] = useState([])
    const [isLoading, setIsLoading] = useState(true)

    useEffect(() => {
        const loadPaymentRequests = async () => {
            try {
                const { data, error } = await DatabaseService.getPaymentRequests()
                if (error) {
                    console.error('Error loading payment requests:', error)
                    toast.error('Failed to load payment requests')
                } else {
                    setDbPaymentRequests(data || [])
                }
            } catch (error) {
                console.error('Error loading payment requests:', error)
                toast.error('Failed to load payment requests')
            } finally {
                setIsLoading(false)
            }
        }

        loadPaymentRequests()
    }, [])

    // Combine database and local payment requests
    const allPaymentRequests = [...dbPaymentRequests, ...paymentRequests]

    return (
        <div className="p-6 space-y-6">
            <h1 className="text-3xl font-bold mb-6">Pricing Requests</h1>
            {isLoading ? (
                <div className="text-center py-12">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                    <p className="mt-2 text-gray-600">Loading payment requests...</p>
                </div>
            ) : allPaymentRequests.length === 0 ? (
                <div className="text-center text-muted-foreground py-12">No pricing requests yet.</div>
            ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {allPaymentRequests.map((req, i) => (
                        <Card key={i}>
                            <CardContent className="p-6 space-y-4">
                                <div className="flex items-center justify-between">
                                    <span className="font-semibold">{req.agencyName || req.name}</span>
                                    <Badge variant={req.status === "pending" ? "secondary" : req.status === "approved" ? "default" : "secondary"}>
                                        {req.status ? req.status.charAt(0).toUpperCase() + req.status.slice(1) : 'Pending'}
                                    </Badge>
                                </div>
                                <div className="text-sm text-muted-foreground">{new Date(req.date || req.created_at).toLocaleString()}</div>
                                <div><span className="font-medium">Owner:</span> {req.ownerName || req.name}</div>
                                <div><span className="font-medium">Email:</span> {req.email}</div>
                                <div><span className="font-medium">Phone:</span> {req.phone}</div>
                                <div><span className="font-medium">Plan:</span> {req.plan ? (req.plan === "6months" ? "6-Month Plan" : req.plan === "yearly" ? "Yearly Plan" : "Monthly Plan") : 'Pricing Request'}</div>
                                {req.message && (
                                    <div><span className="font-medium">Message:</span> {req.message}</div>
                                )}
                                {req.receiptUrl && (
                                    <div>
                                        <span className="font-medium">Receipt:</span><br />
                                        <img src={req.receiptUrl} alt="Receipt" className="mt-2 h-32 rounded border" />
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    ))}
                </div>
            )}
        </div>
    )
} 