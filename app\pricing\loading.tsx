import { Skeleton } from "@/components/ui/skeleton"

export default function PricingLoading() {
    return (
        <div className="w-full py-8">
            <div className="container px-4 md:px-6">
                <Skeleton className="h-10 w-64 mb-8" />
                <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                    {[1, 2, 3].map((i) => (
                        <div key={i} className="space-y-4">
                            <Skeleton className="h-8 w-32 mx-auto" />
                            <Skeleton className="h-6 w-24 mx-auto" />
                            <Skeleton className="h-16 w-full rounded-lg" />
                            <Skeleton className="h-4 w-3/4 mx-auto" />
                            <Skeleton className="h-4 w-1/2 mx-auto" />
                            <Skeleton className="h-10 w-32 mx-auto rounded-full" />
                        </div>
                    ))}
                </div>
            </div>
        </div>
    )
} 