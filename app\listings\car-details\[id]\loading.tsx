import { Skeleton } from "@/components/ui/skeleton"

export default function CarDetailsLoading() {
    return (
        <div className="w-full py-8">
            <div className="container px-4 md:px-6">
                {/* Breadcrumb */}
                <div className="mb-6">
                    <Skeleton className="h-4 w-48" />
                </div>

                {/* Car Images */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                    <div className="space-y-4">
                        <Skeleton className="h-96 w-full rounded-lg" />
                        <div className="grid grid-cols-4 gap-2">
                            {[1, 2, 3, 4].map((i) => (
                                <Skeleton key={i} className="h-20 w-full rounded" />
                            ))}
                        </div>
                    </div>

                    {/* Car Info */}
                    <div className="space-y-6">
                        <div>
                            <Skeleton className="h-8 w-3/4 mb-2" />
                            <Skeleton className="h-6 w-1/2" />
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                            {[1, 2, 3, 4, 5, 6].map((i) => (
                                <div key={i} className="flex items-center space-x-2">
                                    <Skeleton className="h-4 w-4" />
                                    <Skeleton className="h-4 w-24" />
                                </div>
                            ))}
                        </div>

                        <div className="space-y-4">
                            <Skeleton className="h-6 w-32" />
                            <div className="grid grid-cols-3 gap-4">
                                {[1, 2, 3].map((i) => (
                                    <div key={i} className="text-center p-4 border rounded">
                                        <Skeleton className="h-6 w-16 mx-auto mb-2" />
                                        <Skeleton className="h-4 w-12 mx-auto" />
                                    </div>
                                ))}
                            </div>
                        </div>

                        <Skeleton className="h-12 w-full rounded" />
                    </div>
                </div>

                {/* Car Details Tabs */}
                <div className="space-y-6">
                    <div className="flex space-x-4 border-b">
                        {[1, 2, 3].map((i) => (
                            <Skeleton key={i} className="h-10 w-24" />
                        ))}
                    </div>

                    <div className="space-y-4">
                        <Skeleton className="h-6 w-48" />
                        <Skeleton className="h-4 w-full" />
                        <Skeleton className="h-4 w-3/4" />
                        <Skeleton className="h-4 w-5/6" />
                    </div>
                </div>
            </div>
        </div>
    )
} 