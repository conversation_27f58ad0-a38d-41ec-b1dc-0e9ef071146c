// <PERSON>ript to check and fix user roles
// Run with: node check-user-role.js

// Load environment variables from .env.local
require('dotenv').config({ path: '.env.local' })

const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://sagtwjbwgfgvzulnsmhc.supabase.co'
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseKey) {
    console.error('❌ NEXT_PUBLIC_SUPABASE_ANON_KEY not found')
    process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseKey)

async function checkUserRoles() {
    console.log('🔍 Checking user roles in database...')

    try {
        // Get all users
        const { data: users, error: usersError } = await supabase
            .from('users')
            .select('*')
            .order('created_at', { ascending: false })

        if (usersError) {
            console.error('❌ Failed to fetch users:', usersError.message)
            return false
        }

        console.log(`\n📊 Found ${users.length} users:`)
        users.forEach((user, index) => {
            console.log(`${index + 1}. ${user.email} - Role: ${user.role} - Created: ${user.created_at}`)
        })

        // Check for admin user
        const adminUser = users.find(u => u.email === '<EMAIL>')
        if (adminUser) {
            console.log(`\n👑 Admin user found: ${adminUser.email} (Role: ${adminUser.role})`)
        } else {
            console.log('\n❌ Admin user not found. Creating one...')
            await createAdminUser()
        }

        // Check for agency users
        const agencyUsers = users.filter(u => u.role === 'agency')
        console.log(`\n🏢 Found ${agencyUsers.length} agency users:`)
        agencyUsers.forEach(user => {
            console.log(`- ${user.email} (ID: ${user.id})`)
        })

        return true
    } catch (err) {
        console.error('❌ Error checking user roles:', err.message)
        return false
    }
}

async function createAdminUser() {
    console.log('👑 Creating admin user...')

    try {
        const adminEmail = '<EMAIL>'
        const adminPassword = 'Admin123!'

        // Create the auth user
        const { data: authData, error: authError } = await supabase.auth.signUp({
            email: adminEmail,
            password: adminPassword,
            options: {
                data: {
                    first_name: 'Admin',
                    last_name: 'User',
                    role: 'admin'
                }
            }
        })

        if (authError) {
            console.error('❌ Admin auth creation failed:', authError.message)
            return false
        }

        console.log('✅ Admin auth user created!')

        // Wait for profile creation
        await new Promise(resolve => setTimeout(resolve, 2000))

        // Update profile to ensure admin role
        const { data: updatedProfile, error: updateError } = await supabase
            .from('users')
            .update({ role: 'admin', is_verified: true })
            .eq('id', authData.user.id)
            .select()
            .single()

        if (updateError) {
            console.error('❌ Admin profile update failed:', updateError.message)
            return false
        }

        console.log('✅ Admin profile updated!')
        console.log('Admin credentials:')
        console.log('Email:', adminEmail)
        console.log('Password:', adminPassword)

        return true
    } catch (err) {
        console.error('❌ Admin creation error:', err.message)
        return false
    }
}

async function fixUserRole(email, newRole) {
    console.log(`🔧 Fixing role for ${email} to ${newRole}...`)

    try {
        // First find the user by email
        const { data: user, error: userError } = await supabase
            .from('users')
            .select('*')
            .eq('email', email)
            .single()

        if (userError) {
            console.error('❌ User not found:', userError.message)
            return false
        }

        // Update the user's role
        const { data: updatedUser, error: updateError } = await supabase
            .from('users')
            .update({ role: newRole })
            .eq('id', user.id)
            .select()
            .single()

        if (updateError) {
            console.error('❌ Failed to update user role:', updateError.message)
            return false
        }

        console.log(`✅ User role updated: ${email} is now ${newRole}`)
        return true
    } catch (err) {
        console.error('❌ Error fixing user role:', err.message)
        return false
    }
}

async function main() {
    console.log('🚀 Checking user roles...\n')

    const success = await checkUserRoles()
    if (!success) {
        console.log('\n❌ Failed to check user roles.')
        process.exit(1)
    }

    console.log('\n✅ User role check completed!')
    console.log('\nTo fix a user role, run:')
    console.log('node -e "require(\'./check-user-role.js\').fixUserRole(\'<EMAIL>\', \'agency\')"')
}

// Export functions for use in other scripts
module.exports = { checkUserRoles, createAdminUser, fixUserRole }

if (require.main === module) {
    main().catch(console.error)
} 