-- Fix RLS policies to prevent infinite recursion
-- This migration creates proper RLS policies that don't cause recursion issues

-- First, let's disable <PERSON><PERSON> temporarily to clean up any problematic policies
ALTER TABLE public.users DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.agencies DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.cars DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.bookings DISABLE ROW LEVEL SECURITY;

-- Drop all existing policies to start fresh
DROP POLICY IF EXISTS "Users can view own profile" ON public.users;
DROP POLICY IF EXISTS "Users can update own profile" ON public.users;
DROP POLICY IF EXISTS "Users can insert own profile" ON public.users;
DROP POLICY IF EXISTS "Enable read access for all users" ON public.users;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON public.users;
DROP POLICY IF EXISTS "Enable update for users based on user_id" ON public.users;
DROP POLICY IF EXISTS "Enable delete for users based on user_id" ON public.users;

-- Create simple, non-recursive policies for users table
CREATE POLICY "Enable read access for all users" ON public.users
    FOR SELECT USING (true);

CREATE POLICY "Enable insert for authenticated users only" ON public.users
    FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "Enable update for users based on user_id" ON public.users
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Enable delete for users based on user_id" ON public.users
    FOR DELETE USING (auth.uid() = id);

-- Create policies for agencies table
DROP POLICY IF EXISTS "Agencies can view own data" ON public.agencies;
DROP POLICY IF EXISTS "Agencies can update own data" ON public.agencies;
DROP POLICY IF EXISTS "Agencies can insert own data" ON public.agencies;
DROP POLICY IF EXISTS "Enable read access for all agencies" ON public.agencies;
DROP POLICY IF EXISTS "Enable insert for authenticated agencies only" ON public.agencies;
DROP POLICY IF EXISTS "Enable update for agencies based on user_id" ON public.agencies;
DROP POLICY IF EXISTS "Enable delete for agencies based on user_id" ON public.agencies;

CREATE POLICY "Enable read access for all agencies" ON public.agencies
    FOR SELECT USING (true);

CREATE POLICY "Enable insert for authenticated agencies only" ON public.agencies
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Enable update for agencies based on user_id" ON public.agencies
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Enable delete for agencies based on user_id" ON public.agencies
    FOR DELETE USING (auth.uid() = user_id);

-- Create policies for cars table
DROP POLICY IF EXISTS "Cars can view own data" ON public.cars;
DROP POLICY IF EXISTS "Cars can update own data" ON public.cars;
DROP POLICY IF EXISTS "Cars can insert own data" ON public.cars;
DROP POLICY IF EXISTS "Enable read access for all cars" ON public.cars;
DROP POLICY IF EXISTS "Enable insert for authenticated agencies only" ON public.cars;
DROP POLICY IF EXISTS "Enable update for cars based on agency_id" ON public.cars;
DROP POLICY IF EXISTS "Enable delete for cars based on agency_id" ON public.cars;

CREATE POLICY "Enable read access for all cars" ON public.cars
    FOR SELECT USING (true);

CREATE POLICY "Enable insert for authenticated agencies only" ON public.cars
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.agencies 
            WHERE agencies.id = cars.agency_id 
            AND agencies.user_id = auth.uid()
        )
    );

CREATE POLICY "Enable update for cars based on agency_id" ON public.cars
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.agencies 
            WHERE agencies.id = cars.agency_id 
            AND agencies.user_id = auth.uid()
        )
    );

CREATE POLICY "Enable delete for cars based on agency_id" ON public.cars
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM public.agencies 
            WHERE agencies.id = cars.agency_id 
            AND agencies.user_id = auth.uid()
        )
    );

-- Create policies for bookings table
DROP POLICY IF EXISTS "Bookings can view own data" ON public.bookings;
DROP POLICY IF EXISTS "Bookings can update own data" ON public.bookings;
DROP POLICY IF EXISTS "Bookings can insert own data" ON public.bookings;
DROP POLICY IF EXISTS "Enable read access for all bookings" ON public.bookings;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON public.bookings;
DROP POLICY IF EXISTS "Enable update for bookings based on user_id" ON public.bookings;
DROP POLICY IF EXISTS "Enable delete for bookings based on user_id" ON public.bookings;

CREATE POLICY "Enable read access for all bookings" ON public.bookings
    FOR SELECT USING (true);

CREATE POLICY "Enable insert for authenticated users only" ON public.bookings
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Enable update for bookings based on user_id" ON public.bookings
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Enable delete for bookings based on user_id" ON public.bookings
    FOR DELETE USING (auth.uid() = user_id);

-- Now re-enable RLS
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.agencies ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.cars ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.bookings ENABLE ROW LEVEL SECURITY;

-- Create a function to handle admin access (bypass RLS for admin users)
CREATE OR REPLACE FUNCTION is_admin()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.users 
        WHERE id = auth.uid() 
        AND role = 'admin'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON public.users TO anon, authenticated;
GRANT ALL ON public.agencies TO anon, authenticated;
GRANT ALL ON public.cars TO anon, authenticated;
GRANT ALL ON public.bookings TO anon, authenticated;
GRANT ALL ON public.reviews TO anon, authenticated;
GRANT ALL ON public.payments TO anon, authenticated;
GRANT ALL ON public.notifications TO anon, authenticated;
GRANT ALL ON public.blogs TO anon, authenticated;
GRANT ALL ON public.messages TO anon, authenticated;
GRANT ALL ON public.agency_documents TO anon, authenticated;
GRANT ALL ON public.coupons TO anon, authenticated;
GRANT ALL ON public.gps_tracking TO anon, authenticated;

-- Grant sequence permissions
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated; 