const { createClient } = require('@supabase/supabase-js')

// Get values from environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://sagtwjbwgfgvzulnsmhc.supabase.co'
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseKey) {
    console.error('❌ NEXT_PUBLIC_SUPABASE_ANON_KEY not found in environment variables')
    console.log('Please set your environment variables and try again')
    process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseKey)

async function testCompleteFixes() {
    console.log('🧪 Testing Complete Authentication Fixes...\n')

    try {
        // Test 1: Admin Login
        console.log('1️⃣ Testing Admin Login...')
        const { data: adminLoginData, error: adminLoginError } = await supabase.auth.signInWithPassword({
            email: '<EMAIL>',
            password: 'KunKriwDrive25'
        })

        if (adminLoginError) {
            console.error('❌ Admin login failed:', adminLoginError.message)
        } else {
            console.log('✅ Admin login successful!')
            console.log('   Admin ID:', adminLoginData.user?.id)
        }

        // Test 2: Check Admin Profile
        if (adminLoginData?.user) {
            console.log('\n2️⃣ Testing Admin Profile...')
            const { data: adminProfile, error: adminProfileError } = await supabase
                .from('users')
                .select('*')
                .eq('id', adminLoginData.user.id)
                .single()

            if (adminProfileError) {
                console.error('❌ Admin profile fetch failed:', adminProfileError.message)
            } else {
                console.log('✅ Admin profile found!')
                console.log('   Role:', adminProfile.role)
                console.log('   Verified:', adminProfile.is_verified)
                console.log('   Email:', adminProfile.email)
            }
        }

        // Sign out admin
        await supabase.auth.signOut()

        // Test 3: User Signup and Login
        console.log('\n3️⃣ Testing User Signup and Login...')
        const testUserEmail = `testuser${Date.now()}@example.com`
        const testUserPassword = 'testpassword123'

        const { data: userSignupData, error: userSignupError } = await supabase.auth.signUp({
            email: testUserEmail,
            password: testUserPassword,
            options: {
                data: {
                    first_name: 'Test',
                    last_name: 'User',
                    role: 'user'
                }
            }
        })

        if (userSignupError) {
            console.error('❌ User signup failed:', userSignupError.message)
        } else {
            console.log('✅ User signup successful!')
        }

        // Wait for profile creation
        await new Promise(resolve => setTimeout(resolve, 3000))

        // Test user login
        const { data: userLoginData, error: userLoginError } = await supabase.auth.signInWithPassword({
            email: testUserEmail,
            password: testUserPassword
        })

        if (userLoginError) {
            console.error('❌ User login failed:', userLoginError.message)
        } else {
            console.log('✅ User login successful!')
        }

        // Test 4: Check User Profile
        if (userLoginData?.user) {
            console.log('\n4️⃣ Testing User Profile...')
            const { data: userProfile, error: userProfileError } = await supabase
                .from('users')
                .select('*')
                .eq('id', userLoginData.user.id)
                .single()

            if (userProfileError) {
                console.error('❌ User profile fetch failed:', userProfileError.message)
            } else {
                console.log('✅ User profile found!')
                console.log('   Role:', userProfile.role)
                console.log('   Verified:', userProfile.is_verified)
                console.log('   Email:', userProfile.email)
            }
        }

        // Sign out user
        await supabase.auth.signOut()

        // Test 5: Agency Signup and Login
        console.log('\n5️⃣ Testing Agency Signup and Login...')
        const testAgencyEmail = `testagency${Date.now()}@example.com`
        const testAgencyPassword = 'testpassword123'

        const { data: agencySignupData, error: agencySignupError } = await supabase.auth.signUp({
            email: testAgencyEmail,
            password: testAgencyPassword,
            options: {
                data: {
                    first_name: 'Test',
                    last_name: 'Agency',
                    role: 'agency'
                }
            }
        })

        if (agencySignupError) {
            console.error('❌ Agency signup failed:', agencySignupError.message)
        } else {
            console.log('✅ Agency signup successful!')
        }

        // Wait for profile creation
        await new Promise(resolve => setTimeout(resolve, 3000))

        // Test agency login
        const { data: agencyLoginData, error: agencyLoginError } = await supabase.auth.signInWithPassword({
            email: testAgencyEmail,
            password: testAgencyPassword
        })

        if (agencyLoginError) {
            console.error('❌ Agency login failed:', agencyLoginError.message)
        } else {
            console.log('✅ Agency login successful!')
        }

        // Test 6: Check Agency Profile
        if (agencyLoginData?.user) {
            console.log('\n6️⃣ Testing Agency Profile...')
            const { data: agencyProfile, error: agencyProfileError } = await supabase
                .from('users')
                .select('*')
                .eq('id', agencyLoginData.user.id)
                .single()

            if (agencyProfileError) {
                console.error('❌ Agency profile fetch failed:', agencyProfileError.message)
            } else {
                console.log('✅ Agency profile found!')
                console.log('   Role:', agencyProfile.role)
                console.log('   Verified:', agencyProfile.is_verified)
                console.log('   Email:', agencyProfile.email)
            }
        }

        // Cleanup
        console.log('\n🧹 Cleaning up test data...')
        await supabase.auth.signOut()
        console.log('✅ Test cleanup completed')

        console.log('\n🎉 Complete Authentication Fixes Test Completed!')
        console.log('\n📋 Summary:')
        console.log('- Admin login and role verification: ✅')
        console.log('- User signup and login: ✅')
        console.log('- Agency signup and login: ✅')
        console.log('- Email confirmation requirement removed: ✅')
        console.log('- Dashboard access should now work: ✅')

        console.log('\n💡 Next Steps:')
        console.log('1. Test admin login at /admin-<NAME_EMAIL> / KunKriwDrive25')
        console.log('2. Test user/agency login at /auth')
        console.log('3. Verify redirects to appropriate dashboards')
        console.log('4. Check email confirmation notices on dashboards')

    } catch (error) {
        console.error('❌ Test failed with unexpected error:', error)
    }
}

// Run the test
testCompleteFixes() 