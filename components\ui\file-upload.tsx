"use client"

import { useState, useRef } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Upload, X, Image as ImageIcon } from 'lucide-react'
import { StorageService } from '@/services/storage.service'
import { toast } from 'sonner'

interface FileUploadProps {
    bucket: string
    folder?: string
    onUploadComplete: (urls: string[]) => void
    multiple?: boolean
    accept?: string
    maxFiles?: number
    maxSize?: number // in MB
}

export function FileUpload({
    bucket,
    folder = '',
    onUploadComplete,
    multiple = false,
    accept = 'image/*',
    maxFiles = 5,
    maxSize = 5
}: FileUploadProps) {
    const [isUploading, setIsUploading] = useState(false)
    const [uploadProgress, setUploadProgress] = useState(0)
    const [previewUrls, setPreviewUrls] = useState<string[]>([])
    const fileInputRef = useRef<HTMLInputElement>(null)

    const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
        const files = Array.from(event.target.files || [])

        if (files.length === 0) return

        // Validate file count
        if (multiple && files.length > maxFiles) {
            toast.error(`Maximum ${maxFiles} files allowed`)
            return
        }

        // Validate file sizes
        const oversizedFiles = files.filter(file => file.size > maxSize * 1024 * 1024)
        if (oversizedFiles.length > 0) {
            toast.error(`Files must be smaller than ${maxSize}MB`)
            return
        }

        setIsUploading(true)
        setUploadProgress(0)

        try {
            const uploadPromises = files.map(async (file, index) => {
                // Create unique filename
                const timestamp = Date.now()
                const fileExtension = file.name.split('.').pop()
                const fileName = `${timestamp}_${index}.${fileExtension}`
                const filePath = folder ? `${folder}/${fileName}` : fileName

                // Create preview URL
                const previewUrl = URL.createObjectURL(file)
                setPreviewUrls(prev => [...prev, previewUrl])

                // Upload to Supabase
                const { data, error } = await StorageService.uploadFile(bucket, filePath, file)

                if (error) {
                    throw error
                }

                // Get public URL
                const publicUrl = StorageService.getPublicUrl(bucket, filePath)

                // Update progress
                setUploadProgress(((index + 1) / files.length) * 100)

                return publicUrl
            })

            const urls = await Promise.all(uploadPromises)
            onUploadComplete(urls)

            toast.success('Files uploaded successfully!')
        } catch (error) {
            console.error('Upload error:', error)
            toast.error('Failed to upload files')
        } finally {
            setIsUploading(false)
            setUploadProgress(0)
            // Clear file input
            if (fileInputRef.current) {
                fileInputRef.current.value = ''
            }
        }
    }

    const removePreview = (index: number) => {
        setPreviewUrls(prev => prev.filter((_, i) => i !== index))
    }

    return (
        <div className="space-y-4">
            <div>
                <Label htmlFor="file-upload">Upload Files</Label>
                <div className="mt-2">
                    <Input
                        ref={fileInputRef}
                        id="file-upload"
                        type="file"
                        accept={accept}
                        multiple={multiple}
                        onChange={handleFileSelect}
                        disabled={isUploading}
                        className="hidden"
                    />
                    <Button
                        type="button"
                        variant="outline"
                        onClick={() => fileInputRef.current?.click()}
                        disabled={isUploading}
                        className="w-full"
                    >
                        {isUploading ? (
                            <>
                                <Upload className="mr-2 h-4 w-4 animate-spin" />
                                Uploading... {Math.round(uploadProgress)}%
                            </>
                        ) : (
                            <>
                                <Upload className="mr-2 h-4 w-4" />
                                Choose Files
                            </>
                        )}
                    </Button>
                </div>
            </div>

            {/* Preview */}
            {previewUrls.length > 0 && (
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                    {previewUrls.map((url, index) => (
                        <div key={index} className="relative group">
                            <img
                                src={url}
                                alt={`Preview ${index + 1}`}
                                className="w-full h-24 object-cover rounded-lg"
                            />
                            <button
                                type="button"
                                onClick={() => removePreview(index)}
                                className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                            >
                                <X className="h-3 w-3" />
                            </button>
                        </div>
                    ))}
                </div>
            )}

            {/* Progress bar */}
            {isUploading && (
                <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${uploadProgress}%` }}
                    />
                </div>
            )}
        </div>
    )
} 