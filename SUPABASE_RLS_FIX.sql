-- Fix RLS policies for authentication issues
-- Run this in your Supabase SQL editor

-- Drop existing problematic policies
DROP POLICY IF EXISTS "Agency owners can insert their agency" ON public.agencies;
DROP POLICY IF EXISTS "Authenticated users can register as agencies" ON public.agencies;

-- Create new comprehensive policies for agencies
CREATE POLICY "Anyone can view agencies" ON public.agencies
    FOR SELECT USING (true);

CREATE POLICY "Agency owners can update their agency" ON public.agencies
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Authenticated users can create agencies" ON public.agencies
    FOR INSERT WITH CHECK (
        auth.uid() IS NOT NULL AND 
        (auth.uid() = user_id OR user_id IS NULL)
    );

-- Fix users table policies
DROP POLICY IF EXISTS "Users can view own profile" ON public.users;
DROP POLICY IF EXISTS "Users can update own profile" ON public.users;
DROP POLICY IF EXISTS "Users can insert own profile" ON public.users;

CREATE POLICY "Users can view own profile" ON public.users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON public.users
    FOR INSERT WITH CHECK (auth.uid() = id);

-- Allow admins to view all users
CREATE POLICY "Admins can view all users" ON public.users
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Allow admins to manage all agencies
CREATE POLICY "Admins can manage all agencies" ON public.agencies
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Create function to handle agency registration
CREATE OR REPLACE FUNCTION handle_agency_registration()
RETURNS TRIGGER AS $$
BEGIN
    -- If this is an agency user, create the agency record
    IF NEW.role = 'agency' THEN
        INSERT INTO public.agencies (
            user_id,
            agency_name,
            agency_email,
            is_approved
        ) VALUES (
            NEW.id,
            COALESCE(NEW.first_name || ' ' || NEW.last_name || ' Agency', 'New Agency'),
            NEW.email,
            false
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for automatic agency creation
DROP TRIGGER IF EXISTS trigger_handle_agency_registration ON public.users;
CREATE TRIGGER trigger_handle_agency_registration
    AFTER INSERT ON public.users
    FOR EACH ROW
    EXECUTE FUNCTION handle_agency_registration();

-- Update storage policies for better access
CREATE POLICY "Anyone can view public images" ON storage.objects
    FOR SELECT USING (bucket_id IN ('car-images', 'agency-logos', 'user-avatars'));

CREATE POLICY "Authenticated users can upload images" ON storage.objects
    FOR INSERT WITH CHECK (
        auth.uid() IS NOT NULL AND
        bucket_id IN ('car-images', 'agency-logos', 'user-avatars')
    );

-- Fix booking policies
DROP POLICY IF EXISTS "Guest bookings can be created" ON public.bookings;
CREATE POLICY "Anyone can create bookings" ON public.bookings
    FOR INSERT WITH CHECK (true);

-- Create admin user if not exists (run this manually with your admin email)
-- INSERT INTO auth.users (id, email, encrypted_password, email_confirmed_at, created_at, updated_at)
-- VALUES (
--     gen_random_uuid(),
--     '<EMAIL>',
--     crypt('admin123', gen_salt('bf')),
--     now(),
--     now(),
--     now()
-- ) ON CONFLICT (email) DO NOTHING;

-- Insert admin profile (run this manually after creating admin user)
-- INSERT INTO public.users (id, email, first_name, last_name, role, is_verified)
-- SELECT id, email, 'Admin', 'User', 'admin', true
-- FROM auth.users
-- WHERE email = '<EMAIL>'
-- ON CONFLICT (id) DO UPDATE SET role = 'admin', is_verified = true;
