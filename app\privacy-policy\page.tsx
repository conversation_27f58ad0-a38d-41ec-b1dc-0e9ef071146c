"use client"

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Shield, Eye, Lock, Users, FileText, Calendar } from "lucide-react"

export default function PrivacyPolicyPage() {
    return (
        <div className="container mx-auto px-4 py-8">
            <div className="max-w-4xl mx-auto">
                <div className="text-center mb-8">
                    <div className="flex justify-center mb-4">
                        <div className="p-3 bg-blue-100 rounded-full">
                            <Shield className="h-8 w-8 text-blue-600" />
                        </div>
                    </div>
                    <h1 className="text-4xl font-bold text-gray-900 mb-4">Privacy Policy</h1>
                    <p className="text-lg text-gray-600">Last updated: January 2025</p>
                </div>

                <div className="space-y-6">
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Eye className="h-5 w-5 text-blue-600" />
                                Information We Collect
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div>
                                <h3 className="font-semibold text-lg mb-2">Personal Information</h3>
                                <ul className="list-disc list-inside space-y-1 text-gray-600">
                                    <li>Name, email address, and phone number</li>
                                    <li>Driver's license information and driving history</li>
                                    <li>Payment information and billing address</li>
                                    <li>Profile information and preferences</li>
                                </ul>
                            </div>
                            <div>
                                <h3 className="font-semibold text-lg mb-2">Usage Information</h3>
                                <ul className="list-disc list-inside space-y-1 text-gray-600">
                                    <li>Booking history and rental preferences</li>
                                    <li>Device information and IP address</li>
                                    <li>Website usage patterns and interactions</li>
                                    <li>Location data (with your consent)</li>
                                </ul>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Lock className="h-5 w-5 text-green-600" />
                                How We Use Your Information
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <ul className="list-disc list-inside space-y-2 text-gray-600">
                                <li>Process and manage your car rental bookings</li>
                                <li>Verify your identity and driving eligibility</li>
                                <li>Process payments and send billing information</li>
                                <li>Provide customer support and respond to inquiries</li>
                                <li>Send important updates about your bookings</li>
                                <li>Improve our services and user experience</li>
                                <li>Comply with legal obligations and regulations</li>
                            </ul>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Users className="h-5 w-5 text-purple-600" />
                                Information Sharing
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <p className="text-gray-600 mb-4">
                                We do not sell, trade, or rent your personal information to third parties. We may share your information only in the following circumstances:
                            </p>
                            <ul className="list-disc list-inside space-y-2 text-gray-600">
                                <li>With car rental agencies to fulfill your bookings</li>
                                <li>With payment processors to complete transactions</li>
                                <li>With law enforcement when required by law</li>
                                <li>With your explicit consent for specific purposes</li>
                            </ul>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Shield className="h-5 w-5 text-red-600" />
                                Data Security
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <p className="text-gray-600 mb-4">
                                We implement industry-standard security measures to protect your personal information:
                            </p>
                            <ul className="list-disc list-inside space-y-2 text-gray-600">
                                <li>Encryption of sensitive data in transit and at rest</li>
                                <li>Regular security audits and vulnerability assessments</li>
                                <li>Access controls and authentication measures</li>
                                <li>Secure data centers and infrastructure</li>
                                <li>Employee training on data protection practices</li>
                            </ul>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Calendar className="h-5 w-5 text-orange-600" />
                                Data Retention
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <p className="text-gray-600">
                                We retain your personal information for as long as necessary to provide our services, comply with legal obligations, resolve disputes, and enforce our agreements. Typically, this means we keep your information for 7 years after your last interaction with us, unless a longer retention period is required by law.
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <FileText className="h-5 w-5 text-indigo-600" />
                                Your Rights
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <p className="text-gray-600 mb-4">You have the following rights regarding your personal information:</p>
                            <ul className="list-disc list-inside space-y-2 text-gray-600">
                                <li>Access and review your personal information</li>
                                <li>Request correction of inaccurate information</li>
                                <li>Request deletion of your personal information</li>
                                <li>Object to processing of your information</li>
                                <li>Request data portability</li>
                                <li>Withdraw consent for data processing</li>
                            </ul>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Contact Us</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <p className="text-gray-600 mb-4">
                                If you have any questions about this Privacy Policy or our data practices, please contact us:
                            </p>
                            <div className="space-y-2 text-gray-600">
                                <p><strong>Email:</strong> <EMAIL></p>
                                <p><strong>Phone:</strong> +212 524 123 456</p>
                                <p><strong>Address:</strong> 123, Rue Mohammed V, Marrakech, Morocco</p>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </div>
    )
} 