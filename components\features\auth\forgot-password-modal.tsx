"use client"

import type React from "react"

import { useState } from "react"
import { Mail, X, CheckCircle, Lock, Eye, EyeOff, ArrowLeft } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog"
import { useI18n } from "@/i18n/i18n-provider"

interface ForgotPasswordModalProps {
  isOpen: boolean
  onClose: () => void
}

type ResetStep = "email" | "code" | "newPassword" | "success"

export function ForgotPasswordModal({ isOpen, onClose }: ForgotPasswordModalProps) {
  const { t } = useI18n()
  const [currentStep, setCurrentStep] = useState<ResetStep>("email")
  const [email, setEmail] = useState("")
  const [resetCode, setResetCode] = useState("")
  const [newPassword, setNewPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)

  const handleEmailSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!email) {
      setError(t("auth.enterEmail"))
      return
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      setError(t("auth.invalidEmailFormat"))
      return
    }

    setError(null)
    setIsSubmitting(true)

    try {
      // Simulate API call to send reset code
      await new Promise((resolve) => setTimeout(resolve, 1500))

      // In a real app, this would call an API endpoint to send a password reset code
      console.log("Password reset code sent to:", email)

      setCurrentStep("code")
    } catch (err) {
      setError(t("auth.networkError"))
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleCodeSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!resetCode) {
      setError(t("auth.enterResetCode"))
      return
    }

    if (resetCode.length !== 6) {
      setError(t("auth.invalidResetCode"))
      return
    }

    setError(null)
    setIsSubmitting(true)

    try {
      // Simulate API call to verify reset code
      await new Promise((resolve) => setTimeout(resolve, 1000))

      // In a real app, this would verify the reset code
      console.log("Verifying reset code:", resetCode)

      setCurrentStep("newPassword")
    } catch (err) {
      setError(t("auth.invalidResetCode"))
    } finally {
      setIsSubmitting(false)
    }
  }

  const handlePasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!newPassword || !confirmPassword) {
      setError(t("auth.fillAllFields"))
      return
    }

    if (newPassword.length < 8) {
      setError(t("auth.weakPassword"))
      return
    }

    if (newPassword !== confirmPassword) {
      setError(t("auth.passwordMismatch"))
      return
    }

    setError(null)
    setIsSubmitting(true)

    try {
      // Simulate API call to reset password
      await new Promise((resolve) => setTimeout(resolve, 1500))

      // In a real app, this would reset the password
      console.log("Password reset successful for:", email)

      setCurrentStep("success")
    } catch (err) {
      setError(t("auth.passwordResetError"))
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleClose = () => {
    // Reset state when closing
    setCurrentStep("email")
    setEmail("")
    setResetCode("")
    setNewPassword("")
    setConfirmPassword("")
    setError(null)
    onClose()
  }

  const goBack = () => {
    if (currentStep === "code") {
      setCurrentStep("email")
      setResetCode("")
    } else if (currentStep === "newPassword") {
      setCurrentStep("code")
      setNewPassword("")
      setConfirmPassword("")
    }
    setError(null)
  }

  const getStepTitle = () => {
    switch (currentStep) {
      case "email":
        return t("auth.resetPassword")
      case "code":
        return t("auth.enterResetCode")
      case "newPassword":
        return t("auth.createNewPassword")
      case "success":
        return t("auth.passwordResetSuccess")
      default:
        return t("auth.resetPassword")
    }
  }

  const getStepDescription = () => {
    switch (currentStep) {
      case "email":
        return t("auth.resetPasswordDescription")
      case "code":
        return t("auth.enterResetCodeDescription")
      case "newPassword":
        return t("auth.createNewPasswordDescription")
      case "success":
        return t("auth.passwordResetSuccessDescription")
      default:
        return t("auth.resetPasswordDescription")
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{getStepTitle()}</DialogTitle>
          <DialogDescription>{getStepDescription()}</DialogDescription>
        </DialogHeader>

        <DialogClose className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
          <X className="h-4 w-4" />
          <span className="sr-only">Close</span>
        </DialogClose>

        {currentStep === "success" ? (
          <div className="flex flex-col items-center justify-center py-4 text-center">
            <CheckCircle className="h-12 w-12 text-green-500 mb-4" />
            <h3 className="text-xl font-medium mb-2">{t("auth.passwordResetSuccess")}</h3>
            <p className="text-muted-foreground mb-4">
              {t("auth.passwordResetSuccessDescription")}
            </p>
            <Button onClick={handleClose} className="w-full">
              {t("auth.close")}
            </Button>
          </div>
        ) : (
          <form onSubmit={
            currentStep === "email" ? handleEmailSubmit :
              currentStep === "code" ? handleCodeSubmit :
                handlePasswordSubmit
          }>
            <div className="space-y-4 py-2">
              {/* Back button for code and newPassword steps */}
              {(currentStep === "code" || currentStep === "newPassword") && (
                <Button
                  type="button"
                  variant="ghost"
                  onClick={goBack}
                  className="p-0 h-auto text-sm"
                >
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  {t("auth.goBack")}
                </Button>
              )}

              {/* Email Step */}
              {currentStep === "email" && (
                <div className="space-y-2">
                  <Label htmlFor="email">{t("auth.email")}</Label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="email"
                      type="email"
                      placeholder={t("auth.emailPlaceholder")}
                      className="pl-10"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      required
                      disabled={isSubmitting}
                    />
                  </div>
                </div>
              )}

              {/* Code Step */}
              {currentStep === "code" && (
                <div className="space-y-2">
                  <Label htmlFor="reset-code">{t("auth.resetCode")}</Label>
                  <Input
                    id="reset-code"
                    type="text"
                    placeholder="123456"
                    value={resetCode}
                    onChange={(e) => setResetCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
                    maxLength={6}
                    required
                    disabled={isSubmitting}
                    className="text-center text-lg font-mono"
                  />
                  <p className="text-sm text-muted-foreground">
                    {t("auth.resetCodeSentTo")} <span className="font-medium">{email}</span>
                  </p>
                </div>
              )}

              {/* New Password Step */}
              {currentStep === "newPassword" && (
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="new-password">{t("auth.newPassword")}</Label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                      <Input
                        id="new-password"
                        type={showPassword ? "text" : "password"}
                        placeholder={t("auth.newPasswordPlaceholder")}
                        className="pl-10"
                        value={newPassword}
                        onChange={(e) => setNewPassword(e.target.value)}
                        required
                        disabled={isSubmitting}
                      />
                      <button
                        type="button"
                        className="absolute right-3 top-3 text-muted-foreground"
                        onClick={() => setShowPassword(!showPassword)}
                        disabled={isSubmitting}
                      >
                        {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </button>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="confirm-new-password">{t("auth.confirmNewPassword")}</Label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                      <Input
                        id="confirm-new-password"
                        type={showConfirmPassword ? "text" : "password"}
                        placeholder={t("auth.confirmNewPasswordPlaceholder")}
                        className="pl-10"
                        value={confirmPassword}
                        onChange={(e) => setConfirmPassword(e.target.value)}
                        required
                        disabled={isSubmitting}
                      />
                      <button
                        type="button"
                        className="absolute right-3 top-3 text-muted-foreground"
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        disabled={isSubmitting}
                      >
                        {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </button>
                    </div>
                  </div>
                </div>
              )}

              {error && <p className="text-sm text-red-500">{error}</p>}
            </div>

            <DialogFooter className="mt-4">
              <Button
                type="submit"
                className="w-full bg-gradient-to-r from-primary to-secondary hover:opacity-90"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  t("auth.processing")
                ) : (
                  currentStep === "email" ? t("auth.sendResetCode") :
                    currentStep === "code" ? t("auth.verifyCode") :
                      t("auth.resetPassword")
                )}
              </Button>
            </DialogFooter>
          </form>
        )}
      </DialogContent>
    </Dialog>
  )
}
