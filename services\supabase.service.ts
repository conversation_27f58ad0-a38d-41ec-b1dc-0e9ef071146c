import { supabase } from '@/lib/supabase/client'
import { Database } from '@/types/supabase'

type Tables = Database['public']['Tables']
type User = Tables['users']['Row']
type Car = Tables['cars']['Row']
type Booking = Tables['bookings']['Row']
type Agency = Tables['agencies']['Row']

export class SupabaseService {
    // User operations
    static async getCurrentUser() {
        const { data: { user } } = await supabase.auth.getUser()
        return user
    }

    static async getUserProfile(userId: string) {
        const { data, error } = await supabase
            .from('users')
            .select('*')
            .eq('id', userId)
            .single()

        return { data, error }
    }

    static async updateUserProfile(userId: string, updates: Partial<User>) {
        const { data, error } = await supabase
            .from('users')
            .update(updates)
            .eq('id', userId)
            .select()
            .single()

        return { data, error }
    }

    // Car operations
    static async getCars(filters?: {
        location?: string
        price_min?: number
        price_max?: number
        agency_id?: string
    }) {
        let query = supabase.from('cars').select('*, agencies(*)')

        if (filters?.location) {
            query = query.ilike('location', `%${filters.location}%`)
        }
        if (filters?.price_min) {
            query = query.gte('price_per_day', filters.price_min)
        }
        if (filters?.price_max) {
            query = query.lte('price_per_day', filters.price_max)
        }
        if (filters?.agency_id) {
            query = query.eq('agency_id', filters.agency_id)
        }

        const { data, error } = await query
        return { data, error }
    }

    static async getCarById(id: string) {
        const { data, error } = await supabase
            .from('cars')
            .select('*, agencies(*)')
            .eq('id', id)
            .single()

        return { data, error }
    }

    static async createCar(carData: Omit<Car, 'id' | 'created_at' | 'updated_at'>) {
        const { data, error } = await supabase
            .from('cars')
            .insert(carData)
            .select()
            .single()

        return { data, error }
    }

    static async updateCar(id: string, updates: Partial<Car>) {
        const { data, error } = await supabase
            .from('cars')
            .update(updates)
            .eq('id', id)
            .select()
            .single()

        return { data, error }
    }

    static async deleteCar(id: string) {
        const { error } = await supabase
            .from('cars')
            .delete()
            .eq('id', id)

        return { error }
    }

    // Booking operations
    static async getBookings(userId?: string) {
        let query = supabase.from('bookings').select('*, cars(*), users(*)')

        if (userId) {
            query = query.eq('user_id', userId)
        }

        const { data, error } = await query
        return { data, error }
    }

    static async createBooking(bookingData: Omit<Booking, 'id' | 'created_at'>) {
        const { data, error } = await supabase
            .from('bookings')
            .insert(bookingData)
            .select()
            .single()

        return { data, error }
    }

    static async updateBookingStatus(id: string, status: string) {
        const { data, error } = await supabase
            .from('bookings')
            .update({ status })
            .eq('id', id)
            .select()
            .single()

        return { data, error }
    }

    // Agency operations
    static async getAgencies() {
        const { data, error } = await supabase
            .from('agencies')
            .select('*')

        return { data, error }
    }

    static async getAgencyById(id: string) {
        const { data, error } = await supabase
            .from('agencies')
            .select('*')
            .eq('id', id)
            .single()

        return { data, error }
    }

    // File upload
    static async uploadFile(bucket: string, path: string, file: File) {
        const { data, error } = await supabase.storage
            .from(bucket)
            .upload(path, file)

        return { data, error }
    }

    static async getFileUrl(bucket: string, path: string) {
        const { data } = supabase.storage
            .from(bucket)
            .getPublicUrl(path)

        return data.publicUrl
    }
} 