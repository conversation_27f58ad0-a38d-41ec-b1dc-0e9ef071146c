import { supabase } from '@/lib/supabase/client'
import { Database } from '@/types/supabase'

type Tables = Database['public']['Tables']
type User = Tables['users']['Row']
type UserInsert = Tables['users']['Insert']
type Agency = Tables['agencies']['Row']
type AgencyInsert = Tables['agencies']['Insert']

export class SupabaseAuthService {
    static async signUp(email: string, password: string, userData: Partial<UserInsert>) {
        try {
            console.log('🚀 Starting signup process for:', email)

            const { data, error } = await supabase.auth.signUp({
                email,
                password,
                options: {
                    data: userData,
                    emailRedirectTo: `${window.location.origin}/auth/callback`
                }
            })

            if (error) {
                console.error('❌ Auth signup error:', error)
                return { data, error }
            }

            if (data.user) {
                console.log('✅ Auth user created, ID:', data.user.id)

                // Wait a moment for the trigger to create the profile
                await new Promise(resolve => setTimeout(resolve, 2000))

                // Check if profile was created by trigger
                const { data: existingProfile, error: profileCheckError } = await supabase
                    .from('users')
                    .select('*')
                    .eq('id', data.user.id)
                    .single()

                if (profileCheckError && profileCheckError.code === 'PGRST116') {
                    console.log('⚠️ Profile not found, creating manually...')
                    // Profile doesn't exist, create it manually
                    const { error: insertError } = await supabase
                        .from('users')
                        .insert({
                            id: data.user.id,
                            email: data.user.email!,
                            first_name: userData.first_name || 'User',
                            last_name: userData.last_name || '',
                            phone: userData.phone,
                            role: userData.role || 'user',
                            is_verified: false
                        })

                    if (insertError) {
                        console.error('❌ Failed to insert user profile:', insertError)
                        return { data: null, error: insertError }
                    }
                    console.log('✅ User profile created manually')
                } else if (existingProfile) {
                    console.log('✅ User profile already exists')
                }
            }

            return { data, error: null }
        } catch (error) {
            console.error('❌ Unexpected signup error:', error)
            return { data: null, error }
        }
    }

    static async signIn(email: string, password: string) {
        try {
            console.log('🔑 Attempting sign in for:', email)

            const { data, error } = await supabase.auth.signInWithPassword({
                email,
                password,
            })

            if (error) {
                console.error('❌ Sign in error:', error)
                return { data, error }
            }

            if (data.user) {
                console.log('✅ Sign in successful for user:', data.user.id)
            }

            return { data, error }
        } catch (error) {
            console.error('❌ Unexpected sign in error:', error)
            return { data: null, error }
        }
    }

    static async signOut() {
        const { error } = await supabase.auth.signOut()
        return { error }
    }

    static async getCurrentUser() {
        const { data: { user } } = await supabase.auth.getUser()
        return user
    }

    static async getUserProfile(userId: string): Promise<{ data: User | null, error: any }> {
        try {
            console.log('👤 Fetching user profile for:', userId)

            const { data, error } = await supabase
                .from('users')
                .select('*')
                .eq('id', userId)
                .single()

            if (error) {
                console.error('❌ Error fetching user profile:', error)

                // If profile doesn't exist, try to create it
                if (error.code === 'PGRST116') {
                    console.log('⚠️ User profile not found, attempting to create...')
                    const { data: authUser } = await supabase.auth.getUser()
                    if (authUser.user) {
                        const { data: newProfile, error: createError } = await supabase
                            .from('users')
                            .insert({
                                id: userId,
                                email: authUser.user.email!,
                                first_name: authUser.user.user_metadata?.first_name || 'User',
                                last_name: authUser.user.user_metadata?.last_name || '',
                                role: authUser.user.user_metadata?.role || 'user',
                                is_verified: authUser.user.email_confirmed_at !== null
                            })
                            .select()
                            .single()

                        if (createError) {
                            console.error('❌ Error creating user profile:', createError)
                            return { data: null, error: createError }
                        }

                        console.log('✅ User profile created successfully')
                        return { data: newProfile, error: null }
                    }
                }

                return { data: null, error }
            }

            console.log('✅ User profile fetched successfully')
            return { data, error: null }
        } catch (error) {
            console.error('❌ Unexpected error fetching user profile:', error)
            return { data: null, error }
        }
    }

    static async updateUserProfile(userId: string, updates: Partial<User>) {
        const { data, error } = await supabase
            .from('users')
            .update(updates)
            .eq('id', userId)
            .select()
            .single()

        return { data, error }
    }

    static async resetPassword(email: string) {
        const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
            redirectTo: `${window.location.origin}/auth/reset-password`
        })
        return { data, error }
    }

    static async updatePassword(password: string) {
        const { data, error } = await supabase.auth.updateUser({
            password
        })
        return { data, error }
    }

    static async verifyEmail(token: string) {
        const { data, error } = await supabase.auth.verifyOtp({
            token_hash: token,
            type: 'email'
        })
        return { data, error }
    }

    static async resendVerification(email: string) {
        const { data, error } = await supabase.auth.resend({
            type: 'signup',
            email,
            options: {
                emailRedirectTo: `${window.location.origin}/auth/callback`
            }
        })
        return { data, error }
    }

    // Agency-specific methods
    static async registerAgency(userData: {
        email: string
        password: string
        first_name: string
        last_name: string
        phone?: string
        agency_name: string
        agency_description?: string
        agency_address?: string
        agency_phone?: string
        agency_website?: string
    }) {
        try {
            console.log('🏢 Starting agency registration for:', userData.email)

            // First create the user account
            const { data: authData, error: authError } = await this.signUp(
                userData.email,
                userData.password,
                {
                    first_name: userData.first_name,
                    last_name: userData.last_name,
                    phone: userData.phone,
                    role: 'agency'
                }
            )

            if (authError || !authData?.user) {
                console.error('❌ Auth error during agency registration:', authError)
                return { data: null, error: authError }
            }

            console.log('✅ Agency user created, ID:', authData.user.id)

            // Wait for user profile to be created
            await new Promise(resolve => setTimeout(resolve, 2000))

            // Check if agency record already exists
            const { data: existingAgency, error: checkError } = await supabase
                .from('agencies')
                .select('*')
                .eq('user_id', authData.user.id)
                .single()

            if (existingAgency) {
                console.log('⚠️ Agency record already exists for user:', authData.user.id)
                return { data: { user: authData.user, agency: existingAgency }, error: null }
            }

            // Create the agency record
            const { data: agencyData, error: agencyError } = await supabase
                .from('agencies')
                .insert({
                    user_id: authData.user.id,
                    agency_name: userData.agency_name,
                    agency_description: userData.agency_description,
                    agency_address: userData.agency_address,
                    agency_phone: userData.agency_phone,
                    agency_email: userData.email,
                    agency_website: userData.agency_website,
                    is_approved: false
                })
                .select()
                .single()

            if (agencyError) {
                console.error('❌ Agency creation error:', agencyError)

                // If it's a duplicate key error, try to fetch the existing agency record
                if (agencyError.code === '23505' && agencyError.message.includes('agencies_user_id_key')) {
                    console.log('🔄 Duplicate key detected, fetching existing agency record...')
                    const { data: existingAgencyAfterError, error: fetchError } = await supabase
                        .from('agencies')
                        .select('*')
                        .eq('user_id', authData.user.id)
                        .single()

                    if (existingAgencyAfterError && !fetchError) {
                        console.log('✅ Found existing agency record after duplicate key error')
                        return { data: { user: authData.user, agency: existingAgencyAfterError }, error: null }
                    }
                }

                // If agency creation fails and we can't recover, we should clean up the user account
                try {
                    await supabase.auth.admin.deleteUser(authData.user.id)
                } catch (deleteError) {
                    console.error('❌ Failed to delete user after agency creation error:', deleteError)
                }
                return { data: null, error: agencyError }
            }

            console.log('✅ Agency record created successfully')
            return { data: { user: authData.user, agency: agencyData }, error: null }
        } catch (error) {
            console.error('❌ Unexpected error during agency registration:', error)
            return { data: null, error: { message: 'Registration failed. Please try again.' } }
        }
    }

    static async checkUserRole(userId: string): Promise<'user' | 'agency' | 'admin' | null> {
        const { data, error } = await supabase
            .from('users')
            .select('role')
            .eq('id', userId)
            .single()

        if (error || !data) return null
        return data.role as 'user' | 'agency' | 'admin'
    }

    static async getAgencyByUserId(userId: string): Promise<{ data: Agency | null, error: any }> {
        const { data, error } = await supabase
            .from('agencies')
            .select('*')
            .eq('user_id', userId)
            .single()

        return { data, error }
    }

    static async signInWithGoogle() {
        const { data, error } = await supabase.auth.signInWithOAuth({
            provider: 'google',
            options: {
                redirectTo: `${window.location.origin}/auth/callback`
            }
        })
        return { data, error }
    }

    static async signInWithFacebook() {
        const { data, error } = await supabase.auth.signInWithOAuth({
            provider: 'facebook',
            options: {
                redirectTo: `${window.location.origin}/auth/callback`
            }
        })
        return { data, error }
    }

    static async getSession() {
        const { data: { session } } = await supabase.auth.getSession()
        return session
    }

    static async refreshSession() {
        const { data, error } = await supabase.auth.refreshSession()
        return { data, error }
    }

    static onAuthStateChange(callback: (event: string, session: any) => void) {
        return supabase.auth.onAuthStateChange(callback)
    }
}
