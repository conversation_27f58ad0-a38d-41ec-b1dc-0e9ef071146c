"use client"

import * as React from "react"
import { CalendarIcon } from "lucide-react"
import { addDays, format } from "date-fns"
import { DateRange } from "react-day-picker"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

interface DatePickerWithRangeProps {
  date?: DateRange
  setDate: (date: DateRange | undefined) => void
  unavailableDates?: Date[]
  className?: string
}

export function DatePickerWithRange({
  date,
  setDate,
  unavailableDates = [],
  className,
}: DatePickerWithRangeProps) {
  return (
    <div className={cn("grid gap-2", className)}>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            id="date"
            variant={"outline"}
            className={cn(
              "w-full justify-start text-left font-normal",
              !date && "text-muted-foreground"
            )}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {date?.from ? (
              date.to ? (
                <>
                  {format(date.from, "LLL dd, y")} -{" "}
                  {format(date.to, "LLL dd, y")}
                </>
              ) : (
                format(date.from, "LLL dd, y")
              )
            ) : (
              <span>Pick a date range</span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <Calendar
            initialFocus
            mode="range"
            defaultMonth={date?.from}
            selected={date}
            onSelect={setDate}
            numberOfMonths={2}
            disabled={(date) => {
              // Disable unavailable dates (rented days)
              return unavailableDates.some(unavailableDate =>
                unavailableDate.toDateString() === date.toDateString()
              )
            }}
            modifiers={{
              unavailable: unavailableDates,
            }}
            modifiersStyles={{
              unavailable: {
                backgroundColor: '#fecaca',
                color: '#b91c1c',
                fontWeight: 'bold',
              },
            }}
          />
        </PopoverContent>
      </Popover>
    </div>
  )
}
