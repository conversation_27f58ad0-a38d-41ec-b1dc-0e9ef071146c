-- Fix authentication triggers and user profile creation
-- This migration ensures proper user profile creation and prevents conflicts

-- Drop existing triggers to avoid conflicts
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;

-- Create a more robust function to handle new user creation
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    -- Check if user profile already exists to avoid conflicts
    IF NOT EXISTS (SELECT 1 FROM public.users WHERE id = NEW.id) THEN
        INSERT INTO public.users (
            id,
            email,
            first_name,
            last_name,
            role,
            is_verified
        ) VALUES (
            NEW.id,
            NEW.email,
            COALESCE(NEW.raw_user_meta_data->>'first_name', 'User'),
            COALESCE(NEW.raw_user_meta_data->>'last_name', ''),
            COALESCE(NEW.raw_user_meta_data->>'role', 'user'),
            NEW.email_confirmed_at IS NOT NULL
        );
    END IF;
    RETURN NEW;
EXCEPTION
    WHEN unique_violation THEN
        -- If there's a unique violation, just return NEW (user already exists)
        RETURN NEW;
    WHEN OTHERS THEN
        -- Log the error but don't fail the auth process
        RAISE WARNING 'Error creating user profile: %', SQLERRM;
        RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create the trigger
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- Create a function to ensure user profiles exist for existing users
CREATE OR REPLACE FUNCTION ensure_all_user_profiles()
RETURNS void AS $$
BEGIN
    -- Insert profiles for any existing auth users that don't have them
    INSERT INTO public.users (id, email, first_name, last_name, role, is_verified)
    SELECT 
        au.id,
        au.email,
        COALESCE(au.raw_user_meta_data->>'first_name', 'User'),
        COALESCE(au.raw_user_meta_data->>'last_name', ''),
        COALESCE(au.raw_user_meta_data->>'role', 'user'),
        au.email_confirmed_at IS NOT NULL
    FROM auth.users au
    LEFT JOIN public.users pu ON au.id = pu.id
    WHERE pu.id IS NULL
    ON CONFLICT (id) DO NOTHING;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Run the function to ensure all existing users have profiles
SELECT ensure_all_user_profiles();

-- Create a function to clean up orphaned agency records
CREATE OR REPLACE FUNCTION cleanup_orphaned_agencies()
RETURNS void AS $$
BEGIN
    -- Delete agency records that don't have corresponding users
    DELETE FROM public.agencies 
    WHERE user_id NOT IN (SELECT id FROM public.users);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Run the cleanup function
SELECT cleanup_orphaned_agencies();

-- Add better error handling for agency creation
CREATE OR REPLACE FUNCTION ensure_agency_user_exists()
RETURNS TRIGGER AS $$
BEGIN
    -- Ensure the user exists in the users table
    IF NOT EXISTS (SELECT 1 FROM public.users WHERE id = NEW.user_id) THEN
        RAISE EXCEPTION 'User with id % does not exist in users table', NEW.user_id;
    END IF;
    
    -- Ensure the user has agency role
    IF NOT EXISTS (SELECT 1 FROM public.users WHERE id = NEW.user_id AND role = 'agency') THEN
        RAISE EXCEPTION 'User with id % does not have agency role', NEW.user_id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for agency creation validation
DROP TRIGGER IF EXISTS ensure_agency_user_exists_trigger ON public.agencies;
CREATE TRIGGER ensure_agency_user_exists_trigger
    BEFORE INSERT ON public.agencies
    FOR EACH ROW EXECUTE FUNCTION ensure_agency_user_exists(); 