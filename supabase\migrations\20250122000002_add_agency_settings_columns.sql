-- Add settings columns to agencies table
ALTER TABLE public.agencies 
ADD COLUMN rental_terms TEXT,
ADD COLUMN whatsapp TEXT,
ADD COLUMN instagram TEXT,
ADD COLUMN facebook TEXT,
ADD COLUMN email_notifications BOOLEAN DEFAULT true,
ADD COLUMN sms_notifications BOOLEAN DEFAULT false,
ADD COLUMN review_notifications BOOLEAN DEFAULT true,
ADD COLUMN operating_hours_start TIME DEFAULT '08:00',
ADD COLUMN operating_hours_end TIME DEFAULT '18:00';

-- Add comments for documentation
COMMENT ON COLUMN public.agencies.rental_terms IS 'Agency specific rental terms and conditions';
COMMENT ON COLUMN public.agencies.whatsapp IS 'WhatsApp contact number';
COMMENT ON COLUMN public.agencies.instagram IS 'Instagram handle';
COMMENT ON COLUMN public.agencies.facebook IS 'Facebook page name';
COMMENT ON COLUMN public.agencies.email_notifications IS 'Enable email notifications for bookings';
COMMENT ON COLUMN public.agencies.sms_notifications IS 'Enable SMS notifications';
COMMENT ON COLUMN public.agencies.review_notifications IS 'Enable notifications for new reviews';
COMMENT ON COLUMN public.agencies.operating_hours_start IS 'Agency operating hours start time';
COMMENT ON COLUMN public.agencies.operating_hours_end IS 'Agency operating hours end time';
