"use client"

import { type ReactNode, useState } from "react"
import { cn } from "@/lib/utils"
import Link from "next/link"
import { usePathname, useSearchParams } from "next/navigation"
import { Button } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"
import { Menu, X } from "lucide-react"

interface SidebarNavProps {
  items: {
    href: string
    title: string
    icon: ReactNode
  }[]
}

export function SidebarNav({ items }: SidebarNavProps) {
  const pathname = usePathname()
  const searchParams = useSearchParams()
  const tab = searchParams.get("tab") || "cars"

  return (
    <nav className="grid gap-1">
      {items.map((item) => {
        // Determine if this item is active
        let isActive = false
        // Agency dashboard logic
        if (item.href === "/agency/dashboard" && (pathname === "/agency/dashboard" && (tab === "cars" || !searchParams.get("tab")))) {
          isActive = true
        } else if (item.href.startsWith("/agency/dashboard?tab=") && pathname === "/agency/dashboard" && item.href.includes(`tab=${tab}`)) {
          isActive = true
        }
        // User dashboard logic
        if (item.href === "/user/dashboard" && (pathname === "/user/dashboard" && (tab === "bookings" || !searchParams.get("tab")))) {
          isActive = true
        } else if (item.href.startsWith("/user/dashboard?tab=") && pathname === "/user/dashboard" && item.href.includes(`tab=${tab}`)) {
          isActive = true
        }
        return (
          <Link
            key={item.href}
            href={item.href}
            className={cn(
              "flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium transition-all hover:bg-accent",
              isActive ? "bg-primary text-white" : "text-muted-foreground"
            )}
          >
            {item.icon}
            {item.title}
          </Link>
        )
      })}
    </nav>
  )
}

interface DashboardLayoutProps {
  children: ReactNode
  sidebarNavItems: {
    href: string
    title: string
    icon: ReactNode
  }[]
  title: string
  rightPanel?: ReactNode
  showRightPanel?: boolean
}

export function DashboardLayout({
  children,
  sidebarNavItems,
  title,
  rightPanel,
  showRightPanel = false
}: DashboardLayoutProps) {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false)

  return (
    <div className="flex min-h-screen flex-col w-full">
      <div className={cn(
        "flex-1 items-start md:grid",
        showRightPanel
          ? "md:grid-cols-[220px_1fr_300px] lg:grid-cols-[240px_1fr_300px]"
          : "md:grid-cols-[220px_1fr] lg:grid-cols-[240px_1fr]"
      )}>
        {/* Left Sidebar */}
        <aside className="fixed top-14 z-30 hidden h-[calc(100vh-3.5rem)] w-full shrink-0 overflow-y-auto border-r md:sticky md:block">
          <ScrollArea className="py-6 pr-6 lg:py-8">
            <div className="px-4">
              <h2 className="mb-6 text-lg font-semibold">{title}</h2>
              <SidebarNav items={sidebarNavItems} />
            </div>
          </ScrollArea>
        </aside>

        {/* Main Content */}
        <main className="flex w-full flex-col overflow-hidden">
          <div className="md:hidden flex items-center justify-between p-4 border-b">
            <h1 className="text-lg font-semibold">{title}</h1>
            <Sheet open={isSidebarOpen} onOpenChange={setIsSidebarOpen}>
              <SheetTrigger asChild>
                <Button variant="outline" size="icon" className="md:hidden">
                  <Menu className="h-5 w-5" />
                  <span className="sr-only">Toggle Menu</span>
                </Button>
              </SheetTrigger>
              <SheetContent side="left" className="w-[240px] sm:w-[240px] pr-0">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-lg font-semibold">{title}</h2>
                  <Button variant="ghost" size="icon" onClick={() => setIsSidebarOpen(false)}>
                    <X className="h-5 w-5" />
                  </Button>
                </div>
                <SidebarNav items={sidebarNavItems} />
              </SheetContent>
            </Sheet>
          </div>
          <div className="flex-1 p-4 md:p-8">{children}</div>
        </main>

        {/* Right Panel */}
        {showRightPanel && (
          <aside className="hidden md:block border-l h-[calc(100vh-3.5rem)] overflow-y-auto">
            <ScrollArea className="h-full">
              <div className="p-6">
                {rightPanel}
              </div>
            </ScrollArea>
          </aside>
        )}
      </div>
    </div>
  )
}
