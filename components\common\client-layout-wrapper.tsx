"use client"

import { AuthProvider } from "@/contexts/auth-context"
import { Toaster } from "sonner"
import { CheckCircle, XCircle, AlertCircle, Info } from "lucide-react"

export default function ClientLayoutWrapper({ children }: { children: React.ReactNode }) {
  return (
    <AuthProvider>
      <Toaster
        position="top-center"
        expand={true}
        richColors={true}
        closeButton={true}
        toastOptions={{
          style: {
            background: 'white',
            border: '1px solid #e5e7eb',
            borderRadius: '12px',
            padding: '16px',
            fontSize: '14px',
            fontWeight: '500',
            boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
          },
          className: 'toast-custom',
        }}
        icons={{
          success: <CheckCircle className="h-5 w-5 text-green-500" />,
          error: <XCircle className="h-5 w-5 text-red-500" />,
          warning: <AlertCircle className="h-5 w-5 text-yellow-500" />,
          info: <Info className="h-5 w-5 text-blue-500" />,
        }}
      />
      {children}
    </AuthProvider>
  )
}