import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { NextResponse } from 'next/server'

export async function GET(request: Request) {
    const supabase = createRouteHandlerClient({ cookies })

    try {
        // Get current user
        const { data: { user } } = await supabase.auth.getUser()

        if (!user) {
            return NextResponse.json(
                { error: 'Unauthorized', success: false },
                { status: 401 }
            )
        }

        // Get user profile
        const { data, error } = await supabase
            .from('users')
            .select('*')
            .eq('id', user.id)
            .single()

        if (error) throw error

        return NextResponse.json({ data, success: true })
    } catch (error) {
        return NextResponse.json(
            { error: 'Failed to fetch user profile', success: false },
            { status: 500 }
        )
    }
}

export async function PUT(request: Request) {
    const supabase = createRouteHandlerClient({ cookies })

    try {
        const { data: { user } } = await supabase.auth.getUser()

        if (!user) {
            return NextResponse.json(
                { error: 'Unauthorized', success: false },
                { status: 401 }
            )
        }

        const body = await request.json()

        const { data, error } = await supabase
            .from('users')
            .update(body)
            .eq('id', user.id)
            .select()
            .single()

        if (error) throw error

        return NextResponse.json({ data, success: true })
    } catch (error) {
        return NextResponse.json(
            { error: 'Failed to update user profile', success: false },
            { status: 500 }
        )
    }
}