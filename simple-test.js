// Simple test with valid email format
// Run with: node simple-test.js

// Load environment variables from .env.local
require('dotenv').config({ path: '.env.local' })

const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://sagtwjbwgfgvzulnsmhc.supabase.co'
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseKey) {
    console.error('❌ NEXT_PUBLIC_SUPABASE_ANON_KEY not found')
    process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseKey)

async function testConnection() {
    console.log('🔍 Testing Supabase connection...')

    try {
        // Test basic connection
        const { data, error } = await supabase.from('users').select('count').limit(1)

        if (error) {
            console.error('❌ Connection failed:', error.message)
            return false
        }

        console.log('✅ Connection successful!')
        return true
    } catch (err) {
        console.error('❌ Connection error:', err.message)
        return false
    }
}

async function testSignup() {
    console.log('\n👤 Testing signup with valid email...')

    try {
        const testEmail = '<EMAIL>'
        const testPassword = 'TestPassword123!'

        console.log(`📝 Attempting to sign up: ${testEmail}`)

        const { data, error } = await supabase.auth.signUp({
            email: testEmail,
            password: testPassword,
            options: {
                data: {
                    first_name: 'Test',
                    last_name: 'User',
                    role: 'user'
                }
            }
        })

        if (error) {
            console.error('❌ Signup failed:', error.message)
            return false
        }

        console.log('✅ Signup successful!')
        console.log('User ID:', data.user?.id)

        // Test profile creation
        await new Promise(resolve => setTimeout(resolve, 2000))

        const { data: profile, error: profileError } = await supabase
            .from('users')
            .select('*')
            .eq('id', data.user.id)
            .single()

        if (profileError) {
            console.error('❌ Profile fetch failed:', profileError.message)
            return false
        }

        console.log('✅ User profile created!')
        console.log('Profile:', profile)

        // Clean up
        console.log('🧹 Cleaning up test user...')
        await supabase.auth.admin.deleteUser(data.user.id)
        console.log('✅ Test user deleted')

        return true
    } catch (err) {
        console.error('❌ Test error:', err.message)
        return false
    }
}

async function main() {
    console.log('🚀 Starting simple test...\n')

    const connectionOk = await testConnection()
    if (!connectionOk) {
        console.log('\n❌ Connection test failed.')
        process.exit(1)
    }

    const signupOk = await testSignup()
    if (!signupOk) {
        console.log('\n❌ Signup test failed.')
        process.exit(1)
    }

    console.log('\n🎉 All tests passed!')
}

main().catch(console.error) 