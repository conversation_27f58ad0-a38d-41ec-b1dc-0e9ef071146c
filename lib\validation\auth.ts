// Authentication validation helpers
import { z } from 'zod';

// Login validation schema
export const loginSchema = z.object({
    email: z
        .string()
        .min(1, 'Email is required')
        .email('Invalid email format'),
    password: z
        .string()
        .min(1, 'Password is required')
        .min(6, 'Password must be at least 6 characters'),
    rememberMe: z.boolean().optional(),
});

// Registration validation schema
export const registerSchema = z.object({
    email: z
        .string()
        .min(1, 'Email is required')
        .email('Invalid email format'),
    password: z
        .string()
        .min(1, 'Password is required')
        .min(8, 'Password must be at least 8 characters')
        .regex(
            /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
            'Password must contain at least one uppercase letter, one lowercase letter, and one number'
        ),
    confirmPassword: z
        .string()
        .min(1, 'Please confirm your password'),
    firstName: z
        .string()
        .min(1, 'First name is required')
        .min(2, 'First name must be at least 2 characters'),
    lastName: z
        .string()
        .min(1, 'Last name is required')
        .min(2, 'Last name must be at least 2 characters'),
    phone: z
        .string()
        .optional()
        .refine((val) => !val || /^\+?[\d\s\-\(\)]+$/.test(val), {
            message: 'Invalid phone number format',
        }),
    role: z.enum(['user', 'agency']).optional(),
    agencyName: z
        .string()
        .optional()
        .refine((val) => {
            // Agency name is required if role is agency
            return true; // This will be handled in the form validation
        }),
    agencyDescription: z
        .string()
        .optional()
        .refine((val) => !val || val.length <= 500, {
            message: 'Description must be less than 500 characters',
        }),
}).refine((data) => {
    if (data.role === 'agency' && !data.agencyName) {
        return false;
    }
    return true;
}, {
    message: 'Agency name is required for agency registration',
    path: ['agencyName'],
}).refine((data) => {
    return data.password === data.confirmPassword;
}, {
    message: 'Passwords do not match',
    path: ['confirmPassword'],
});

// Password reset request schema
export const passwordResetRequestSchema = z.object({
    email: z
        .string()
        .min(1, 'Email is required')
        .email('Invalid email format'),
});

// Password reset confirmation schema
export const passwordResetConfirmSchema = z.object({
    token: z
        .string()
        .min(1, 'Token is required'),
    newPassword: z
        .string()
        .min(1, 'New password is required')
        .min(8, 'Password must be at least 8 characters')
        .regex(
            /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
            'Password must contain at least one uppercase letter, one lowercase letter, and one number'
        ),
    confirmPassword: z
        .string()
        .min(1, 'Please confirm your password'),
}).refine((data) => {
    return data.newPassword === data.confirmPassword;
}, {
    message: 'Passwords do not match',
    path: ['confirmPassword'],
});

// Profile update schema
export const profileUpdateSchema = z.object({
    firstName: z
        .string()
        .min(1, 'First name is required')
        .min(2, 'First name must be at least 2 characters'),
    lastName: z
        .string()
        .min(1, 'Last name is required')
        .min(2, 'Last name must be at least 2 characters'),
    phone: z
        .string()
        .optional()
        .refine((val) => !val || /^\+?[\d\s\-\(\)]+$/.test(val), {
            message: 'Invalid phone number format',
        }),
    avatar: z
        .instanceof(File)
        .optional()
        .refine((file) => {
            if (!file) return true;
            const maxSize = 5 * 1024 * 1024; // 5MB
            return file.size <= maxSize;
        }, {
            message: 'File size must be less than 5MB',
        })
        .refine((file) => {
            if (!file) return true;
            const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
            return allowedTypes.includes(file.type);
        }, {
            message: 'File must be a valid image (JPEG, PNG, or WebP)',
        }),
});

// Change password schema
export const changePasswordSchema = z.object({
    currentPassword: z
        .string()
        .min(1, 'Current password is required'),
    newPassword: z
        .string()
        .min(1, 'New password is required')
        .min(8, 'Password must be at least 8 characters')
        .regex(
            /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
            'Password must contain at least one uppercase letter, one lowercase letter, and one number'
        ),
    confirmPassword: z
        .string()
        .min(1, 'Please confirm your password'),
}).refine((data) => {
    return data.newPassword === data.confirmPassword;
}, {
    message: 'Passwords do not match',
    path: ['confirmPassword'],
});

// Export types
export type LoginFormData = z.infer<typeof loginSchema>;
export type RegisterFormData = z.infer<typeof registerSchema>;
export type PasswordResetRequestData = z.infer<typeof passwordResetRequestSchema>;
export type PasswordResetConfirmData = z.infer<typeof passwordResetConfirmSchema>;
export type ProfileUpdateData = z.infer<typeof profileUpdateSchema>;
export type ChangePasswordData = z.infer<typeof changePasswordSchema>; 