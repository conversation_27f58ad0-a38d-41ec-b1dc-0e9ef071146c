"use client"

import { useRef, useEffect, useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  StarIcon,
  MapPin,
  Car,
  Calendar,
  Clock,
  CheckCircle,
  Award,
  ShieldCheck,
  Phone,
  Mail,
  Instagram,
  Facebook,
  MessageCircle,
  FileText,
  Filter,
  Users,
} from "lucide-react"
import { FeaturedCarCard } from "@/components/features/car/featured-car-card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

export default function AgencyDetailsPage() {
  const contactSectionRef = useRef<HTMLDivElement>(null)
  const [contractUrl, setContractUrl] = useState("")
  const [cars, setCars] = useState([
    {
      id: "CAR-AGY-001-001",
      make: "Toyota",
      model: "Corolla",
      year: 2022,
      transmission: "automatic",
      price: 750,
      mileage: 15000,
      insuranceType: "comprehensive",
      deposit: 5000,
      color: "Red",
    },
    {
      id: "CAR-AGY-001-002",
      make: "Honda",
      model: "Civic",
      year: 2023,
      transmission: "automatic",
      price: 800,
      mileage: 8000,
      insuranceType: "premium",
      deposit: 6000,
      color: "Blue",
    },
    {
      id: "CAR-AGY-001-003",
      make: "Mercedes",
      model: "C-Class",
      year: 2021,
      transmission: "automatic",
      price: 1500,
      mileage: 20000,
      insuranceType: "premium",
      deposit: 10000,
      color: "Black",
    },
    {
      id: "CAR-AGY-001-004",
      make: "BMW",
      model: "3 Series",
      year: 2022,
      transmission: "automatic",
      price: 1200,
      mileage: 12000,
      insuranceType: "premium",
      deposit: 8000,
      color: "White",
    },
    {
      id: "CAR-AGY-001-005",
      make: "Tesla",
      model: "Model 3",
      year: 2023,
      transmission: "automatic",
      price: 1800,
      mileage: 5000,
      insuranceType: "comprehensive",
      deposit: 12000,
      color: "Silver",
    },
    {
      id: "CAR-AGY-001-006",
      make: "Audi",
      model: "A4",
      year: 2021,
      transmission: "automatic",
      price: 1100,
      mileage: 18000,
      insuranceType: "premium",
      deposit: 9000,
      color: "Gray",
    },
  ])
  const [reviews, setReviews] = useState([
    {
      id: "review-1",
      customerName: "John Doe",
      customerAvatar: "/placeholder.svg?height=40&width=40",
      rating: 5,
      date: "March 2025",
      comment: "Premium Auto Rentals provided exceptional service from start to finish. The Tesla Model 3 I rented was spotless and fully charged. Their staff was professional and accommodating. I highly recommend this agency!",
      status: "approved",
    },
    {
      id: "review-2",
      customerName: "Sarah Johnson",
      customerAvatar: "/placeholder.svg?height=40&width=40",
      rating: 5,
      date: "February 2025",
      comment: "I rented the BMW 3 Series for a business trip and it was perfect. The car was delivered on time and the pick-up process was smooth. Premium Auto Rentals has great customer service and a fantastic selection of vehicles.",
      status: "approved",
    },
    {
      id: "review-3",
      customerName: "Michael Rodriguez",
      customerAvatar: "/placeholder.svg?height=40&width=40",
      rating: 4,
      date: "January 2025",
      comment: "I rented the RAV4 for a weekend trip with my family. The SUV was spacious and comfortable for our luggage and kids. Premium Auto Rentals made the entire process easy and stress-free. We'll definitely be using them again!",
      status: "approved",
    },
    {
      id: "review-4",
      customerName: "Emily Chen",
      customerAvatar: "/placeholder.svg?height=40&width=40",
      rating: 5,
      date: "December 2024",
      comment: "Excellent experience with Premium Auto Rentals. The Mercedes C-Class was in perfect condition and the service was top-notch. Will definitely rent from them again on my next visit to Morocco.",
      status: "approved",
    },
    {
      id: "review-5",
      customerName: "David Wilson",
      customerAvatar: "/placeholder.svg?height=40&width=40",
      rating: 4,
      date: "November 2024",
      comment: "Great selection of cars and very professional staff. The Toyota Corolla was perfect for my needs. The only minor issue was a slight delay in pickup, but they compensated with excellent service.",
      status: "approved",
    },
  ])

  useEffect(() => {
    const url = localStorage.getItem("agencyContractUrl")
    if (url) setContractUrl(url)
  }, [])

  const scrollToContact = () => {
    contactSectionRef.current?.scrollIntoView({ behavior: "smooth" })
  }

  return (
    <div className="container mx-auto px-4 py-4 sm:py-8">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-orange-600 via-yelow-600 to-purple-600 rounded-2xl p-4 sm:p-8 mb-6 sm:mb-8 text-white">
        <div className="flex flex-col md:flex-row gap-4 sm:gap-6 md:items-center">
          <Avatar className="h-16 w-16 sm:h-24 sm:w-24 border-4 border-white shadow-lg ring-2 ring-white/20 mx-auto md:mx-0">
            <AvatarImage src="/placeholder.svg?height=100&width=100" />
            <AvatarFallback className="text-white font-bold bg-white/20 text-lg sm:text-2xl">PAR</AvatarFallback>
          </Avatar>
          <div className="space-y-2 sm:space-y-3 flex-1 text-center md:text-left">
            <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold">Morocco Premium Rentals</h1>
            <div className="flex flex-wrap items-center gap-2 sm:gap-4 justify-center md:justify-start">
              <Badge variant="secondary" className="bg-white/20 text-white border-white/30 text-xs sm:text-sm">
                <StarIcon className="h-3 w-3 sm:h-4 sm:w-4 fill-current text-yellow-300 mr-1" />
                <span>4.9 (1243 reviews)</span>
              </Badge>
              <div className="flex items-center text-blue-100 text-xs sm:text-sm">
                <MapPin className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                <span>Multiple locations in Morocco</span>
              </div>
              <Badge variant="secondary" className="bg-white/20 text-white border-white/30 text-xs sm:text-sm">
                <Car className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
                <span>145 cars</span>
              </Badge>
              <Badge variant="secondary" className="bg-white/20 text-white border-white/30 text-xs sm:text-sm">
                <Calendar className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
                <span>Member since 2020</span>
              </Badge>
              <Badge variant="secondary" className="bg-green-400/20 text-green-100 border-green-300/30 text-xs sm:text-sm">
                <CheckCircle className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
                <span>Verified Agency</span>
              </Badge>
            </div>
            <p className="text-blue-100 text-sm sm:text-lg max-w-3xl">
              Specializing in providing high-quality car rental experiences with a diverse fleet of vehicles ranging from economy to luxury.
            </p>
          </div>
          <div className="flex flex-col gap-3">
            <Button
              className="bg-white text-blue-600 hover:bg-blue-50 font-semibold text-sm sm:text-base"
              onClick={scrollToContact}
            >
              Contact Agency
            </Button>
          </div>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-3 sm:gap-6 mb-6 sm:mb-8">
        <Card className="text-center p-3 sm:p-6 bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-200">
          <div className="flex items-center justify-center mb-2">
            <Car className="h-6 w-6 sm:h-8 sm:w-8 text-blue-600" />
          </div>
          <h3 className="text-lg sm:text-2xl font-bold text-gray-900">145</h3>
          <p className="text-xs sm:text-sm text-gray-600">Available Cars</p>
        </Card>
        <Card className="text-center p-3 sm:p-6 bg-gradient-to-br from-green-50 to-emerald-50 border-green-200">
          <div className="flex items-center justify-center mb-2">
            <StarIcon className="h-6 w-6 sm:h-8 sm:w-8 text-green-600 fill-current" />
          </div>
          <h3 className="text-lg sm:text-2xl font-bold text-gray-900">4.9</h3>
          <p className="text-xs sm:text-sm text-gray-600">Average Rating</p>
        </Card>
        <Card className="text-center p-3 sm:p-6 bg-gradient-to-br from-purple-50 to-violet-50 border-purple-200">
          <div className="flex items-center justify-center mb-2">
            <Users className="h-6 w-6 sm:h-8 sm:w-8 text-purple-600" />
          </div>
          <h3 className="text-lg sm:text-2xl font-bold text-gray-900">1,243</h3>
          <p className="text-xs sm:text-sm text-gray-600">Happy Customers</p>
        </Card>
        <Card className="text-center p-3 sm:p-6 bg-gradient-to-br from-amber-50 to-orange-50 border-amber-200">
          <div className="flex items-center justify-center mb-2">
            <Award className="h-6 w-6 sm:h-8 sm:w-8 text-amber-600" />
          </div>
          <h3 className="text-lg sm:text-2xl font-bold text-gray-900">5+</h3>
          <p className="text-xs sm:text-sm text-gray-600">Years Experience</p>
        </Card>
      </div>

      <Tabs defaultValue="about" className="space-y-4 sm:space-y-6">
        <TabsList className="flex w-full flex-wrap gap-2 bg-white border-2 border-gray-300 p-2 rounded-xl shadow-lg justify-between">
          <TabsTrigger
            value="about"
            className="flex-1 min-w-[120px] data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-md transition-all duration-200 font-bold py-2 text-xs sm:text-sm"
          >
            <FileText className="h-4 w-4 mr-2" />
            About
          </TabsTrigger>
          <TabsTrigger
            value="cars"
            className="flex-1 min-w-[120px] data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-md transition-all duration-200 font-bold py-2 text-xs sm:text-sm"
          >
            <Car className="h-4 w-4 mr-2" />
            Cars ({cars.length})
          </TabsTrigger>
          <TabsTrigger
            value="reviews"
            className="flex-1 min-w-[120px] data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-md transition-all duration-200 font-semibold py-2 text-xs sm:text-sm"
          >
            <StarIcon className="h-4 w-4 mr-2" />
            Reviews ({reviews.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="about" className="space-y-4 sm:space-y-6">
          <Card className="border-0 shadow-lg">
            <CardContent className="p-4 sm:p-8">
              <div className="space-y-6 sm:space-y-8">
                <div>
                  <h2 className="text-xl sm:text-2xl font-bold text-gray-900 mb-3 sm:mb-4">About Morocco Premium Rentals</h2>
                  <p className="text-gray-600 leading-relaxed text-sm sm:text-lg">
                    Morocco Premium Rentals specializes in providing high-quality car rental experiences with a diverse
                    fleet of vehicles ranging from economy to luxury. With 10 locations across major Moroccan cities, we
                    pride ourselves on exceptional customer service and well-maintained vehicles.
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 sm:gap-6">
                  <Card className="text-center p-4 sm:p-6 bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-200">
                    <Clock className="h-8 w-8 sm:h-12 sm:w-12 text-blue-600 mx-auto mb-3 sm:mb-4" />
                    <h3 className="font-bold text-base sm:text-lg mb-2">Fast Response</h3>
                    <p className="text-gray-600 text-sm">Average response time under 30 minutes</p>
                  </Card>
                  <Card className="text-center p-4 sm:p-6 bg-gradient-to-br from-green-50 to-emerald-50 border-green-200">
                    <Award className="h-8 w-8 sm:h-12 sm:w-12 text-green-600 mx-auto mb-3 sm:mb-4" />
                    <h3 className="font-bold text-base sm:text-lg mb-2">Top Rated</h3>
                    <p className="text-gray-600 text-sm">Among the highest rated rental agencies</p>
                  </Card>
                  <Card className="text-center p-4 sm:p-6 bg-gradient-to-br from-purple-50 to-violet-50 border-purple-200">
                    <ShieldCheck className="h-8 w-8 sm:h-12 sm:w-12 text-purple-600 mx-auto mb-3 sm:mb-4" />
                    <h3 className="font-bold text-base sm:text-lg mb-2">100% Insured</h3>
                    <p className="text-gray-600 text-sm">Full insurance coverage on all vehicles</p>
                  </Card>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 sm:gap-8">
                  <div>
                    <h3 className="text-lg sm:text-xl font-bold text-gray-900 mb-3 sm:mb-4">Rental Policy</h3>
                    <ul className="space-y-2 sm:space-y-3 text-gray-600 text-sm sm:text-base">
                      <li className="flex items-start gap-2 sm:gap-3">
                        <CheckCircle className="h-4 w-4 sm:h-5 sm:w-5 text-green-600 mt-0.5 flex-shrink-0" />
                        <span>Minimum rental period: 1 day</span>
                      </li>
                      <li className="flex items-start gap-2 sm:gap-3">
                        <CheckCircle className="h-4 w-4 sm:h-5 sm:w-5 text-green-600 mt-0.5 flex-shrink-0" />
                        <span>Drivers must be 21+ years old with valid driver's license</span>
                      </li>
                      <li className="flex items-start gap-2 sm:gap-3">
                        <CheckCircle className="h-4 w-4 sm:h-5 sm:w-5 text-green-600 mt-0.5 flex-shrink-0" />
                        <span>Security deposit required (varies by vehicle)</span>
                      </li>
                      <li className="flex items-start gap-2 sm:gap-3">
                        <CheckCircle className="h-4 w-4 sm:h-5 sm:w-5 text-green-600 mt-0.5 flex-shrink-0" />
                        <span>Free cancellation up to 48 hours before pickup</span>
                      </li>
                      <li className="flex items-start gap-2 sm:gap-3">
                        <CheckCircle className="h-4 w-4 sm:h-5 sm:w-5 text-green-600 mt-0.5 flex-shrink-0" />
                        <span>All cars delivered with full tank and should be returned the same</span>
                      </li>
                    </ul>
                  </div>

                  <div>
                    <h3 className="text-lg sm:text-xl font-bold text-gray-900 mb-3 sm:mb-4">Locations</h3>
                    <div className="space-y-2 sm:space-y-3">
                      <div className="p-3 sm:p-4 border border-gray-200 rounded-lg bg-gray-50">
                        <h4 className="font-semibold text-gray-900 text-sm sm:text-base">Marrakech</h4>
                        <p className="text-xs sm:text-sm text-gray-600">Avenue Mohammed V, Marrakech</p>
                      </div>
                      <div className="p-3 sm:p-4 border border-gray-200 rounded-lg bg-gray-50">
                        <h4 className="font-semibold text-gray-900 text-sm sm:text-base">Casablanca</h4>
                        <p className="text-xs sm:text-sm text-gray-600">Boulevard d'Anfa, Casablanca</p>
                      </div>
                      <div className="p-3 sm:p-4 border border-gray-200 rounded-lg bg-gray-50">
                        <h4 className="font-semibold text-gray-900 text-sm sm:text-base">Rabat</h4>
                        <p className="text-xs sm:text-sm text-gray-600">Avenue Fal Ould Oumeir, Rabat</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-blue-50 p-4 sm:p-6 rounded-xl border border-blue-200">
                  <h3 className="text-base sm:text-lg font-bold text-gray-900 mb-2 sm:mb-3">Agency Contract</h3>
                  {contractUrl ? (
                    <a
                      href={contractUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-700 font-medium text-sm sm:text-base"
                    >
                      <FileText className="h-3 w-3 sm:h-4 sm:w-4" />
                      View Contract
                    </a>
                  ) : (
                    <span className="text-gray-600 text-sm sm:text-base">No contract uploaded yet.</span>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="cars" className="space-y-4 sm:space-y-6">
          <Card className="border-0 shadow-lg">
            <CardContent className="p-4 sm:p-8">
              <div className="space-y-4 sm:space-y-6">
                <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3 sm:gap-0">
                  <h2 className="text-xl sm:text-2xl font-bold text-gray-900">Available Cars</h2>
                  <div className="flex flex-col sm:flex-row gap-2 sm:gap-3">
                    <Button variant="outline" size="sm" className="text-xs sm:text-sm">
                      <Filter className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                      Filter
                    </Button>
                    <Select defaultValue="all">
                      <SelectTrigger className="w-full sm:w-48 text-xs sm:text-sm">
                        <SelectValue placeholder="All Categories" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Categories</SelectItem>
                        <SelectItem value="economy">Economy</SelectItem>
                        <SelectItem value="suv">SUV</SelectItem>
                        <SelectItem value="luxury">Luxury</SelectItem>
                        <SelectItem value="electric">Electric</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
                  {cars.map((car) => (
                    <FeaturedCarCard
                      key={car.id}
                      id={car.id}
                      title={`${car.brand} ${car.model}`}
                      price={car.daily_rate}
                      location="Medina, Marrakech"
                      images={["/placeholder.svg?height=400&width=600"]}
                      rating={4.9}
                      reviews={234}
                      category={car.transmission}
                    />
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="reviews" className="space-y-4 sm:space-y-6">
          <Card className="border-0 shadow-lg">
            <CardContent className="p-4 sm:p-8">
              <div className="space-y-4 sm:space-y-6">
                <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2 sm:gap-0">
                  <h2 className="text-xl sm:text-2xl font-bold text-gray-900">Customer Reviews</h2>
                  <div className="flex items-center gap-2">
                    <StarIcon className="h-4 w-4 sm:h-5 sm:w-5 fill-current text-yellow-500" />
                    <span className="text-base sm:text-lg font-semibold">4.9</span>
                    <span className="text-gray-600 text-sm sm:text-base">({reviews.length} reviews)</span>
                  </div>
                </div>

                <div className="space-y-4 sm:space-y-6">
                  {reviews.map((review) => (
                    <div key={review.id} className="border border-gray-200 rounded-lg p-4 sm:p-6">
                      <div className="flex items-start gap-3 sm:gap-4">
                        <Avatar className="h-10 w-10 sm:h-12 sm:w-12">
                          <AvatarImage src={review.customerAvatar} />
                          <AvatarFallback>{review.customerName.substring(0, 2)}</AvatarFallback>
                        </Avatar>
                        <div className="flex-1">
                          <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2 mb-2">
                            <h4 className="font-semibold text-gray-900 text-sm sm:text-base">{review.customerName}</h4>
                            <div className="flex items-center gap-1">
                              {[...Array(5)].map((_, i) => (
                                <StarIcon
                                  key={i}
                                  className={`h-3 w-3 sm:h-4 sm:w-4 ${i < review.rating ? 'fill-current text-yellow-500' : 'text-gray-300'
                                    }`}
                                />
                              ))}
                            </div>
                            <Badge variant="secondary" className="ml-auto text-xs sm:text-sm">
                              {review.status}
                            </Badge>
                          </div>
                          <p className="text-gray-600 mb-2">{review.comment}</p>
                          <p className="text-sm text-gray-500">{review.date}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <div ref={contactSectionRef} id="contact-section" className="mt-12 pt-8 border-t">
        <h2 className="text-2xl font-bold mb-6">Contact Us</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <Card>
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold mb-4">Contact Information</h3>
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <div className="bg-primary/10 p-2 rounded-full">
                    <Phone className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Phone</p>
                    <p className="font-medium">+212 524 123 456</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <div className="bg-primary/10 p-2 rounded-full">
                    <Mail className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Email</p>
                    <p className="font-medium"><EMAIL></p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <div className="bg-primary/10 p-2 rounded-full">
                    <MessageCircle className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">WhatsApp</p>
                    <p className="font-medium">+212 661 234 567</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <div className="bg-primary/10 p-2 rounded-full">
                    <Instagram className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Instagram</p>
                    <p className="font-medium">@moroccopremiumrentals</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <div className="bg-primary/10 p-2 rounded-full">
                    <Facebook className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Facebook</p>
                    <p className="font-medium">MoroccoPremiumRentals</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold mb-4">Send Us a Message</h3>
              <form className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Name</Label>
                    <Input id="name" placeholder="Your name" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <Input id="email" type="email" placeholder="Your email" />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="subject">Subject</Label>
                  <Input id="subject" placeholder="Subject" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="message">Message</Label>
                  <Textarea id="message" className="min-h-[120px]" placeholder="Your message" />
                </div>
                <Button className="w-full bg-gradient-to-r from-primary to-secondary hover:opacity-90">
                  Send Message
                </Button>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
